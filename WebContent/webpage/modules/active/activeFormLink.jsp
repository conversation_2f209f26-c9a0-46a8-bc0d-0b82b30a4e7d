<%@ taglib prefix="from" uri="http://www.springframework.org/tags/form" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>

    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <%-- switch --%>
    <link rel="stylesheet" href="${ctxStatic}/bootstrap/3.3.4/css/bootstrap-switch.min.css">
    <script src="${ctxStatic}/bootstrap/3.3.4/js/bootstrap-switch.min.js"></script>
    <!-- SUMMERNOTE -->
    <link href="${ctxStatic}/summernote/summernote.css" rel="stylesheet">
    <link href="${ctxStatic}/summernote/summernote-bs3.css" rel="stylesheet">

    <link rel="stylesheet" type="text/css" href="${ctxStatic}/webuploader-0.1.5/webuploader.css">
    <link rel="stylesheet" type="text/css" href="${ctxStatic}/webuploader-0.1.5/demo.css">

    <script src="${ctxStatic}/summernote/summernote.min.js"></script>
    <script src="${ctxStatic}/summernote/summernote-zh-CN.js"></script>

    <script type="text/javascript">
        // 添加全局站点信息
        var BASE_URL = '/webuploader';
    </script>
    <script type="text/javascript" src="${ctxStatic}/webuploader-0.1.5/webuploader.js"></script>
    <style>
        #uploader .queueList {
            margin: 0px;
            border: none;
        }
        #uploader .webuploader-pick{
            padding: 0px;
        }
        .input-group-addon, .input-group-btn{
            width: 100%;
        }
        #uploader .queueList.webuploader-dnd-over {
            border: none;
        }
        .btn-default{
            color: #333 !important;
            background-color: #fff !important;
            border-color: #ccc !important;
        }
    </style>
</head>

<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="row">

        <div class="col-sm-12 animated fadeInRight">
            <div class="mail-box">
                <div class="mail-body">
                    <form:form modelAttribute="active" id="inputForm" action="${ctx}/qqc/active/save" method="post" class="form-horizontal">
                        <from:hidden path="addType" />
                        <div class="form-group">
                            <form:hidden path="id"/>
                            <label class="col-sm-2 control-label"><font color="red">*</font>活动标题：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${active.title}" id="title" name="title"  class="form-control required" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>活动链接：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${active.url}" id="url" name="url"  class="form-control required" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>活动类型：</label>
                            <div class="col-sm-8">
                                <form:select style="width:150px;" path="type"  class="form-control required m-b">
                                    <form:option value="" label=""/>
                                    <form:options items="${fns:getDictList('qqc_active_type')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
                                </form:select>
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>活动时间：</label>
                            <div class="col-sm-8">
                                <input autocomplete="off" id="beginDate" style="cursor: pointer" name="beginTime" type="text" maxlength="20" class="required laydate-icon form-control layer-date input-sm"
                                       value="${active.beginTime}"/>
                                <label>&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label><input autocomplete="off" style="cursor: pointer" id="endDate" name="endTime" type="text" maxlength="20" class="required laydate-icon form-control layer-date input-sm"
                                                                                            value="${active.endTime}" />&nbsp;&nbsp;
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>报名时间：</label>
                            <div class="col-sm-8">
                                <input autocomplete="off" id="apllyBeginDate" style="cursor: pointer" name="apllyBeginTime" type="text" maxlength="20" class="required laydate-icon form-control layer-date input-sm"
                                       value="${active.apllyBeginTime}"/>
                                <label>&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label><input autocomplete="off" style="cursor: pointer" id="apllyEndDate" name="apllyEndTime" type="text" maxlength="20" class="required laydate-icon form-control layer-date input-sm"
                                                                                           value="${active.apllyEndTime}" />&nbsp;&nbsp;
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>活动地点：</label>
                            <div class="col-sm-2">
                                <sys:treeSelectMulti cssStyle="width:180px" id="area" name="areaId" value="${active.areaId}" labelName="areaName" labelValue="${active.areaName}"
                                                title="区域" url="/sys/area/treeData" extId="" cssClass="form-control required m-s" allowClear="true"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">详细地址：</label>
                            <div class="col-sm-8">
                                <input type="text" placeholder="详细地址,不少于四个字" value="${active.address}" id="address" name="address"  class="form-control" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">主办方：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${active.sponsor}" id="sponsor" name="sponsor"  class="form-control" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">活动费用：</label>
                            <div class="col-sm-3">
                                <input type="text" value="${active.cost}" id="cost" name="cost"  class="form-control" />
                            </div>
                            <label class="col-sm-2 control-label">活动积分：</label>
                            <div class="col-sm-3">
                                <input type="text" value="${active.integral}" id="integral" name="integral"  class="form-control" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">活动海报：</label>
                            <%-- <input style="display: none" name="img" id="imgurl" value="${active.img}"> --%>
                            <div class="col-sm-8">
                                <%-- <div id="uploader" class="wu-example">
                                    <div class="queueList">
                                        <div id="dndArea" class="img">
                                            <div id="filePicker">
                                                <c:if test="${active.img != null}">
                                                    <img style="width: 110px;height: 110px" src="${active.img}">
                                                </c:if>
                                                <c:if test="${active.img == null}">
                                                    <img style="width: 110px;height: 110px" src="${ctxStatic}/img/addpic.jpg">
                                                </c:if>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <font color="red">(注：如以上传图片可以点击图片重新上传)</font> --%>
                                <form:hidden  path="img" htmlEscape="false"  maxlength="255" class="form-control "/>
						        <sys:ckfinder input="img" type="images" uploadPath="/qqchuang/ative"  selectMultiple="false" enableJcrop="true" />
                            </div>
                        </div>
                    </form:form>
                </div>
            </div>
        </div>

    </div>
</div>
<script type="text/javascript">
    var validateForm;
    var enumType = {
        "yes":"1",
        "no":"2"
    }
    function doSubmit(){//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
        if(validateForm.form()){
            $("#inputForm").submit();
            return true;
        }
        return false;
    }

    $(document).ready(function () {
        $("#summernote").summernote({
            height: 400,
            minHeight: 300,
            maxHeight: 500,
            lang:'zh-CN',
        });
        //外部js调用
        //外部js调用
        laydate({
            elem: '#beginDate',
            format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
            festival: true, //显示节日
            istime: false, //是否开启时间选择<br/>
            isclear: true, //是否显示清空<br/>
            istoday: true, //是否显示今天<br/>
            choose: function(datas){ //选择日期完毕的回调
                $("#beginDate").val(datas+" 00:00:00")
            }
        });
        laydate({
            elem: '#endDate',
            format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
            festival: true, //显示节日
            istime: false, //是否开启时间选择<br/>
            isclear: true, //是否显示清空<br/>
            istoday: true, //是否显示今天<br/>
            choose: function(datas){ //选择日期完毕的回调
                $("#endDate").val(datas+" 23:59:59")
            }
        });
        laydate({
            elem: '#apllyBeginDate',
            format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
            festival: true, //显示节日
            istime: false, //是否开启时间选择<br/>
            isclear: true, //是否显示清空<br/>
            istoday: true, //是否显示今天<br/>
            choose: function(datas){ //选择日期完毕的回调
                $("#apllyBeginDate").val(datas+" 00:00:00")
            }
        });
        laydate({
            elem: '#apllyEndDate',
            format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
            festival: true, //显示节日
            istime: false, //是否开启时间选择<br/>
            isclear: true, //是否显示清空<br/>
            istoday: true, //是否显示今天<br/>
            choose: function(datas){ //选择日期完毕的回调
                $("#apllyEndDate").val(datas+" 23:59:59")
            }
        });
        //开关
       if($("#accIs").val() == enumType.yes){
            $('#acc_is input').bootstrapSwitch('state',true,false);
        }else{
            $('#acc_is input').bootstrapSwitch('state',false,true);
        }
        if($("#isReview").val() == enumType.yes){
            $('#review_is input').bootstrapSwitch('state',true,false);
        }else{
            $('#review_is input').bootstrapSwitch('state',false,true);
        }
        if($("#displayUserIs").val() == enumType.yes){
            $('#display_user input').bootstrapSwitch('state',true,false);
        }else{
            $('#display_user input').bootstrapSwitch('state',false,true);
        }
        validateForm = $("#inputForm").validate({
            submitHandler: function(form){
                var str= $('#summernote').code();
                $("#content").val(str);
                if($("#numberIs").val() == "1"){
                    $("#numberLimit").val("");
                }
                if($("#checkType").val() == "1"){
                    $("#adminPhone").val("");
                }
                loading('正在提交，请稍等...');
                form.submit();
            },
            errorContainer: "#messageBox",
            errorPlacement: function(error, element) {
                $("#messageBox").text("输入有误，请先更正。");
                if (element.is(".layer-date")){
                    $(this).val(error);
                }else if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
                    error.appendTo(element.parent().parent());
                } else {
                    error.insertAfter(element);
                }
            }
        });
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

    });



    $('#acc_is input').bootstrapSwitch({
        onText:'是',
        offText:'否',
        onSwitchChange:function(event,state){
            if(state==true){
                $("#accIs").val(enumType.yes)
            }else{
                $("#accIs").val(enumType.noe)
            }
        }
    });
    $('#review_is input').bootstrapSwitch({
        onText:'是',
        offText:'否' ,
        onSwitchChange:function(event,state){
            if(state==true){
                $("#isReview").val(enumType.yes);
            }else{
                $("#isReview").val(enumType.no);
            }
        }
    });
    $('#display_user input').bootstrapSwitch({
        onText:'是',
        offText:'否' ,
        onSwitchChange:function(event,state){
            if(state==true){
                $("#displayUserIs").val(enumType.yes);
            }else{
                $("#displayUserIs").val(enumType.no);
            }
        }
    });

    $("#number_is li").click(function(){
        $("#number_is_text").html($(this).children("a").html());
        if($(this).attr("value") == "2"){
            $("#numberLimitDiv").removeAttr("hidden");
        }else{
            $("#numberLimitDiv").attr("hidden","hidden");
        }
        $("#numberIs").val($(this).attr("value"))
    })

    $("#checkType_is li").click(function(){
        $("#checkType_is_text").html($(this).children("a").html());
        if($(this).attr("value") == "2"){
            $("#adminPhoneDiv").removeAttr("hidden");
        }else{
            $("#adminPhoneDiv").attr("hidden","hidden");
        }
        $("#checkType").val($(this).attr("value"))
    })
</script>
<script type="text/javascript">
    jQuery(function() {
        var $ = jQuery,    // just in case. Make sure it's not an other libaray.

            $wrap = $('#uploader'),
            // 图片容器
            $queue = $('<ul class="filelist"></ul>')
                .appendTo( $wrap.find('.queueList') ),


            // 没选择文件之前的内容。
            $img = $wrap.find('.img'),

            // 优化retina, 在retina下这个值是2
            ratio = window.devicePixelRatio || 1,

            // 缩略图大小
            thumbnailWidth = 110 * ratio,
            thumbnailHeight = 110 * ratio,

            // 所有文件的进度信息，key为file id
            percentages = {},

            //图片选择初始化
            supportTransition = (function(){
                var s = document.createElement('p').style,
                    r = 'transition' in s ||
                        'WebkitTransition' in s ||
                        'MozTransition' in s ||
                        'msTransition' in s ||
                        'OTransition' in s;
                s = null;
                return r;
            })(),

            // WebUploader实例
            uploader;
        if ( !WebUploader.Uploader.support() ) {
            alert( 'Web Uploader 不支持您的浏览器！如果你使用的是IE浏览器，请尝试升级 flash 播放器');
            throw new Error( 'WebUploader does not support the browser you are using.' );
        }

        // 实例化
        uploader = WebUploader.create({
            pick: {
                id: '#filePicker',
            },
            dnd: '#uploader .queueList',
            paste: document.body,

            accept: {
                title: 'Images',
                extensions: 'gif,jpg,jpeg,bmp,png',
                mimeTypes: 'image/*'
            },
            // swf文件路径
            swf: BASE_URL + '/js/Uploader.swf',
            disableGlobalDnd: true,

            chunked: true,
            server: '${ctx}/qqc/active/imageUpload',
            fileNumLimit: 1,//一次最多上传多少张照片
        });

        // 当有文件添加进来时执行，负责view的创建
        function addFile( file ) {
            var $li = $( '<li id="' + file.id + '">' +
                '<p class="imgWrap"></p>'+
                '<p style="display:none;" class="title">' + file.name + '</p>' +
                '</li>' ),
                $btns = $('<div class="file-panel">' +
                    '<span title="删除" class="cancel">删除</span>' +
                    '<span title="向右旋转" class="rotateRight">向右旋转</span>' +
                    '<span title="向左旋转" class="rotateLeft">向左旋转</span></div>').appendTo( $li ),
                $wrap = $li.find( 'p.imgWrap' ),
                showError = function( code ) {
                    switch( code ) {
                        case 'exceed_size':
                            text = '文件大小超出';
                            break;
                        case 'interrupt':
                            text = '上传暂停';
                            break;
                        case 'Q_TYPE_DENIED':
                            text = '不支持该格式';
                            break;
                        default:
                            text = '上传失败，请重试';
                            break;
                    }
                };
            if ( file.getStatus() === 'invalid' ) {
                showError( file.statusText );
            } else {
                // @todo lazyload
                $wrap.text( '预览中' );
                uploader.makeThumb( file, function( error, src ) {
                    if ( error ) {
                        $wrap.text( '不能预览' );
                        return;
                    }
                    var img = $('<img src="'+src+'">');
                    $wrap.empty().append( img );
                }, thumbnailWidth, thumbnailHeight );
                percentages[ file.id ] = [ file.size, 0 ];
                file.rotation = 0;
            }

            $li.on( 'mouseenter', function() {
                $btns.stop().animate({height: 30});
            });

            $li.on( 'mouseleave', function() {
                $btns.stop().animate({height: 0});
            });

            $btns.on( 'click', 'span', function() {
                var index = $(this).index(),
                    deg;
                switch ( index ) {
                    case 0:
                        uploader.removeFile( file );
                        return;
                    case 1:
                        file.rotation += 90;
                        break;
                    case 2:
                        file.rotation -= 90;
                        break;
                }
                if ( supportTransition ) {
                    deg = 'rotate(' + file.rotation + 'deg)';
                    $wrap.css({
                        '-webkit-transform': deg,
                        '-mos-transform': deg,
                        '-o-transform': deg,
                        'transform': deg
                    });
                } else {
                    $wrap.css( 'filter', 'progid:DXImageTransform.Microsoft.BasicImage(rotation='+ (~~((file.rotation/90)%4 + 4)%4) +')');
                }
            });
            $li.appendTo( $queue );
        }
        // 负责view的销毁
        function removeFile( file ) {
            var $li = $('#'+file.id);
            delete percentages[ file.id ];
            $li.off().find('.file-panel').off().end().remove();
        }
        //图片添加
        uploader.onFileQueued = function( file ) {
            $img.addClass( 'element-invisible' );
            uploader.upload();
        };

        //图片添加成功后回显
        uploader.on('uploadSuccess', function (file, response) {
            var imgurl = response._raw; //上传图片的路径
            addFile(file);
            $("#imgurl").val(imgurl);
        });

        //图片删除
        uploader.onFileDequeued = function( file ) {
            $img.removeClass( 'element-invisible' );
            removeFile( file );
        };

        //图片上传错误信息
        uploader.onError = function( code ) {
            var text;
            switch( code ) {
                case 'exceed_size':
                    text = '文件大小超出';
                    break;
                case 'interrupt':
                    text = '上传暂停';
                    break;
                case 'Q_TYPE_DENIED':
                    text = '不支持该格式';
                    break;
                default:
                    text = '上传失败，请重试';
                    break;
            }
            alert("Eroor: " + text );
        };
    });
</script>
</div>
</body>

</html>
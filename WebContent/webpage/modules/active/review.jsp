<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
    <title>分配报名</title>
    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <script>
        var validateForm;
        function doSubmit(){//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
            if ($("#userId").val() === ""){
                top.layer.alert('请选择评分专家!', {icon: 0, title:'提示'});
                return false;
            }
            if(validateForm.form()){
                $("#inputForm").submit();
                return true;
            }
            return false;
        }
        $(document).ready(function() {
            $("#ids").val("");
            validateForm = $("#inputForm").validate({
                submitHandler: function(form){
                    loading('正在提交，请稍等...');
                    form.submit();
                }
            });
        });
        //选择活动
        function selectActity(){
            top.layer.open({
                type: 2,
                area: ['800px', '450px'],
                title:"选择活动",
                maxmin: true, //开启最大化最小化按钮
                content: "${ctx}/qqc/active/list/view",
                btn: ['确定', '关闭'],
                yes: function(index, layero){
                    var arr = layero.find("iframe")[0].contentWindow.selectActity();
                    console.log(arr);
                    if(!arr){
                        return false;
                    };
                    $("#activeName").val(arr.name);
                    $("#activeId").val(arr.id);
                    $("#form_activeId").val(arr.id);
                    $("#enrollTable").attr("src","${ctx}/qqc/enroll/list/active?type=view&activeId="+arr.id);
                    top.layer.close(index);
                },
                cancel: function(index){}
            });
        }

        function selectUser() {
            top.layer.open({
                type: 2,
                area: ['800px', '450px'],
                title:"选择报名",
                maxmin: true, //开启最大化最小化按钮
                content: "${ctx}/sys/user/list/view?userType=4",
                btn: ['确定', '关闭'],
                yes: function(index, layero){
                    var arr = layero.find("iframe")[0].contentWindow.selectUser();
                    console.log(arr);
                    if(!arr){
                        return false;
                    };
                    $("#userName").val(arr.name);
                    $("#userId").val(arr.id);
                    $("#enrollTable").attr("src",$("#enrollTable").attr("src")+"&userId="+arr.id);
                    $("#ids").val("");
                    top.layer.close(index);
                },
                cancel: function(index){}
            });
        }


    </script>
</head>
<body>
<form:form id="inputForm" action="${ctx}/qqc/active/saveReview" method="post" class="form-horizontal">
    <input type="hidden"  id="ids" name="ids" value="">
    <input type="hidden" id="activeId" name="activeId" value="${active.id}">
    <input type="hidden" id="userId" name="userId" value="">
</form:form>
<table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
    <tbody>
    <tr>
        <td  class="width-15 active">
            <label class="pull-right">活动标题：</label>
        </td>
        <td class="width-35" >
            <input  type="text" value="${active.title}"
                   class="form-control" readonly="readonly" >
        </td>
        <td  class="width-15 active">
            <label class="pull-right">专家名称：</label>
        </td>
        <td class="width-35" >
            <input style="width: 200px;" type="text" id="userName" value=""
                   class="form-control required" placeholder="请选择专家" readonly="readonly" onclick="selectUser()">
        </td    >
    </tr>
    <%--<tr>
        <td colspan="4">
            <button style="margin-left: -4px" type="button" onclick="selectEnroll()" class="btn btn-primary">
                <i class="fa fa-search"></i>选择报名
            </button>
        </td>
    </tr>--%>
    <tr>
        <c:choose>
            <c:when test="${active.enrollType == '1'}">
                <td colspan="4" height="800px">
                    <iframe id="enrollTable" src="${ctx}/qqc/enroll/list/active?type=view&activeId=${active.id}" width="100%" height="100%"  frameborder="0"></iframe>
                </td>
            </c:when>
            <c:when test="${active.enrollType == '2'}">
                <td colspan="4" height="800px">
                    <iframe id="enrollTable" src="${ctx}/qqc/entrepreneurship/list/view?activeId=${active.id}" width="100%" height="100%"  frameborder="0"></iframe>
                </td>
            </c:when>
        </c:choose>
    </tr>
    </tbody>
</table>
</body>
</html>

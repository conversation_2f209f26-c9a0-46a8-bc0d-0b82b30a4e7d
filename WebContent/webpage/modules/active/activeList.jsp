<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>活动列表</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
		.btn-default{
			color: #333 !important;
			background-color: #fff !important;
			border-color: #ccc !important;
		}
	</style>
	<script type="text/javascript">
		$(document).ready(function(){
			laydate({
				elem: '#beginDate',
				format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
				festival: true, //显示节日
				istime: false, //是否开启时间选择<br/>
				isclear: true, //是否显示清空<br/>
				istoday: true, //是否显示今天<br/>
				choose: function(datas){ //选择日期完毕的回调
					$("#beginDate").val(datas+" 00:00:00")
				}
			});
			laydate({
				elem: '#endDate',
				format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
				festival: true, //显示节日
				istime: false, //是否开启时间选择<br/>
				isclear: true, //是否显示清空<br/>
				istoday: true, //是否显示今天<br/>
				choose: function(datas){ //选择日期完毕的回调
					$("#endDate").val(datas+" 23:59:59")
				}
			});

            $('#activeTable thead tr th input.i-checks').on('ifChecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
                $('#activeTable tbody tr td input.i-checks').iCheck('check');
            });

            $('#activeTable thead tr th input.i-checks').on('ifUnchecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
                $('#activeTable tbody tr td input.i-checks').iCheck('uncheck');
            });
		})
		function online(obj){
			top.layer.confirm('是否上线该活动?', {icon: 3, title:'系统提示'}, function(index){
				window.location = "${ctx}/qqc/active/online?id="+obj;
				top.layer.close(index);
			});
		}
		function Offline(obj){
			top.layer.confirm('是否下线该活动?', {icon: 3, title:'系统提示'}, function(index){
				window.location = "${ctx}/qqc/active/Offline?id="+obj;
				top.layer.close(index);
			});
		}

		function addActive(add){
			var url = '${ctx}/qqc/active/form?type=Form&addType='+add;
			openDialog("新增活动",url,"1000px", "700px");
		}

		function downloadFiel(obj){
			top.layer.confirm('如果报名人数过多,获取文件可能需要较长时间,请耐心等待?', {icon: 3, title:'系统提示'}, function(index){
				window.location = "${ctx}/qqc/active/downloadFiel?id="+obj;
				top.layer.close(index);
			});
		}

		function selectActity(){
			var id="";
			var name="";
			var checked = 0;
			$("#activeTable tbody tr td input.i-checks:checkbox").each(function(){
				if(true == $(this).is(':checked')){
					checked ++;
					id = $(this).attr("id");
					name = $(this).attr("name");
				}
			})
			if(checked > 1){
				top.layer.alert('只能选择一个活动!', {icon: 0, title:'警告'});
				return;
			}
			if(id == ""){
				top.layer.alert('请至少选择一条数据!', {icon: 0, title:'警告'});
				return;
			}
			var myMap = {
				"id":id,
				"name":name
			};
			return myMap;
		}

		function reviewEnroll(id) {
            top.layer.open({
                type: 2,
                area: ['1000px', '700px'],
                title: '分配报名',
                maxmin: true, //开启最大化最小化按钮
                content: '${ctx}/qqc/active/review?id='+id ,
                btn: ['确定', '关闭'],
                yes: function(index, layero){
                    var iframeWin = layero.find('iframe')[0]; //得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
                    if(iframeWin.contentWindow.doSubmit() ){
                        top.layer.alert('分配成功!', {icon: 0, title:'提示'});
                    }
                },
                cancel: function(index){
                }
            });
        }
	</script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
<div class="ibox">
<div class="ibox-title">
		<h5>活动信息 </h5>
		<div class="ibox-tools">
			<a class="collapse-link">
				<i class="fa fa-chevron-up"></i>
			</a>
			<a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
				<i class="fa fa-wrench"></i>
			</a>
			<ul class="dropdown-menu dropdown-user">
				<li><a href="#">选项1</a>
				</li>
				<li><a href="#">选项2</a>
				</li>
			</ul>
			<a class="close-link">
				<i class="fa fa-times"></i>
			</a>
		</div>
	</div>
    
    <div class="ibox-content">
	<sys:message content="${message}"/>
	
	<!-- 查询条件 -->
	<div class="row">
	<div class="col-sm-12">
	<form:form id="searchForm" modelAttribute="active" action="" method="post" class="form-inline">
		<input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
		<div class="form-group">
			<span>标题：&nbsp;</span>
			<form:input autocomplete="off" path="title" cssStyle="cursor: pointer" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
			<span>	活动状态：</span>
			<form:select path="status"  class="form-control m-b">
				<form:option value="" label=""/>
				<form:options items="${fns:getDictList('active_status')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
			</form:select>
			<span>	活动类型：</span>
			<form:select path="type"  class="form-control m-b">
				<form:option value="" label=""/>
				<form:options items="${fns:getDictList('qqc_active_type')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
			</form:select>
			<span>创建日期范围：&nbsp;</span>
			<input autocomplete="off" id="beginDate" style="cursor: pointer" name="beginTime" type="text" maxlength="20" class="laydate-icon form-control layer-date input-sm"
				   value="${active.beginTime}"/>
			<label>&nbsp;--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label><input autocomplete="off" style="cursor: pointer" id="endDate" name="endTime" type="text" maxlength="20" class=" laydate-icon form-control layer-date input-sm"
																		value="${active.endTime}" />&nbsp;&nbsp;
		</div>
	</form:form>
	<br/>
	</div>
	</div>
	
	
	<!-- 工具栏 -->
	<div class="row">
	<div class="col-sm-12">
		<div class="pull-left">
			<c:if test="${mode == 'edit'}">
				<div class="btn-group">
					<button type="button" class="btn btn-default dropdown-toggle"
							data-toggle="dropdown">
						<span id="number_is_text">
							选择添加方式
						</span>
						<span class="caret"></span>
					</button>
					<ul id="addActive" style="right: auto;" class="dropdown-menu" role="menu">
						<li onclick="addActive('Add')"><a href="#"><i class="fa fa-plus"></i>添加活动</a></li>
						<li onclick="addActive('Link')"><a href="#"><i class="fa fa-plus"></i>添加外部链接活动</a></li>
					</ul>
				</div>
				<%--<shiro:hasPermission name="qqc:capital:add">--%>
				<%--</shiro:hasPermission>--%>
				<%--<shiro:hasPermission name="qqc:capital:del">--%>
				<table:delRow url="${ctx}/qqc/active/deleteAll" id="activeTable"></table:delRow><!-- 删除按钮 -->
				<%--</shiro:hasPermission>--%>
				<%-- <table:importExcel url="${ctx}/qqc/companyInfo/import"></table:importExcel><!-- 导入按钮 -->
	       		<table:exportExcel url="${ctx}/qqc/companyInfo/export"></table:exportExcel><!-- 导出按钮 --> --%>
	       		<!-- <button class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" onclick="sortOrRefresh()" title="刷新"><i class="glyphicon glyphicon-repeat"></i> 刷新</button> -->
			</c:if>
		</div>

		<div class="pull-right">
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
		</div>
	</div>
	</div>
	
	
	
	
	<table id="activeTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
		<thead>
			<tr>
				<th> <input type="checkbox" class="i-checks"></th>
				<th>类型</th>
				<th>标题</th>
				<th>状态</th>
				<th>创建时间</th>
				<th>活动开始时间</th>
				<th>活动结束时间</th>
				<th>报名人数</th>
				<th>分享链接</th>
				<th>分享二维码</th>
				<th>签到二维码</th>
				<c:if test="${mode == 'edit'}">
					<th>操作</th>
				</c:if>

			</tr>
		</thead>
		<tbody>
		<c:forEach items="${page.list}" var="active">
			<tr>
				<td> <input id="${active.id}" name="${active.title}"  type="checkbox" class="i-checks"></td>
				<td>
					${fns:getDictLabel(active.type, 'qqc_active_type', '')}
				</td>
				<td><a title="${active.title}" href="#" onclick="openDialogView('查看活动信息', '${ctx}/qqc/active/form?type=View&id=${active.id}&addType=${active.addType}','1000px', '700px')">
						${fns:abbr(active.title,30)}
				</a></td>
				<td>
					${fns:getDictLabel(active.status, 'active_status', '')}
				</td>
				<td>
					${fns:abbr(active.createTime,50)}
				</td>
				<td>
					${fns:abbr(active.beginTime,50)}
				</td>
				<td>
					${fns:abbr(active.endTime,50)}
				</td>
				<td align="center">
					<c:if test="${active.addType == 'Link'}">
						外部链接无法统计
					</c:if>
					<c:if test="${active.addType == 'Add'}">
						<c:if test="${active.number == '' || active.number == null}">
							0
						</c:if>
						<c:if test="${active.number != ''}">
							${active.number}
						</c:if>
					</c:if>
				</td>
				<td>
					<a href="#" onclick="windowOpen('${active.url}','活动',window.screen.width+'px',window.screen.height+'px')">
							${active.url}
					</a>
				</td>
				<td>
				</td>
				<td>
				</td>
				<c:if test="${mode == 'edit'}">
					<td>
						<c:if test="${active.addType == 'Add' && active.enrollType == '1'}">
							<a title="设置报名表单" onclick="openDialogView('报名表单', '${ctx}/customer/form/form?activeId=${active.id}&type=active',window.screen.availWidth+'px',  window.screen.availHeight-100+'px')"  href="#" class="btn btn-success btn-xs btn-circle" >报名</a>
						</c:if>
						<c:if test="${active.status == '1'}">
							<a title="活动下线" href="#" onclick="Offline('${active.id}')" class="btn btn-success btn-xs btn-circle" >下线</a>
						</c:if>
						<c:if test="${active.status == '2'}">
							<a title="上线活动" href="#" onclick="online('${active.id}')" class="btn btn-success btn-xs btn-circle" >上线</a>
						</c:if>
							<a title="修改信息" href="#" onclick="openDialog('修改信息', '${ctx}/qqc/active/form?type=Form&addType=${active.addType}&id=${active.id}','1000px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
							<a title="删除信息" href="${ctx}/qqc/active/delete?id=${active.id}" onclick="return confirmx('确认要删除该信息吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
					    <%--<a title="分配报名" href="#" onclick="reviewEnroll('${active.id}')" class="btn btn-success btn-xs btn-circle" >分配</a>--%>
                        <c:if test="${active.addType == 'Add'}">
                            <a title="分配报名" href="#" onclick="openDialog('分配报名', '${ctx}/qqc/active/review?id=${active.id}','1000px', '700px')" class="btn btn-success btn-xs btn-circle" >分配</a>
                        </c:if>
                    </td>
				</c:if>
			</tr>
		</c:forEach>
		</tbody>
	</table>
	<!-- 分页代码 -->
	<table:page page="${page}"></table:page>
	<br/>
	<br/>
	</div>
	</div>
</div>
</body>
<script>

</script>
</html>
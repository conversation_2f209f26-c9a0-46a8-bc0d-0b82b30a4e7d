<%@ taglib prefix="from" uri="http://www.springframework.org/tags/form" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>

    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <%-- switch --%>
    <link rel="stylesheet" href="${ctxStatic}/bootstrap/3.3.4/css/bootstrap-switch.min.css">
    <script src="${ctxStatic}/bootstrap/3.3.4/js/bootstrap-switch.min.js"></script>
    <!-- SUMMERNOTE -->
    <link href="${ctxStatic}/summernote/summernote.css" rel="stylesheet">
    <link href="${ctxStatic}/summernote/summernote-bs3.css" rel="stylesheet">

    <link rel="stylesheet" type="text/css" href="${ctxStatic}/webuploader-0.1.5/webuploader.css">
    <link rel="stylesheet" type="text/css" href="${ctxStatic}/webuploader-0.1.5/demo.css">

    <script src="${ctxStatic}/summernote/summernote.min.js"></script>
    <script src="${ctxStatic}/summernote/summernote-zh-CN.js"></script>

    <script type="text/javascript">
        // 添加全局站点信息
        var BASE_URL = '/webuploader';
    </script>
    <script type="text/javascript" src="${ctxStatic}/webuploader-0.1.5/webuploader.js"></script>
    <style>
        #uploader .queueList {
            margin: 0px;
            border: none;
        }
        #uploader .webuploader-pick{
            padding: 0px;
        }
        .input-group-addon, .input-group-btn{
            width: 100%;
        }
        #uploader .queueList.webuploader-dnd-over {
            border: none;
        }
        .btn-default{
            color: #333 !important;
            background-color: #fff !important;
            border-color: #ccc !important;
        }
    </style>
</head>

<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="row">

        <div class="col-sm-12 animated fadeInRight">
            <div class="mail-box">
                <div class="mail-body">
                    <form:form modelAttribute="active" id="inputForm" action="${ctx}/qqc/active/save" method="post" class="form-horizontal">
                        <div class="form-group">
                            <form:hidden path="id"/>
                            <label class="col-sm-2 control-label"><font color="red">*</font>活动标题：</label>
                            <div class="col-sm-8">
                                <input type="text" readonly="readonly" autocomplete="off" value="${active.title}" id="title" name="title"  class="form-control required" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>活动类型：</label>
                            <div class="col-sm-8">
                                <input type="text" readonly="readonly" autocomplete="off" value="${active.url}" id="url" name="url"  class="form-control required" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>活动类型：</label>
                            <div class="col-sm-8">
                                <form:select cssStyle="cursor: pointer;width: 150px" disabled="true" path="type"  class="form-control required m-b">
                                    <form:option value="" label=""/>
                                    <form:options items="${fns:getDictList('qqc_active_type')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
                                </form:select>
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>活动时间：</label>
                            <div class="col-sm-8">
                                <input autocomplete="off" readonly="readonly" id="beginDate" style="cursor: pointer" name="beginTime" type="text" maxlength="20" class="required laydate-icon form-control layer-date input-sm"
                                       value="${active.beginTime}"/>
                                <label>&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label><input readonly="readonly" autocomplete="off" style="cursor: pointer" id="endDate" name="endTime" type="text" maxlength="20" class="required laydate-icon form-control layer-date input-sm"
                                                                                            value="${active.endTime}" />&nbsp;&nbsp;
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>报名时间：</label>
                            <div class="col-sm-8">
                                <input autocomplete="off" readonly="readonly" id="apllyBeginDate" style="cursor: pointer" name="apllyBeginTime" type="text" maxlength="20" class="required laydate-icon form-control layer-date input-sm"
                                       value="${active.apllyBeginTime}"/>
                                <label>&nbsp;至&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label><input readonly="readonly" autocomplete="off" style="cursor: pointer" id="apllyEndDate" name="apllyEndTime" type="text" maxlength="20" class="required laydate-icon form-control layer-date input-sm"
                                                                                           value="${active.apllyEndTime}" />&nbsp;&nbsp;
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>活动地点：</label>
                            <div class="col-sm-8">
                                <form:input readonly="true" path="indexAddress"  class="form-control" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">主办方：</label>
                            <div class="col-sm-8">
                                <input type="text" readonly="readonly" autocomplete="off" value="${active.sponsor}" id="sponsor" name="sponsor"  class="form-control" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">活动费用：</label>
                            <div class="col-sm-3">
                                <input readonly="readonly" type="text" value="${active.cost}" id="cost" name="cost"  class="form-control" />
                            </div>
                            <label class="col-sm-2 control-label">活动积分：</label>
                            <div class="col-sm-3">
                                <input readonly="readonly" type="text" value="${active.integral}" id="integral" name="integral"  class="form-control" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">活动海报：</label>
                            <input style="display: none" name="img" value="${active.img}">
                            <div class="col-sm-8">
                                <img style="width: 110px;height: 110px" src="${active.img}">
                            </div>
                        </div>
                    </form:form>
                </div>
            </div>
        </div>

    </div>
</div>
<script type="text/javascript">
    var validateForm;
    var enumType = {
        "yes":"1",
        "no":"2"
    }
    $(document).ready(function () {
        $("#summernote").summernote({
            height: 400,
            minHeight: 300,
            maxHeight: 500,
            lang:'zh-CN',
        });
        //开关
        $('#acc_is input').bootstrapSwitch({
            onText:'是',
            offText:'否',
        });
        $('#review_is input').bootstrapSwitch({
            onText:'是',
            offText:'否' ,
        });
        $('#display_user input').bootstrapSwitch({
            onText:'是',
            offText:'否' ,
        });
       if($("#accIs").val() == enumType.yes){
            $('#acc_is input').bootstrapSwitch('state',true,false);
        }else{
            $('#acc_is input').bootstrapSwitch('state',false,true);
        }
        if($("#isReview").val() == enumType.yes){
            $('#review_is input').bootstrapSwitch('state',true,false);
        }else{
            $('#review_is input').bootstrapSwitch('state',false,true);
        }
        if($("#displayUserIs").val() == enumType.yes){
            $('#display_user input').bootstrapSwitch('state',true,false);
        }else{
            $('#display_user input').bootstrapSwitch('state',false,true);
        }
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

    });
</script>
</div>
</body>

</html>
<%@ taglib prefix="from" uri="http://www.springframework.org/tags/form" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>审核</title>
    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <script>
        var validateForm;
        function doSubmit(){//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
            if(validateForm.form()){
                $("#inputForm").submit();
                return true;
            }
            return false;
        }
        $(document).ready(function() {
            validateForm = $("#inputForm").validate({
                submitHandler: function(form){
                    loading('正在提交，请稍等...');
                    form.submit();
                },
                errorContainer: "#messageBox",
                errorPlacement: function(error, element) {
                    $("#messageBox").text("输入有误，请先更正。");
                    if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
                        error.appendTo(element.parent().parent());
                    } else {
                        error.insertAfter(element);
                    }
                }
            });
        });
    </script>
</head>
<body >
<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-sm-12 animated fadeInRight">
            <div class="mail-box">
                <div class="mail-body">
                    <form:form modelAttribute="enroll" id="inputForm" action="${ctx}/qqc/enroll/saveReview" method="post" class="form-horizontal">
                        <div class="form-group">
                            <form:hidden path="id"/>
                            <label class="col-sm-2 control-label">审核状态：</label>
                            <div class="col-sm-8">
                                <input type="radio" name="status" value="1">通过
                                <input type="radio" name="status" value="0">不通过
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">审核说明：</label>
                            <div class="col-sm-8">
                                <textarea class="form-control" name="reviewReason" cols="3" rows="3"></textarea>
                            </div>
                        </div>
                    </form:form>
                </div>
            </div>
        </div>

    </div>
</div>
</body>
<script>
</script>
</html>
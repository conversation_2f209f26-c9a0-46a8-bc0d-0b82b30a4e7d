<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>报名列表</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
	</style>
	<script type="text/javascript">
		$(document).ready(function(){
			laydate({
				elem: '#beginDate',
				format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
				festival: true, //显示节日
				istime: false, //是否开启时间选择<br/>
				isclear: true, //是否显示清空<br/>
				istoday: true, //是否显示今天<br/>
				choose: function(datas){ //选择日期完毕的回调
					$("#beginDate").val(datas+" 00:00:00")
				}
			});
			laydate({
				elem: '#endDate',
				format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
				festival: true, //显示节日
				istime: false, //是否开启时间选择<br/>
				isclear: true, //是否显示清空<br/>
				istoday: true, //是否显示今天<br/>
				choose: function(datas){ //选择日期完毕的回调
					$("#endDate").val(datas+" 23:59:59")
				}
			});

			$("#uploadFile").on('change',function () {
				$("#importForm").submit();
				loading('正在导入，请稍等...');
			})


            $('#enrollTable thead tr th input.i-checks').on('ifChecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
                $('#enrollTable tbody tr td input.i-checks').iCheck('check');
                selectEnroll();
            });

            $('#enrollTable thead tr th input.i-checks').on('ifUnchecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
                $('#enrollTable tbody tr td input.i-checks').iCheck('uncheck');
                $("#ids", parent.document).val("");
                selectEnroll();
            });

            $('#enrollTable tbody tr td input.i-checks').on('ifChecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
                selectEnroll();
            });

            $('#enrollTable tbody tr td input.i-checks').on('ifUnchecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
                $("#ids", parent.document).val("");
                selectEnroll();
            });

            checked($("#ids", parent.document).val());

		})

		//选择活动
		function selectActity(){
			top.layer.open({
				type: 2,
				area: ['800px', '450px'],
				title:"选择活动",
				maxmin: true, //开启最大化最小化按钮
				content: "${ctx}/qqc/active/list/view",
				btn: ['确定', '关闭'],
				yes: function(index, layero){
					var arr = layero.find("iframe")[0].contentWindow.selectActity();
					console.log(arr);
					if(!arr){
						return false;
					};
					$("#actityName").val(arr.name);
					$("#actityId").val(arr.id);
					$("#uploadFileActiveId").val(arr.id);
					top.layer.close(index);
				},
				cancel: function(index){}
			});
		}

		function downloadFile(){
			var actityId = $("#actityId").val();
			if (actityId === ""){
				top.layer.alert('请指定一个活动!', {icon: 0, title:'警告'});
				return;
			}
			top.layer.confirm('如果报名人数过多,获取文件可能需要较长时间,请耐心等待?', {icon: 3, title:'系统提示'}, function(index){
				window.location = "${ctx}/qqc/active/downloadFiel?id="+actityId;
				loading('正在导出，请稍等...');
				top.layer.close(index);
				closeTip();
			});
		}

		function downloadFileTemp(){
			var actityId = $("#actityId").val();
			if (actityId === ""){
				top.layer.alert('请指定一个活动!', {icon: 0, title:'警告'});
				return;
			}
			top.layer.confirm('请勿修改模板文件中的各列,如有疑问,请联系相关技术人员咨询。', {icon: 3, title:'系统提示'}, function(index){
				window.location = "${ctx}/qqc/active/downloadFileTemplate?id="+actityId;
				loading('正在下载模板，请稍等...');
				top.layer.close(index);
				closeTip();
			});
		}

		function importEnroll(){
			var actityId = $("#actityId").val();
			if (actityId === ""){
				top.layer.alert('请指定一个活动!', {icon: 0, title:'警告'});
				return;
			}
			$("#uploadFile").click();
		}

        function selectEnroll(){
            var id="";
            $("#enrollTable tbody tr td input[type=checkbox]").each(function(){
                if(true === $(this).is(':checked')){
                    if (id === ""){
                        id = $(this).attr("id");
                    }else{
                        id += ","+$(this).attr("id");
                    }
                }
            })
            $("#ids", parent.document).val(id);
        }

        function checked(ids) {
		    if (ids == undefined){
		        ids = "";
            }
		    $("#enrollTable tbody tr td input[type=checkbox]").each(function(){
                if (ids.includes($(this).attr("id"))){
                    $(this).parent().addClass("checked");
                }
            })
        }

        function selectAll() {
            $("#enrollTable tbody tr td input[type=checkbox]").each(function(){
                $(this).parent().addClass("checked");
            })

        }
	</script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
<div class="ibox">
<div class="ibox-title">
		<h5>报名信息 </h5>
		<div class="ibox-tools">
			<a class="collapse-link">
				<i class="fa fa-chevron-up"></i>
			</a>
			<a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
				<i class="fa fa-wrench"></i>
			</a>
			<ul class="dropdown-menu dropdown-user">
				<li><a href="#">选项1</a>
				</li>
				<li><a href="#">选项2</a>
				</li>
			</ul>
			<a class="close-link">
				<i class="fa fa-times"></i>
			</a>
		</div>
	</div>

    <div class="ibox-content">
	<sys:message content="${message}"/>

	<!-- 查询条件 -->
	<div class="row">
	<div class="col-sm-12">
            <form:form id="searchForm" modelAttribute="enroll" action="" method="post" class="form-inline">
                <input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
                <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>

                <div class="form-group">
                    <c:if test="${type == 'edit'}">
                        <span>活动标题：&nbsp;</span>
                        <input type="hidden" id="actityId" name="activeId" value="${enroll.activeId}">
                        <input style="width: 200px;" type="text" id="actityName" name="title" value="${enroll.title}"
                               class="form-control required" readonly="readonly" onclick="selectActity()">
                        <button style="margin-left: -4px" type="button" onclick="selectActity()" class="btn btn-primary">
                            <i class="fa fa-search"></i>
                        </button>
                    </c:if>
                    <span>报名日期范围：&nbsp;</span>
                    <input autocomplete="off" id="beginDate" style="cursor: pointer" name="beginTime" type="text" maxlength="20" class="laydate-icon form-control layer-date input-sm"
                           value="${enroll.beginTime}"/>
                    <label>&nbsp;--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label><input autocomplete="off" style="cursor: pointer" id="endDate" name="endTime" type="text" maxlength="20" class=" laydate-icon form-control layer-date input-sm"
                           value="${enroll.endTime}"/>
                </div>
                <c:if test="${type == 'edit'}">
                    <div class="form-group" style="margin-top: 2px">
                        <span>申请人姓名:</span>
                        <input style="width: 200px;" type="text" id="applyName" name="applyName" value="${enroll.applyName}"
                               class="form-control"  >
                        <span>申请人电话:</span>
                        <input style="width: 200px;" type="text" id="applyPhone" name="applyPhone" value="${enroll.applyPhone}"
                               class="form-control"  >
                        <span>审核状态:</span>
                        <form:select path="status"  class="form-control m-b">
                            <form:option value="" label=""/>
                            <form:options items="${fns:getDictList('qqc_enroll_status')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
                        </form:select>
                    </div>
                </c:if>
            </form:form >
	<br/>
	</div>
	</div>


	<!-- 工具栏 -->
	<div class="row">
	<div class="col-sm-12">
		<div class="pull-left">
            <c:if test="${type == 'edit'}">
                <button onclick="downloadFileTemp()" class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" title="下载模板"><i class="fa fa-folder-open-o"></i> 下载模板</button>
                <button id="btnImport" onclick="importEnroll()" class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" title="导入"><i class="fa fa-folder-open-o"></i> 导入</button>
                <div id="importBox" class="hide">
                    <form id="importForm" action="${ctx}/qqc/active/ImportEnroll/active" method="post" enctype="multipart/form-data"
                          style="padding-left:20px;text-align:center;" ><br/>
                        <input name="id" type="hidden" id="uploadFileActiveId">
                        <input id="uploadFile" name="file" type="file" style="width:330px"/>导入文件不能超过5M，仅允许导入“xls”或“xlsx”格式文件！<br/>　　
                    </form>
                </div>
                <button id="btnExport" onclick="downloadFile()" class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" title="导出"><i class="fa fa-file-excel-o"></i> 导出</button>
            </c:if>
        </div>
		<div class="pull-right">
            <button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
            <button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
        </div>
	</div>
	</div>




	<table id="enrollTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
		<thead>
			<tr>
				<th> <input type="checkbox"  class="i-checks" /></th>
				<th>申请人</th>
				<th>报名时间</th>
                <th>审核状态</th>
                <c:if test="${type == 'edit'}">
				    <th>操作</th>
                </c:if>
			</tr>
		</thead>
		<tbody>
		<c:forEach items="${page.list}" var="enroll">
			<tr>
				<td> <input type="checkbox" name="${enroll.applyId}" id="${enroll.id}" class="i-checks" /></td>
				<td>
                     ${enroll.applyId}
				</td>
				<td>
					${enroll.createTime}
				</td>
                <td>
                        ${fns:getDictLabel(enroll.status, 'qqc_enroll_status', '')}
                </td>
                <c:if test="${type == 'edit'}">
                    <td>
                        <a title="查看" href="#" onclick="openDialogView('查看信息', '${ctx}/qqc/enroll/form/active?id=${enroll.id}','1000px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
                        <a title="删除" href="${ctx}/qqc/enroll/delete/active?id=${enroll.id}" onclick="return confirmx('确认要删除该信息吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
                        <c:if test="${enroll.applyId == '管理员导入'}">
                            <a title="上传附件" href="#"  onclick="openDialog('报名信息', '${ctx}/qqc/enroll/uploadAttachmentForm/active?id=${enroll.id}','1000px', '700px')" class="btn btn-success btn-xs btn-circle"><i class="fa fa-edit"></i></a>
                        </c:if>
                        <c:if test="${enroll.active.isReview == '1'}">
                            <a title="审核" href="#" onclick="openDialog('审核', '${ctx}/qqc/enroll/review?id=${enroll.id}','1000px', '700px')" class="btn btn-success btn-xs btn-circle" >审核</a>
                        </c:if>
                    </td>
                </c:if>
			</tr>
		</c:forEach>
		</tbody>
	</table>
	<!-- 分页代码 -->
	<table:page page="${page}"></table:page>
	<br/>
	<br/>
	</div>
	</div>
</div>
</body>
</html>
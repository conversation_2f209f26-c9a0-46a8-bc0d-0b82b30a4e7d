<%@ taglib prefix="from" uri="http://www.springframework.org/tags/form" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>报名详情</title>
    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <link rel="stylesheet" href="${ctxStatic}/weui/css/table-css.css">
    <script>
        //加载扩展模块
        layer.config({
            extend: 'extend/layer.ext.js'
        });
        layer.ready(function(){
            //使用相册
            layer.photos({
                photos: '.img'
            });
        });

        $(document).ready(function () {
            $(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg img", function (e) {
                var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
                    (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                var imagep = $(".layui-layer-phimg").parent().parent();
                var image = $(".layui-layer-phimg").parent();
                var h = image.height();
                var w = image.width();
                if (delta > 0) {
                    if (h < (window.innerHeight)) {
                        h = h * 1.05;
                        w = w * 1.05;
                    }
                } else if (delta < 0) {
                    if (h > 100) {
                        h = h * 0.95;
                        w = w * 0.95;
                    }
                }
                imagep.css("top", (window.innerHeight - h) / 2);
                imagep.css("left", (window.innerWidth - w) / 2);
                image.height(h);
                image.width(w);
                imagep.height(h);
                imagep.width(w);
            });
        })
    </script>
</head>
<body class="gray-bg">
<form:form method="post" class="form-horizontal">
    <table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
        <tbody>
        <tr>
            <td  class="width-15 active">
                <label class="pull-right">活动标题：</label>
            </td>
            <td colspan="3" class="width-35" >
                <input type="text" readonly value="${enroll.active.title}" class="form-control">
            </td>
            <td  class="width-15 active">
                <label class="pull-right">活动详情地址：</label>
            </td>
            <td colspan="3" class="width-35" >
                <input type="text" readonly value="${enroll.active.indexAddress}" class="form-control">
            </td>
        </tr>
        <tr>
            <td  class="width-15 active">
                <label class="pull-right">审核状态：</label>
            </td>
            <td colspan="3" class="width-35" >
                <input type="text" readonly value="${fns:getDictLabel(enroll.status, 'qqc_enroll_status', '')}" class="form-control">
            </td>
            <td  class="width-15 active">
                <label class="pull-right">审核说明：</label>
            </td>
            <td colspan="3" class="width-35" >
                <textarea cols="3" rows="3" class="form-control" readonly>${enroll.reviewReason}</textarea>
            </td>
        </tr>
        </tbody>
    </table>
</form:form>
<div align="center">
    <h3>报名信息</h3>
    <table>
        <tbody>
        <c:forEach items="${configList}" var="config">
            <tr>
                <td data-label="${config.title}">${config.title}</td>
                <td data-label="${config.value}">
                    <c:choose>
                        <c:when test="${config.type == 'uploaderImg'}">

                            <c:choose>
                                <c:when test="${empty config.value}">
                                    <span style="color: red">未上传图片</span>
                                </c:when>
                                <c:otherwise>
                                    <c:forEach items="${config.value.split(',')}" var="img">
                                        <div class="img">
                                            <img width="200px" height="200px" src="${img}">
                                        </div>
                                    </c:forEach>
                                </c:otherwise>
                            </c:choose>
                        </c:when>
                        <c:when test="${config.type == 'uploaderFile'}">
                            <a href="${config.value}">附件</a>
                        </c:when>
                        <c:otherwise>
                            ${config.value}
                        </c:otherwise>
                    </c:choose>
                </td>
            </tr>
        </c:forEach>
        </tbody>
    </table>
</div>
</body>
<script>
</script>
</html>
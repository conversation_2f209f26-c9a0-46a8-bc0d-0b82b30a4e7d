<%@ taglib prefix="from" uri="http://www.springframework.org/tags/form" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>上传附件</title>
    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <link rel="stylesheet" href="${ctxStatic}/weui/css/table-css.css">
    <script>
        var validateForm;
        function doSubmit(){//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
            if(validateForm.form()){
                $("#inputForm").submit();
                return true;
            }
            return false;
        }
        $(document).ready(function() {
            //$("#name").focus();
            validateForm = $("#inputForm").validate({
                submitHandler: function(form){
                    loading('正在提交，请稍等...');
                    form.submit();
                },
                errorContainer: "#messageBox",
                errorPlacement: function(error, element) {
                    $("#messageBox").text("输入有误，请先更正。");
                    if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
                        error.appendTo(element.parent().parent());
                    } else {
                        error.insertAfter(element);
                    }
                }
            });

            $("#uploadFile").on('change',function (e) {
                var file = e.target.files[0],
                    title = $(this).attr("title");
                //文件路径
                var fileName = file.name;
                //获取最后一个.的位置
                var index= fileName.lastIndexOf(".");
                //获取后缀
                var ext = fileName.substr(index+1);
                //输出结果
                console.log(ext);
                console.log(file.size);
                if (ext !== "pdf"){
                    top.layer.alert(title + '附件不是PDF格市', {icon: 0, title: '警告'});
                }else if (file.size >= 10485760) {
                    top.layer.alert(title + '附件不能超过10M', {icon: 0, title: '警告'});
                }
            })
        });
    </script>
</head>
<body class="gray-bg">
<div align="center">
    <h3>报名信息</h3>
    <form:form enctype="multipart/form-data" id="inputForm" action="${ctx}/qqc/enroll/uploadAttachment/active" method="post" class="form-horizontal">
        <input type="hidden" name="id" value="${enroll.id}">
        <table>
            <tbody>
            <c:forEach items="${configList}" var="config">
                <tr>
                    <td data-label="${config.title}">${config.title}</td>
                    <td data-label="${config.value}">
                        <input type="hidden" name="index" value="${config.index}">
                        <input type="hidden" name="columnType" value="${config.columnType}">
                        <input type="hidden" name="title" value="${config.title}">
                        <input type="hidden" name="req" value="${config.req}">
                        <input type="hidden" name="log" value="${config.log}">
                        <input type="hidden" name="value" value="">
                        <input title="${config.title}" name="file" type="file" id="uploadFile" accept=".pdf" style="width:330px">
                        <span style="display: inherit;color: red"><b>导入文件不能超过10M，仅允许导入"PDF"格式文件！</b></span>
                    </td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
    </form:form>
</div>
</body>
<script>
</script>
</html>
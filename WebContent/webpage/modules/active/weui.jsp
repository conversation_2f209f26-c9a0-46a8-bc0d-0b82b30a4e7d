<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html lang="en">
	<head>
		<meta charset="utf-8">
		<title>审批-表单设计器</title>
		<script src="${ctxStatic}/weui/js/jquery.min.js"></script>
		<script src="${ctxStatic}/weui/js/jquery-ui.min.js"></script>
		<script src="${ctxStatic}/weui/js/jquery.qrcode.min.js"></script>
		<link rel="stylesheet" href="${ctxStatic}/weui/css/index.css">
		<link rel="stylesheet" href="${ctxStatic}/weui/css/xiedajian.css">

	</head>
	<body>
		<input hidden="hidden" id="id" value="${customerForm.id}" />
		<input hidden="hidden" id="activeId" value="${customerForm.activeId}" />
		<input hidden="hidden" id="type" value="${customerForm.type}" />
		<div id="body" class="">
			<div data-reactroot="" class="wf-wrapper">
				<div class="wf-header">
					<div class="head-title">
						<a href="#" target="_self">
							<span>报名页面设计器</span>
						</a>
						<%--<span class="navbar-head"></span>--%>
					</div>
					<div class="head-actions disabled">
						<button onclick="" class="wf-new-button wf-button-green" id="xdj-yulan">预览</button>
						<button class="wf-new-button wf-button-green" id="xdj-saveBtn">保存</button>
						<button class="wf-new-button wf-button-green" id="xdj-codeBtn">二维码</button>
						<button class="wf-new-button wf-button-green" id="xdj-preview-close">二维码</button>
						<div id="xdj-qrcode"></div>
						<%--<div id="xdj-preview" style="display: none" class="qrcode-box qrcode"></div>--%>
					</div>
				</div>

				<div class="wf-main">
					<div class="wf-panel wf-widgetspanel">
						<div class="ant-tabs ant-tabs-top ant-tabs-line">
							<div role="tablist" class="ant-tabs-bar" tabindex="0">
								<div class="ant-tabs-nav-container"><span unselectable="unselectable" class="ant-tabs-tab-prev ant-tabs-tab-btn-disabled"><span
										 class="ant-tabs-tab-prev-icon"></span></span><span unselectable="unselectable" class="ant-tabs-tab-next ant-tabs-tab-btn-disabled"><span
										 class="ant-tabs-tab-next-icon"></span></span>
									<div class="ant-tabs-nav-wrap">
										<div class="ant-tabs-nav-scroll">
											<div class="ant-tabs-nav ant-tabs-nav-animated">
												<div class="ant-tabs-ink-bar ant-tabs-ink-bar-animated" style="transform: translate3d(0px, 0px, 0px); width: 312px; display: block;"></div>
												<div role="tab" aria-disabled="false" aria-selected="true" class="ant-tabs-tab-active ant-tabs-tab">控件库
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="ant-tabs-content ant-tabs-content-animated" style="margin-left: 0%;">
								<div id="itemClone" role="tabpanel" aria-hidden="false" class="ant-tabs-tabpane ant-tabs-tabpane-active">
									<div class="item" data-columntype="inputText">
										<div class="wf-widgetsitem"><label>单行输入框</label><i class="widgeticon iconfont textfield"></i></div>
										<div class="wf-component wf-component-textfield" data-title="单行输入框" data-tip="请输入" data-req="2" data-log="1">
											<div class="wf-remove icon icon-close"></div>
											<div class="wf-overlay"></div>
											<div class="wf-componentview">
												<div class="wf-componentview-border"><label class="wf-componentview-label">单行输入框</label><span class="wf-componentview-placeholder">请输入</span></div>
											</div>
										</div>
										<div class="wf-form wf-widgetsettings">
											<div class="wf-field wf-setting-label">
												<div class="fieldname">标题<span class="fieldinfo">最多20字</span></div>
												<div class="fieldblock"><input type="text" class="" value="单行输入框"></div>
											</div>
											<div class="wf-field wf-setting-placeholder">
												<div class="fieldname">提示文字<span class="fieldinfo">最多50字</span></div>
												<div class="fieldblock"><input type="text" class="" value="请输入">
													<div class="fieldtips">内容最多可填写1000个字</div>
												</div>
											</div>
											<div class="wf-field wf-setting-required">
												<div class="fieldname">验证</div>
												<label class="fieldblock"><input type="checkbox" value="1"><span class="verticalmiddle">（必填）</span></label>
											</div>
											<div class="wf-field wf-setting-print">
												<div class="fieldname">打印</div>
												<label class="fieldblock"><input type="checkbox" value="1"><span class="verticalmiddle">参与打印</span><span
													 class="fieldinfo verticalmiddle">（如不勾选，打印时不显示该项）</span></label>
											</div>
										</div>

									</div>
									<div class="item" data-columntype="textareaText">
										<div class="wf-widgetsitem"><label>多行输入框</label><i class="widgeticon iconfont textareafield"></i></div>
										<div class="wf-component wf-component-textareafield" data-title="多行输入框" data-tip="请输入" data-req="2" data-log="1">
											<div class="wf-remove icon icon-close"></div>
											<div class="wf-overlay"></div>
											<div class="wf-componentview">
												<div class="wf-componentview-border"><label class="wf-componentview-label">多行输入框</label><span class="wf-componentview-placeholder">请输入</span></div>
											</div>
										</div>
									</div>
									<div class="item" data-columntype="numberText">
										<div class="wf-widgetsitem"><label>数字输入框</label><i class="widgeticon iconfont numberfield"></i></div>
										<div class="wf-component wf-component-numberfield" data-title="数字输入框" data-tip="请输入" data-unit="" data-req="2"
										 data-log="1">
											<div class="wf-remove icon icon-close"></div>
											<div class="wf-overlay"></div>
											<div class="wf-componentview">
												<div class="wf-componentview-border"><label class="wf-componentview-label">
														数字输入框
													</label><span class="wf-componentview-placeholder">请输入</span>
												</div>
											</div>
										</div>
									</div>
									<div class="item" data-columntype="singleSelect">
										<div class="wf-widgetsitem"><label>单选框</label><i class="widgeticon iconfont ddselectfield"></i></div>
										<div class="wf-component wf-component-ddselectfield" data-title="单选框" data-tip="请选择" data-items="请选择,选项1,选项2,选项3"
										 data-req="2" data-log="1">
											<div class="wf-remove icon icon-close"></div>
											<div class="wf-overlay"></div>
											<div class="wf-componentview">
												<div class="wf-componentview-border"><label class="wf-componentview-label">单选框</label><span class="wf-componentview-placeholder">请选择</span><i
													 class="icon icon-enter"></i>
												</div>
											</div>
										</div>
									</div>
									<div class="item" data-columntype="multipleSelect">
										<div class="wf-widgetsitem"><label>多选框</label><i class="widgeticon iconfont ddmultiselectfield"></i></div>
										<div class="wf-component wf-component-ddmultiselectfield" data-title="多选框" data-items="选项1,选项2,选项3" data-req="2"
										 data-log="1">
											<div class="wf-remove icon icon-close"></div>
											<div class="wf-overlay"></div>
											<div class="wf-componentview">
												<div class="wf-componentview-border"><label class="wf-componentview-label">多选框</label><span class="wf-componentview-placeholder">请选择</span><i
													 class="icon icon-enter"></i>
												</div>
											</div>
										</div>
									</div>
									<div class="item" data-columntype="selectDate">
										<div class="wf-widgetsitem"><label>日期</label><i class="widgeticon iconfont dddatefield"></i>
										</div>
										<div class="wf-component wf-component-dddatefield" data-type="YYYY" data-title="日期" data-req="2" data-log="1" >
											<div class="wf-remove icon icon-close"></div>
											<div class="wf-overlay"></div>
											<div class="wf-componentview">
												<div class="wf-componentview-border"><label class="wf-componentview-label">日期</label><span class="wf-componentview-placeholder">YYYY</span><i
													 class="icon icon-enter"></i>
												</div>
											</div>
										</div>
									</div>
									<%--<div class="item" data-columntype="dateRange">
										<div class="wf-widgetsitem"><label>日期区间</label><i class="widgeticon iconfont dddaterangefield"></i></div>
										<div class="wf-component wf-component-dddaterangefield" data-type="YYYY" data-title="开始时间" data-secondtitle="结束时间"
										 data-req="2" data-log="1">
											<div class="wf-remove icon icon-close"></div>
											<div class="wf-overlay"></div>
											<div class="wf-componentview">
												<div class="wf-componentview-border"><label class="wf-componentview-label">开始时间</label><span class="wf-componentview-placeholder">YYYY</span><i
													 class="icon icon-enter"></i>
												</div>
												<div class="wf-componentview-border"><label class="wf-componentview-label">结束时间</label><span class="wf-componentview-placeholder">YYYY</span><i
													 class="icon icon-enter"></i>
												</div>
											</div>
										</div>
									</div>--%>
									<div class="item" data-columntype="uploaderImg">
										<div class="wf-widgetsitem"><label>图片</label><i class="widgeticon iconfont ddphotofield"></i></div>
										<div class="wf-component wf-component-ddphotofield" data-title="图片" data-req="2" data-log="1">
											<div class="wf-remove icon icon-close"></div>
											<div class="wf-overlay"></div>
											<div class="wf-componentview">
												<div class="wf-componentview-border"><label class="wf-componentview-label">图片</label><span class="wf-componentview-placeholder"></span><i
													 class="icon icon-camera"></i></div>
											</div>
										</div>
									</div>
									<%--<div class="item" data-columntype="tips">
										<div class="wf-widgetsitem"><label>说明文字</label><i class="widgeticon iconfont textnote"></i>
										</div>
										<div class="wf-component wf-component-textnote" data-textnote="请输入说明文字" data-link="" data-log="1">
											<div class="wf-remove icon icon-close"></div>
											<div class="wf-overlay"></div>
											<div class="wf-componentview">
												<div class="wf-componentview-content">
													<div class="textnote-content">
														<p>请输入说明文字</p>
													</div>
												</div>
											</div>
										</div>
									</div>--%>
									<div class="item" data-columntype="moneyInput">
										<div class="wf-widgetsitem"><label>金额</label><i class="widgeticon iconfont moneyfield"></i>
										</div>
										<div class="wf-component wf-component-moneyfield" data-title="金额（元）" data-tip="请输入" data-req="2" data-log="2">
											<div class="wf-remove icon icon-close"></div>
											<div class="wf-overlay"></div>
											<div class="wf-componentview">
												<div class="wf-componentview-border"><label class="wf-componentview-label">金额（元）</label><span class="wf-componentview-placeholder">请输入</span></div>
												<div class="cnformat">大写：壹万元整（示例）</div>
											</div>
										</div>
									</div>
									<div class="item" data-columntype="uploaderFile">
										<div class="wf-widgetsitem"><label>附件</label><i class="widgeticon iconfont ddattachment"></i></div>
										<div class="wf-component wf-component-ddattachment" data-title="附件" data-req="2" data-log="1">
											<div class="wf-remove icon icon-close"></div>
											<div class="wf-overlay"></div>
											<div class="wf-componentview">
												<div class="wf-componentview-border"><label class="wf-componentview-label">附件</label><span class="wf-componentview-placeholder"></span><i
														class="icon icon-chakanfujian"></i></div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="wf-formcanvas">
						<div class="wf-formcanvas-inner">
							<div class="wf-formcanvas-body dropbody empty">
								<div class="empty-bg-text">请从左侧拖动控件来创建属于您的表单</div>
							</div>
						</div>
					</div>
					<div class="wf-panel wf-settingpanel">
						<div class="ant-tabs ant-tabs-top ant-tabs-line">
							<div role="tablist" class="ant-tabs-bar" tabindex="0">
								<div class="ant-tabs-nav-container ant-tabs-nav-container2">
									<div class="ant-tabs-nav-wrap">
										<div class="ant-tabs-nav-scroll">
											<div class="ant-tabs-nav ant-tabs-nav-animated">
												<div role="tab" aria-disabled="false" aria-selected="false" data-i="1" class="ant-tabs-tab">控件设置
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="ant-tabs-content ant-tabs-content2 ant-tabs-content-animated" style="margin-left: -100%;">
								<div role="tabpanel" id="wf-widgetsettings" aria-hidden="false" class="ant-tabs-tabpane ant-tabs-tabpane-active">
								</div>
								<div role="tabpanel" aria-hidden="true" class="ant-tabs-tabpane ant-tabs-tabpane-active">
									<div class="wf-field wf-setting-title">
										<div class="fieldname">名称
										</div>
										<div class="fieldblock">
											<input type="text" autocomplete="off" class="" value="${customerForm.formName}" id="xdj-formName">
										</div>
									</div>
									<div class="wf-field wf-setting-description">
										<div class="fieldname">描述</div>
										<div class="fieldblock">
											<textarea type="text" rows="6" class="" id="xdj-formDescribe">${customerForm.formDescribe}</textarea>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<script src="${ctxStatic}/weui/js/index.js"></script>
		<script>
			//初始化页面
			$(document).ready(function() {
				startWatch();
				let configs = ${config};
				if(configs == null) return
				//初始化
				if (configs.length < 1) $('.wf-formcanvas-body').show();
				else $('.wf-formcanvas-body').hide();
				for (var index in configs) {
					var config = JSON.parse(configs[index])
					var that = $("#itemClone div[data-columntype=" + config.columnType + "]").clone();
					$(that).css("display", "block");
					$(that).find('.wf-component').attr("data-title", config.title);
					$(that).find('.wf-component').attr("data-tip", config.tip);
					$(that).find('.wf-component').attr("data-req", config.req);
					$(that).find('.wf-component').attr("data-log", config.log);
					$(that).find('.wf-component').attr("data-items", config.items);
					$(that).find('.wf-component').attr("data-secondtitle", config.secondtitle);
					$(that).find('.wf-component').attr("data-link", config.link);
					$(that).find('.wf-component').attr("data-type", config.type);
					$(that).find('.wf-component').attr("data-unit", config.unit);
					$(that).find(".wf-componentview-label").html(config.title);
					if(config.columnType === "selectDate" || config.columnType === "dateRange"){
						$(that).find('.wf-componentview-placeholder').html(config.type);
					}else{
						$(that).find('.wf-componentview-placeholder').html(config.tip);
					}
					$(".wf-formcanvas-inner").append(that);
					selectItem(that);
				}
			})

			//保存按钮
			$("#xdj-saveBtn").on("click", function() {
				var n = getConf();
				if (n.config.length <= 0 ){
					top.layer.alert('未选择控件',{icon: 3, title:'系统提示'});
					return;
				}
				var formData = new FormData();
				formData.append("id",$("#id").val());
				formData.append("activeId",$("#activeId").val());
				formData.append("formName",$("#xdj-formName").val());
				formData.append("type",$("#type").val())
				formData.append("formDescribe",$("#xdj-formDescribe").val());
				var config = n.config;
				for (var i = 0; i < config.length; i++) {
					formData.append("custom"+(i+1),JSON.stringify(config[i]));
				}
				$.ajax({
					url:"${ctx}/customer/form/save",
					dataType:"json",
					type:"POST",
					data:formData,
					contentType: false,
					processData: false,
					success:function(date){
						top.layer.alert(date.msg,{icon: 3, title:'系统提示'})
						location.reload();
					},
					error:function(date){
						top.layer.alert(date.msg,{icon: 0, title:'警告'})
						location.reload();
					}
				})
			})

			$("#xdj-yulan").on("click", function() {
				top.layer.open({
					type: 2,
					area: ['400px', '600px'],
					title: '预览',
					maxmin: false, //开启最大化最小化按钮
					content: '${pageContext.request.contextPath}/webpage/modules/active/preview.jsp?id='+$("#activeId").val()+"&userId=",
					btn: ['关闭'],
					cancel: function(index){
					}
				});
			})
			//生成二维码
			function makeCode () {
				var url = "https://chuang.qq.zjgqt.org/static/weui/preview.html?id="+$("#activeId").val()+"&userId=";
				jQuery('#xdj-qrcode').qrcode({width: 200,height: 200,correctLevel:0,text: url});
			}
		</script>
	</body>
</html>

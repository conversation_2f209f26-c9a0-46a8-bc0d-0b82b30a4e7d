<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>

    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <!-- SUMMERNOTE -->
    <link href="${ctxStatic}/summernote/summernote.css" rel="stylesheet">
    <link href="${ctxStatic}/summernote/summernote-bs3.css" rel="stylesheet">

    <link rel="stylesheet" type="text/css" href="${ctxStatic}/webuploader-0.1.5/webuploader.css">
    <link rel="stylesheet" type="text/css" href="${ctxStatic}/webuploader-0.1.5/demo.css">

    <script src="${ctxStatic}/summernote/summernote.min.js"></script>
    <script src="${ctxStatic}/summernote/summernote-zh-CN.js"></script>

    <script type="text/javascript">
        //加载扩展模块
        layer.config({
            extend: 'extend/layer.ext.js'
        });
        layer.ready(function(){
            //使用相册
            layer.photos({
                photos: '.fileImg'
            });
        });

        $(document).ready(function () {
            $(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg img", function (e) {
                var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
                    (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                var imagep = $(".layui-layer-phimg").parent().parent();
                var image = $(".layui-layer-phimg").parent();
                var h = image.height();
                var w = image.width();
                if (delta > 0) {
                    if (h < (window.innerHeight)) {
                        h = h * 1.05;
                        w = w * 1.05;
                    }
                } else if (delta < 0) {
                    if (h > 100) {
                        h = h * 0.95;
                        w = w * 0.95;
                    }
                }
                imagep.css("top", (window.innerHeight - h) / 2);
                imagep.css("left", (window.innerWidth - w) / 2);
                image.height(h);
                image.width(w);
                imagep.height(h);
                imagep.width(w);
            });
        })
    </script>
    <script type="text/javascript" src="${ctxStatic}/webuploader-0.1.5/webuploader.js"></script>
    <style>
        #uploader .queueList {
            margin: 0px;
            border: none;
        }
        #uploader .webuploader-pick{
            padding: 0px;
        }
        #uploader .queueList.webuploader-dnd-over {
            border: none;
        }
    </style>
</head>

<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="row">

        <div class="col-sm-12 animated fadeInRight">
            <div class="mail-box">
                <div class="mail-body">
                    <form:form enctype="multipart/form-data"  id="inputForm" modelAttribute="newsInfo" action="${ctx}/qqc/news/save/${newsInfo.newsType}" method="post" class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>图片：</label>
                            <div class="col-sm-8">
                                <c:if test="${not empty newsInfo.url}">
                                    <div class="fileImg">
                                        <img id="imgUrl" width="200px" src="${newsInfo.url}" />
                                    </div>
                                </c:if>
                                <input type="file" class="form-control" name="imgFile" id="imgFile"><br>
                            </div>
                        </div>
                        <div class="form-group">
                            <form:hidden path="id"/>
                            <label class="col-sm-2 control-label"><font color="red">*</font>标题：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${newsInfo.title}" id="title" name="title"  class="form-control required" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>发布者：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${newsInfo.publisher}" id="publisher" name="publisher"  class="form-control required">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>等级：</label>
                            <div class="col-sm-8">
                                <form:select style="width:150px;" path="type"  class="form-control required m-b">
                                    <form:option value="" label="--请选择--"/>
                                    <form:options items="${fns:getDictList('news_type')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
                                </form:select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>区域：</label>
                            <div class="col-sm-8">
                                <sys:treeSelectMulti id="area" name="areaId" value="${newsInfo.areaId}" labelName="areaName" labelValue="${newsInfo.areaName}"
                                                     title="区域" url="/sys/area/treeData" extId="" cssClass="form-control required m-s" allowClear="true"/>
                            </div>
                        </div>
                        <div id="linkDiv" class="form-group" >
                            <label class="col-sm-2 control-label"><font color="red">*</font>链接：</label>
                            <div class="col-sm-8">
                                <input id="link" name="link" type="text" class="form-control" value="${newsInfo.link}">
                            </div>
                        </div>
                    </form:form>
                </div>
            </div>
        </div>

    </div>
</div>
<script type="text/javascript">
    var validateForm;
    var radioVal;
    function doSubmit(){//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
        if(validateForm.form()){
            $("#inputForm").submit();
            return true;
        }
        return false;
    }
    $(document).ready(function () {
        validateForm = $("#inputForm").validate({
            submitHandler: function(form){
                loading('正在提交，请稍等...');
                form.submit();
            },
            errorContainer: "#messageBox",
            errorPlacement: function(error, element) {
                $("#messageBox").text("输入有误，请先更正。");
                if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
                    error.appendTo(element.parent().parent());
                } else {
                    error.insertAfter(element);
                }
            }
        });

    });
</script>
</body>

</html>
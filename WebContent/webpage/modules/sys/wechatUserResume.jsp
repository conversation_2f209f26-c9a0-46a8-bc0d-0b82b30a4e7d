<%@ taglib prefix="from" uri="http://www.springframework.org/tags/form" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp" %>
<html>
<head>
	<title>简历信息</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
		td a{
			margin-left: 10px;
		}
	</style>
	<script>
		//加载扩展模块
		layer.config({
			extend: 'extend/layer.ext.js'
		});
		layer.ready(function(){
			//使用相册
			layer.photos({
				photos: '#img'
			});
		});

	</script>
</head>
<body>
<form:form id="inputForm" modelAttribute="resume"  class="form-horizontal">
	<table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
		<tbody>
		<tr>
			<td class="active">	<label class="pull-right"><font color="red">*</font>姓名:</label></td>
			<td class="width-35">
				${resume.name}
				<%--<form:input autocomplete="off" path="name" htmlEscape="false" maxlength="200" class="form-control required"/>--%>
			</td>
			<td class="active"><label class="pull-right"><font color="red">*</font>电话:</label></td>
			<td>
				${resume.phone}
				<%--<form:input autocomplete="off" path="phone" htmlEscape="false" maxlength="200" class="form-control required"/>--%>
			</td>
		</tr>
		<tr>
			<td class="active"><label class="pull-right"><font color="red">*</font>邮箱:</label></td>
			<td>
				${resume.email}
				<%--<form:input path="email" htmlEscape="false" maxlength="50" class="form-control required"/>--%>
			</td>
			<td class="active"><label class="pull-right"><font color="red">*</font>性别:</label></td>
			<td>
				${resume.sex}
				<%--<form:select path="sex"  class="form-control m-b">
					<form:option value="" label=""/>
					<form:options items="${fns:getDictList('sex')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
				</form:select>--%>
			</td>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">生日:</label></td>
			<td>
				<fmt:formatDate pattern='yyyy-MM-dd'  value='${resume.birthday}'/>
				<%--<form:input path="birthday" value="" htmlEscape="false" maxlength="50" class="form-control"/>--%>
			</td>
			<td class="active"><label class="pull-right">户籍:</label></td>
			<td>
				${resume.householdRegister}
				<%--<form:input path="householdRegister" htmlEscape="false" maxlength="50" class="form-control "/>--%>
			</td>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">现居地:</label></td>
			<td>
					${resume.residence}
				<%--<form:input path="residence" htmlEscape="false" maxlength="50" class="form-control"/>--%>
			</td>
			<td class="active"><label class="pull-right">政治面貌:</label></td>
			<td>
					${resume.politicalOutlook}
				<%--<form:input path="politicalOutlook" htmlEscape="false" maxlength="50" class="form-control"/>--%>
			</td>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">学历:</label></td>
			<td>
					${resume.education}
				<%--<form:input path="education" htmlEscape="false" maxlength="50" class="form-control"/>--%>
			</td>
			<td class="active"><label class="pull-right">毕业院校:</label></td>
			<td>
					${resume.school}
				<%--<form:input path="school" htmlEscape="false" maxlength="50" class="form-control"/>--%>
			</td>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">所学专业:</label></td>
			<td>
					${resume.major}
				<%--<form:input path="major" htmlEscape="false" maxlength="50" class="form-control"/>--%>
			</td>
			<td class="active"><label class="pull-right">毕业时间:</label></td>
			<td>
				<fmt:formatDate pattern='yyyy-MM-dd'  value='${resume.graduationTime}'/>
				<%--<form:input path="graduationTime" htmlEscape="false" maxlength="50" class="form-control"/>--%>
			</td>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">薪资:</label></td>
			<td colspan="3">
				${resume.salary}元
			</td>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">自我介绍:</label></td>
			<td colspan="3">
				${resume.introduce}
				<%--<from:textarea path="introduce" htmlEscape="false" rows="3" cols="3" class="form-control"/>--%>
			</td>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">图片:</label></td>
			<td colspan="3">
				<div id="img">
					<c:forEach items="${resumeFileList}" var="file">
						<img style="width: 100px;height: 100px" src="${file.file}">
					</c:forEach>
				</div>
			</td>
		</tr>
		</tbody>
	</table>
</form:form>
</body>
</html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>用户管理</title>
	<meta name="decorator" content="default"/>
	<style>
		td a{
			margin-left: 10px;
		}
	</style>
</head>
<body>
	<form:form id="inputForm" modelAttribute="wechatUserinfo" action="" method="post" class="form-horizontal">
		<form:hidden path="id"/>
		<sys:message content="${message}"/>
		<table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
		   <tbody>
		      <tr>
		         <%--<td class="width-15 active">	<label class="pull-right">头像：</label></td>
		         <td class="width-35">

				 </td>--%>
		         <td  class="width-15"  class="active">	<label class="pull-right"><font color="red">*</font>姓名:</label></td>
		         <td class="width-35">
					 <form:input readonly="true" autocomplete="off" path="nickname" htmlEscape="false" maxlength="200" class="form-control required"/>
				 </td>
					 <td class="active"><label class="pull-right"><font color="red">*</font>性别:</label></td>
					 <td>
						 <form:select cssStyle="cursor: pointer" disabled="true" path="sex"  class="form-control required m-b">
							 <form:option value="" label=""/>
							 <form:options items="${fns:getDictList('sex')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
						 </form:select>
					 </td>
		      </tr>

		      <tr>
		         <td class="active"><label class="pull-right"><font color="red">*</font>手机:</label></td>
		         <td>
					 <form:input path="tels" readonly="true" htmlEscape="false" maxlength="50" class="form-control required"/>
				 </td>
		         <td class="active"><label class="pull-right"><font color="red">*</font>身份证号码:</label></td>
		         <td>
					 <form:input path="idCard" readonly="true" htmlEscape="false" maxlength="50" class="form-control required userName"/></td>
		      </tr>

			  <tr>
				  <td class="active"><label class="pull-right">邮箱:</label></td>
				  <td>
					  <form:input path="email"  readonly="true" htmlEscape="false" maxlength="50" class="form-control"/>
				  </td>
				  <td class="active"><label class="pull-right">公司:</label></td>
				  <td>
					  <form:input path="company"  readonly="true" htmlEscape="false" maxlength="50" class="form-control "/></td>
			  </tr>
			  <tr>
				  <td class="active"><label class="pull-right">职位:</label></td>
				  <td>
					  <form:input path="post" readonly="true" htmlEscape="false" maxlength="50" class="form-control"/>
				  </td>
				  <td class="active"><label class="pull-right">是否在职:</label></td>
				  <td>
					  <form:select path="isJob" cssStyle="cursor: pointer" disabled="true"  class="form-control m-b">
						  <form:option value="" label=""/>
						  <form:options items="${fns:getDictList('yes_no')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
					  </form:select>
			  </tr>
			  <tr>
				  <td class="active"><label class="pull-right">办公地点:</label></td>
				  <td>
					  <form:input path="officeLocation" readonly="true" htmlEscape="false" maxlength="50" class="form-control"/>
				  </td>
				  <td class="active"><label class="pull-right">入驻孵化器:</label></td>
				  <td>
					  <form:input path="enterTheIncubator" readonly="true" htmlEscape="false" maxlength="50" class="form-control"/></td>
			  </tr>
			  <tr>
				  <td class="active"><label class="pull-right">高校创业学院:</label></td>
				  <td>
					  <form:input path="collegeOfEntrepreneurship" readonly="true" htmlEscape="false" maxlength="50" class="form-control"/>
				  </td>
				  <td class="active"><label class="pull-right">创业阶段:</label></td>
				  <td>
					  <form:select path="entrepreneurialStage" cssStyle="cursor: pointer" disabled="true"  class="form-control m-b">
						  <form:option value="" label=""/>
						  <form:options items="${fns:getDictList('qqc_entrepreneurial_stage')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
					  </form:select>
			  </tr>
			  <tr>
				  <td class="active"><label class="pull-right">个人简介:</label></td>
				  <td colspan="3">
					  <form:textarea path="personalProfile" readonly="true" htmlEscape="false" rows="3" cols="3" class="form-control"/>
				  </td>
			  </tr>
			  <tr>
				  <td class="active"><label class="pull-right">个人简介:</label></td>
				  <td colspan="3">
					  <c:forEach items="${wechatUserinfo.resumes}" var="resume" varStatus="index">
						  <a href="#" onclick="openDialog('查看简历', '${ctx}/wechatuser/resume/from?id=${resume.id}','800px', '680px')">查看简历${index.index+1}</a>
					  </c:forEach>
				  </td>
			  </tr>
	</form:form>
</body>
</html>
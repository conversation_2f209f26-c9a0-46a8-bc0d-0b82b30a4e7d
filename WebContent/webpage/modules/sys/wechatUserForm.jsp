<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp" %>
<html>
<head>
	<title>岗位信息</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
		td a{
			margin-left: 10px;
		}
	</style>
	<script type="text/javascript">
		var validateForm;
		function doSubmit() {//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
			if (validateForm.form()) {
				$("#inputForm").submit();
				return true;
			}
			return false;
		}

		$(document).ready(function () {
			//$("#name").focus();
			validateForm = $("#inputForm").validate({
				submitHandler: function (form) {
					loading('正在提交，请稍等...');
					form.submit();
				},
				errorContainer: "#messageBox",
				errorPlacement: function (error, element) {
					console.log(element);
					$("#messageBox").text("输入有误，请先更正。");
					if (element.is(":checkbox") || element.is(":radio") || element.parent().is(".input-append")) {
						error.appendTo(element.parent().parent());
					} else {
						error.insertAfter(element);
					}
				}
			});
		});
	</script>
</head>
<body>
<form:form id="inputForm" modelAttribute="wechatUserinfo"  action="${ctx}/sys/wechatuser/save" method="post"
		   class="form-horizontal">
	<form:hidden path="id"/>
	<sys:message content="${message}"/>
	<table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
		<tbody>
		<tr>
			<%--<td class="width-15 active">	<label class="pull-right">头像：</label></td>
			<td class="width-35">

			</td>--%>
			<td  class="width-15"  class="active">	<label class="pull-right"><font color="red">*</font>姓名:</label></td>
			<td class="width-35">
				<form:input autocomplete="off" path="nickname" htmlEscape="false" maxlength="200" class="form-control required"/>
			</td>
				<td class="active"><label class="pull-right"><font color="red">*</font>性别:</label></td>
				<td>
					<form:select path="sex"  class="form-control required m-b">
						<form:option value="" label=""/>
						<form:options items="${fns:getDictList('sex')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
					</form:select>
				</td>
		</tr>

		<tr>
			<td class="active"><label class="pull-right"><font color="red">*</font>手机:</label></td>
			<td>
				<form:input path="tels" htmlEscape="false" maxlength="50" class="form-control required"/>
			</td>
			<td class="active"><label class="pull-right"><font color="red">*</font>身份证号码:</label></td>
			<td>
				<form:input path="idCard" htmlEscape="false" maxlength="50" class="form-control required userName"/></td>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">邮箱:</label></td>
			<td>
				<form:input path="email" htmlEscape="false" maxlength="50" class="form-control"/>
			</td>
			<td class="active"><label class="pull-right">公司:</label></td>
			<td>
				<form:input path="company" htmlEscape="false" maxlength="50" class="form-control "/></td>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">职位:</label></td>
			<td>
				<form:input path="post" htmlEscape="false" maxlength="50" class="form-control"/>
			</td>
			<td class="active"><label class="pull-right">是否在职:</label></td>
			<td>
				<form:select path="isJob"  class="form-control m-b">
					<form:option value="" label=""/>
					<form:options items="${fns:getDictList('yes_no')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
				</form:select>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">办公地点:</label></td>
			<td>
				<form:input path="officeLocation" htmlEscape="false" maxlength="50" class="form-control"/>
			</td>
			<td class="active"><label class="pull-right">入驻孵化器:</label></td>
			<td>
				<form:input path="enterTheIncubator" htmlEscape="false" maxlength="50" class="form-control"/></td>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">高校创业学院:</label></td>
			<td>
				<form:input path="collegeOfEntrepreneurship" htmlEscape="false" maxlength="50" class="form-control"/>
			</td>
			<td class="active"><label class="pull-right">创业阶段:</label></td>
			<td>
				<form:select path="entrepreneurialStage"  class="form-control m-b">
					<form:option value="" label=""/>
					<form:options items="${fns:getDictList('qqc_entrepreneurial_stage')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
				</form:select>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">个人简介:</label></td>
			<td colspan="3">
				<form:textarea path="personalProfile" htmlEscape="false" rows="3" cols="3" class="form-control"/>
			</td>
		</tr>
		<tr>
			<td class="active"><label class="pull-right">个人简介:</label></td>
			<td colspan="3">
				<c:forEach items="${wechatUserinfo.resumes}" var="resume" varStatus="index">
					<a href="#" onclick="openDialog('查看简历', '${ctx}/wechatuser/resume/from?id=${resume.id}','800px', '680px')">查看简历${index.index+1}</a>
				</c:forEach>
			</td>
		</tr>
		</tbody>
	</table>
</form:form>
</body>
</html>
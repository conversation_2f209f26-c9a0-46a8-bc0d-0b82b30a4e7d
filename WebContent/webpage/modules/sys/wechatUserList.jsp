<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
    <title>微信用户信息</title>
    <meta name="decorator" content="default"/>
    <style type="text/css">
        #img{
            height: 20px;
            width: 20px;
            margin: 0 auto;
            vertical-align:bottom;
        }
    </style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="ibox">
        <div class="ibox-title">
            <h5>微信用户信息 </h5>
            <div class="ibox-tools">
                <a class="collapse-link">
                    <i class="fa fa-chevron-up"></i>
                </a>
                <a class="close-link">
                    <i class="fa fa-times"></i>
                </a>
            </div>
        </div>

        <div class="ibox-content">
            <sys:message content="${message}"/>

            <!-- 查询条件 -->
            <div class="row">
                <div class="col-sm-12">
                    <form:form id="searchForm" modelAttribute="wechatUserinfo" action="${ctx}/sys/wechatuser/list" method="post" class="form-inline">
                        <input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
                        <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                        <div class="form-group">
                            <span>姓&nbsp;&nbsp;&nbsp;名：</span>
                            <form:input path="nickname" autocomplete="off" htmlEscape="false" maxlength="50" class=" form-control input-sm"/>
                            <span>性&nbsp;&nbsp;&nbsp;别：</span>
                            <form:select path="sex"  class="form-control">
                                <form:option value="" label=""/>
                                <form:options items="${fns:getDictList('sex')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
                            </form:select>
                            <span>是否在职：</span>
                            <form:select path="isJob"  class="form-control">
                                <form:option value="" label=""/>
                                <form:options items="${fns:getDictList('yes_no')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
                            </form:select>
                        </div>
                    </form:form>
                    <br/>
                </div>
            </div>

            <!-- 工具栏 -->
            <div class="row">
                <div class="col-sm-12">
                    <div class="pull-left">
                        <shiro:hasPermission name="sys:wechatuser:del">
                            <table:delRow url="${ctx}/sys/wechatuser/deleteAll" id="contentTable"></table:delRow><!-- 删除按钮 -->
                        </shiro:hasPermission>
                    </div>
                    <div class="pull-right">
                        <button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
                        <button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
                    </div>
                </div>
            </div>

            <table id="contentTable" class="table table-striped table-bordered table-hover table-condensed dataTables-example dataTable">
                <thead>
                <tr>
                    <th><input type="checkbox" class="i-checks"></th>
                    <th>姓名</th>
                    <th>性别</th>
                    <th>用户手机号</th>
                    <th>用户身份证号</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${page.list}" var="wechatUset">
                    <tr>
                        <td> <input type="checkbox" id="${wechatUset.openid}" class="i-checks"></td>
                        <td>
                            <a href="#" onclick="openDialogView('查看用户', '${ctx}/sys/wechatuser/form?type=View&id=${wechatUset.id}','800px', '680px')">
                                    ${wechatUset.nickname}</a>
                        </td>
                        <td>${fns:getDictLabel(wechatUset.sex, 'sex', '')}</td>
                        <td>${wechatUset.tels}</td>
                        <td>${wechatUset.idCard}</td>
                        <td>
                            <shiro:hasPermission name="sys:wechatuser:view">
                                <a title="查看信息" href="#" onclick="openDialogView('查看用户', '${ctx}/sys/wechatuser/form?type=View&id=${wechatUset.id}','800px', '680px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
                            </shiro:hasPermission>
                            <shiro:hasPermission name="sys:wechatuser:edit">
                                <a title="修改信息" href="#" onclick="openDialog('修改用户', '${ctx}/sys/wechatuser/form?type=Form&id=${wechatUset.id}','800px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
                            </shiro:hasPermission>
                            <shiro:hasPermission name="sys:wechatuser:del">
                                <a title="删除信息" href="${ctx}/sys/wechatuser/delete?id=${wechatUset.id}" onclick="return confirmx('确认要删除该用户吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
                            </shiro:hasPermission>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
            <table:page page="${page}"></table:page>
            <br/>
            <br/>
        </div>
    </div>
</div>
</body>
</html>
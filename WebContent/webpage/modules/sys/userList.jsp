<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>用户管理</title>
	<meta name="decorator" content="default"/>
    <script>
        function selectUser(){
            var id="";
            var name="";
            var checked = 0;
            $("#contentTable tbody tr td input.i-checks:checkbox").each(function(){
                if(true == $(this).is(':checked')){
                    checked ++;
                    id = $(this).attr("id");
                    name = $(this).attr("name");
                }
            })
            if(checked > 1){
                top.layer.alert('只能选择一个专家!', {icon: 0, title:'警告'});
                return;
            }
            if(id == ""){
                top.layer.alert('请至少选择一条数据!', {icon: 0, title:'警告'});
                return;
            }
            var myMap = {
                "id":id,
                "name":name
            };
            return myMap;
        }
    </script>
</head>
<body>
    <sys:message content="${message}"/>
		<!-- 查询条件 -->
	<div class="row">
	<div class="col-sm-12">
	<form:form id="searchForm" modelAttribute="user" action="${ctx}/sys/user/list/edit" method="post" class="form-inline">
		<input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
        <input type="hidden" name="userType" value="${user.userType}">
		<table:sortColumn id="orderBy" name="orderBy" value="${page.orderBy}" callback="sortOrRefresh();"/><!-- 支持排序 -->
		<div style="margin-top: 10px;margin-left: 10px" class="form-group">
			<%--<span>归属公司：</span>
				<sys:treeselect id="company" name="company.id" value="${user.company.id}" labelName="company.name" labelValue="${user.company.name}" 
				title="公司" url="/sys/office/treeData?type=1" cssClass=" form-control input-sm" allowClear="true"/>--%>
			<span>登录名：</span>
				<form:input path="loginName" htmlEscape="false" maxlength="50" class=" form-control input-sm" value="${loginName}"/>
            <span>姓&nbsp;&nbsp;&nbsp;名：</span>
                <form:input path="name" htmlEscape="false" maxlength="50" class=" form-control input-sm"/>
			<%--<span>归属部门：</span>
				<sys:treeselect id="office" name="office.id" value="${user.office.id}" labelName="office.name" labelValue="${user.office.name}" 
				title="部门" url="/sys/office/treeData?type=2" cssClass=" form-control input-sm" allowClear="true" notAllowSelectParent="true"/>--%>
		 </div>
		<div style="margin-top: 10px" class="form-group">

		</div>
	</form:form>
	<br/>
	</div>
	</div>
	
	<!-- 工具栏 -->
	<div class="row">
	<div class="col-sm-12">
		<div class="pull-left">
            <c:if test="${mode == 'edit' || mode=='act'}">
                <shiro:hasPermission name="sys:user:add">
                    <table:addRow url="${ctx}/sys/user/form" title="用户" width="800px" height="620px" target="officeContent"></table:addRow><!-- 增加按钮 -->
                </shiro:hasPermission>
                <shiro:hasPermission name="sys:user:edit">
                    <table:editRow url="${ctx}/sys/user/form" id="contentTable"  title="用户" width="800px" height="680px" target="officeContent"></table:editRow><!-- 编辑按钮 -->
                </shiro:hasPermission>
                <shiro:hasPermission name="sys:user:del">
                    <table:delRow url="${ctx}/sys/user/deleteAll" id="contentTable"></table:delRow><!-- 删除按钮 -->
                </shiro:hasPermission>
                <shiro:hasPermission name="sys:user:import">
                    <table:importExcel url="${ctx}/sys/user/import"></table:importExcel><!-- 导入按钮 -->
                </shiro:hasPermission>
                <shiro:hasPermission name="sys:user:export">
                    <table:exportExcel url="${ctx}/sys/user/export"></table:exportExcel><!-- 导出按钮 -->
               </shiro:hasPermission>
               <button class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" onclick="sortOrRefresh()" title="刷新"><i class="glyphicon glyphicon-repeat"></i> 刷新</button>
            </c:if>
			</div>
		<div class="pull-right">
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
		</div>
	</div>
	</div>
	
	<table id="contentTable" class="table table-striped table-bordered table-hover table-condensed dataTables-example dataTable">
		<thead>
			<tr>
				<th><input type="checkbox" class="i-checks"></th>
				<th class="sort-column login_name">登录名</th>
				<th class="sort-column name">姓名</th>
				<th class="sort-column indexaddressName">地址</th>
				<th class="sort-column phone">电话</th>
				<th class="sort-column mobile">手机</th>
				<th class="sort-column c.name">归属公司</th>
				<th class="sort-column o.name">归属部门</th>
                <c:if test="${mode == 'edit'}">
				    <th>操作</th>
                </c:if>
			</tr>
		</thead>
		<tbody>
		<c:forEach items="${page.list}" var="user">
			<tr>
				<td> <input type="checkbox" name="${user.name}" id="${user.id}" class="i-checks"></td>
				<td><a  href="#" onclick="openDialogView('查看用户', '${ctx}/sys/user/form?id=${user.id}','800px', '680px')">${user.loginName}</a></td>
				<td>${user.name}</td>
				<td>${user.indexaddressName}</td>
				<td>${user.phone}</td>
				<td>${user.mobile}</td>
				<td>${user.company.name}</td>
				<td>${user.office.name}</td>
                <c:if test="${mode == 'edit'}">
                    <td>
                        <shiro:hasPermission name="sys:user:view">
                            <a href="#" onclick="openDialogView('查看用户', '${ctx}/sys/user/form?id=${user.id}','800px', '680px')" class="btn btn-info btn-xs" ><i class="fa fa-search-plus"></i> 查看</a>
                        </shiro:hasPermission>
                        <shiro:hasPermission name="sys:user:edit">
                            <a href="#" onclick="openDialog('修改用户', '${ctx}/sys/user/form?id=${user.id}','800px', '700px', 'officeContent')" class="btn btn-success btn-xs" ><i class="fa fa-edit"></i> 修改</a>
                        </shiro:hasPermission>
                        <shiro:hasPermission name="sys:user:del">
                            <a href="${ctx}/sys/user/delete?id=${user.id}" onclick="return confirmx('确认要删除该用户吗？', this.href)" class="btn btn-danger btn-xs"><i class="fa fa-trash"></i> 删除</a>
                        </shiro:hasPermission>
                    </td>
                </c:if>
			</tr>
		</c:forEach>
		</tbody>
	</table>
	<table:page page="${page}"></table:page>
	
</body>
</html>
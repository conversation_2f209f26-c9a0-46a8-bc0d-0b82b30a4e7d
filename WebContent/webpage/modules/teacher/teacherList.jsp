<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>导师列表</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
	</style>
    <script src="${ctxStatic}/weui/js/jquery.qrcode.min.js"></script>
	<script type="text/javascript">
        $(document).ready(function() {
            $(".qrcode").click();
			$("#uploadFile").on('change', function () {
                var filePath = this.value;
                var fileExt = filePath.substring(filePath.lastIndexOf("."))
                    .toLowerCase();
                if (!checkFileExt(fileExt)) {
					top.layer.alert('您上传的文件不是Excel文件,请重新上传！', {icon: 0, title:'警告'});
					this.value = "";
                    return;
                }
                if (this.files && this.files[0]) {
					var num = Number(this.files[0].size)/1048576;
					if (this.files[0].size > ${fileMaxSize}){
						top.layer.alert('当前上传文件的大小为:'+num.toFixed(2)+'MB,文件最大不能超过'+(Number(${fileMaxSize})/1048576)+'MB', {icon: 0, title:'警告'});
						return false;
					}
                }
				$("#importForm").submit();
				loading('正在导入，请稍等...');
			})
        });

        function checkFileExt(ext) {
            if (!ext.match(/.xlsx|.xls/i)) {
                return false;
            }
            return true;
        }

        //生成二维码
        function makeCode (obj,idCard) {
            jQuery(obj).empty();
            jQuery(obj).qrcode({width: 50,height: 50,correctLevel:0,text: idCard});
        }
		function selectTeacher(){
			var id="";
			var name="";
			var checked = 0;
			$("#newsTable tbody tr td input.i-checks:checkbox").each(function(){
				if(true == $(this).is(':checked')){
					checked ++;
					id = $(this).attr("id");
					name = $(this).attr("name");
				}
			})
			if(checked > 1){
				top.layer.alert('只能选择一名导师!', {icon: 0, title:'警告'});
				return;
			}
			if(id == ""){
				top.layer.alert('请至少选择一条数据!', {icon: 0, title:'警告'});
				return;
			}
			var myMap = {
				"id":id,
				"name":name
			};
			return myMap;
		}

		function importTeacher(){
			$("#uploadFile").click();
		}

	</script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
<div class="ibox">
<div class="ibox-title">
		<h5>导师信息 </h5>
		<div class="ibox-tools">
			<a class="collapse-link">
				<i class="fa fa-chevron-up"></i>
			</a>
			<a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
				<i class="fa fa-wrench"></i>
			</a>
			<ul class="dropdown-menu dropdown-user">
				<li><a href="#">选项1</a>
				</li>
				<li><a href="#">选项2</a>
				</li>
			</ul>
			<a class="close-link">
				<i class="fa fa-times"></i>
			</a>
		</div>
	</div>
    
    <div class="ibox-content">
	<sys:message content="${message}"/>
	
	<!-- 查询条件 -->
	<div class="row">
	<div class="col-sm-12">
	<form:form id="searchForm" modelAttribute="teacher" action="" method="post" class="form-inline">
		<input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
		<div class="form-group">
			<span>姓名：&nbsp;</span>
			<form:input path="name" cssStyle="cursor: pointer" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
		 </div>
	</form:form>
	<br/>
	</div>
	</div>
	
	
	<!-- 工具栏 -->
	<div class="row">
	<div class="col-sm-12">
		<div class="pull-left">
			<button id="btnImport" class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" title="下载模板">
				<a href="https://chuang.qq.zjgqt.org/model/qqchuang_teacher_model.xlsx">
					<i class="fa fa-folder-open-o"></i> 下载模板
				</a>

			</button>
			<button id="btnImport" onclick="importTeacher()" class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" title="导入"><i class="fa fa-folder-open-o"></i> 导入</button>
			<div id="importBox" class="hide">
				<form id="importForm" action="${ctx}/qqc/teacher/teacherImport" method="post" enctype="multipart/form-data"
					  style="padding-left:20px;text-align:center;" ><br/>
					<input id="uploadFile" name="file" type="file" style="width:330px"/>导入文件不能超过100M，仅允许导入“xls”或“xlsx”格式文件！<br/>　　
				</form>
			</div>
			<%--<shiro:hasPermission name="qqc:teacher:add">--%>
				<table:addRow width="1000px" height="700px" url="${ctx}/qqc/teacher/form" title="导师"></table:addRow><!-- 增加按钮 -->
			<%--</shiro:hasPermission>--%>
			<%--<shiro:hasPermission name="qqc:teacher:del">--%>
				<table:delRow url="${ctx}/qqc/teacher/deleteAll" id="newsTable"></table:delRow><!-- 删除按钮 -->
			<%--</shiro:hasPermission>--%>
				<%-- <table:importExcel url="${ctx}/qqc/companyInfo/import"></table:importExcel><!-- 导入按钮 -->
	       		<table:exportExcel url="${ctx}/qqc/companyInfo/export"></table:exportExcel><!-- 导出按钮 --> --%>
	       		<!-- <button class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" onclick="sortOrRefresh()" title="刷新"><i class="glyphicon glyphicon-repeat"></i> 刷新</button> -->
			</div>
		<div class="pull-right">
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
		</div>
	</div>
	</div>
	
	
	
	
	<table id="newsTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
		<thead>
			<tr>
				<th> <input type="checkbox" class="i-checks"></th>
                <td>亲青码</td>
				<th>姓名</th>
				<th>公司</th>
				<th>职位</th>
				<th>擅长指导</th>
				<th>投资领域</th>
				<th>曾获荣誉</th>
				<th>操作</th>
			</tr>
		</thead>
		<tbody>
		<c:forEach items="${page.list}" var="teacher">
			<tr>
				<td> <input type="checkbox" id="${teacher.id}" name="${teacher.name}" class="i-checks"></td>
                <td>
                    <div class="qrcode" onclick="makeCode(this,'${fns:entryptIdCard(teacher.id)}')" style="width:50px; height:50px; margin-top:15px;"></div>
                </td>
				<td><a href="#" onclick="openDialogView('查看新闻', '${ctx}/qqc/teacher/form?id=${teacher.id}','1000px', '700px')">
					${fns:abbr(teacher.name,50)}
				</a></td>
				<td>
					${fns:abbr(teacher.company,50)}
				</td>
				<td>
					${fns:abbr(teacher.post,50)}
				</td>
				<td>
					${fns:abbr(teacher.skill,50)}
				</td>
				<td>
					${fns:abbr(teacher.investmentAreas,50)}
				</td>
				<td>
					${fns:abbr(teacher.honors,50)}
				</td>
				<td>
					 <%--<shiro:hasPermission name="qqc:teacher:view">--%>
						<a title="查看信息" href="#" onclick="openDialogView('查看信息', '${ctx}/qqc/teacher/form?type=View&id=${teacher.id}','1000px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
					 <%--</shiro:hasPermission>--%>
					 <%--<shiro:hasPermission name="qqc:teacher:edit">--%>
    					<a title="修改信息" href="#" onclick="openDialog('修改信息', '${ctx}/qqc/teacher/form?id=${teacher.id}','1000px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
    				<%-- </shiro:hasPermission>--%>
    				<%-- <shiro:hasPermission name="qqc:teacher:del">--%>
						<a title="删除信息" href="${ctx}/qqc/teacher/delete?id=${teacher.id}" onclick="return confirmx('确认要删除该信息吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
					 <%--</shiro:hasPermission>--%>
				</td>
			</tr>
		</c:forEach>
		</tbody>
	</table>
	<!-- 分页代码 -->
	<table:page page="${page}"></table:page>
	<br/>
	<br/>
	</div>
	</div>
</div>
</body>
</html>
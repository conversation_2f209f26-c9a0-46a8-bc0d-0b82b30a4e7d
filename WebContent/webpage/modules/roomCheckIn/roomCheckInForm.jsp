<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
    <title>空间入住新增</title>
    <meta name="decorator" content="default"/>
    <link href="${ctxStatic}/summernote/summernote.css" rel="stylesheet">
    <link href="${ctxStatic}/summernote/summernote-bs3.css" rel="stylesheet">

    <script src="${ctxStatic}/summernote/summernote.min.js"></script>
    <script src="${ctxStatic}/summernote/summernote-zh-CN.js"></script>
    <link rel="stylesheet" type="text/css" href="${ctxStatic}/webuploader-0.1.5/webuploader.css">
    <link rel="stylesheet" type="text/css" href="${ctxStatic}/webuploader-0.1.5/demo.css">
    <script type="text/javascript" src="${ctxStatic}/webuploader-0.1.5/webuploader.js"></script>
    <style>
        #uploader .queueList {
            margin: 0px;
            border: none;
        }
        #uploader .webuploader-pick{
            padding: 0px;
        }
        #uploader .queueList.webuploader-dnd-over {
            border: none;
        }
    </style>
    <script type="text/javascript">
        // 添加全局站点信息
        var BASE_URL = '/webuploader';
        var validateForm;
        function doSubmit(){//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
            if(validateForm.form()){
                $("#inputForm").submit();
                return true;
            }
            return false;
        }
        $(document).ready(function() {
            $("#summernote").summernote({
                height: 400,
                minHeight: 300,
                maxHeight: 500,
                lang:'zh-CN',
            });
            //$("#name").focus();
            validateForm = $("#inputForm").validate({
                submitHandler: function(form){
                    var str= $('#summernote').code();
                    $("#produce").val(str);
                    loading('正在提交，请稍等...');
                    form.submit();
                },
                errorContainer: "#messageBox",
                errorPlacement: function(error, element) {
                    $("#messageBox").text("输入有误，请先更正。");
                    if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
                        error.appendTo(element.parent().parent());
                    } else {
                        error.insertAfter(element);
                    }
                }
            });
        });

    </script>
</head>
<body>
<form:form id="inputForm" modelAttribute="roomCheckIn" action="${ctx}/qqc/roomcheckin/save" method="post" class="form-horizontal">
    <form:hidden path="id"/>
    <sys:message content="${message}"/>
    <table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
        <tbody>
        <tr>
            <td  class="width-15 active">	<label class="pull-right"><font color="red">*</font>空间名称：</label></td>
            <td class="width-35" >
                <form:input autocomplete="off" path="roomName" htmlEscape="false" maxlength="200" class="form-control required"/>
            </td>
                <td  class="width-15 active">	<label class="pull-right">图片：</label></td>
                    <%-- <input style="display: none" value="${roomCheckIn.url}" name="url" id="url"> --%>
                <td>
                    <div class="col-sm-8">
                    <%-- <div id="uploader" class="wu-example">
                            <div class="queueList">
                                <div id="dndArea" class="img">
                                    <div id="filePicker">
                                        <c:if test="${roomCheckIn.url != null}">
                                            <img style="width: 110px;height: 110px" src="${roomCheckIn.url}">
                                        </c:if>
                                        <c:if test="${roomCheckIn.url == null}">
                                            <img style="width: 110px;height: 110px" src="${ctxStatic}/img/addpic.jpg">
                                        </c:if>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <font color="red">(如以上传图片可以点击图片重新上传)</font> --%>
                        <form:hidden  path="url" htmlEscape="false"  maxlength="255" class="form-control "/>
						<sys:ckfinder input="url" type="images" uploadPath="/qqchuang/roomCheckin"  selectMultiple="false" enableJcrop="true" />
                    </div>
            </td>
        </tr>
        <tr>
            <td  class="width-15 active">	<label class="pull-right"><font color="red">*</font>区域：</label></td>
            <td  class="width-35" >
                <sys:treeSelectMulti id="area" name="area.Id" value="${roomCheckIn.area.id}" labelName="areaName" labelValue="${roomCheckIn.area.name}"
                                     title="区域" url="/sys/area/treeData" extId="" cssClass="form-control required m-s" allowClear="true"/>
            </td>
            <td  class="width-15 active">	<label class="pull-right"><font color="red">*</font>工作人员电话：</label></td>
            <td  class="width-35" >
                <form:input autocomplete="off" path="responsibleCellPhone" htmlEscape="false" maxlength="200" class="form-control required"/>
            </td>


        </tr>
        <tr>
            <td  class="width-15 active">	<label class="pull-right"><font color="red">*</font>详细地址：</label></td>
            <td  class="width-35" >
                <form:input autocomplete="off" path="roomAddress" htmlEscape="false" maxlength="200" class="form-control required"/>
            </td>
            <td  class="width-15 active">	<label class="pull-right">产业范围：</label></td>
            <td  class="width-35">
                <form:input autocomplete="off" path="industryScope" htmlEscape="false" maxlength="200" class="form-control"/>
            </td>
        </tr>
        <tr>
            <td  class="width-15 active">	<label class="pull-right">介绍：</label></td>
            <td colspan="3" class="width-35" >
                <form:textarea cssStyle="display: none" path="produce" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
                <div style="width: 100%" class="col-sm-8">
                    <div class="mail-text">
                        <div id="summernote" class="summernote">
                                ${roomCheckIn.produce}
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <%--<form:textarea path="produce" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>--%>
            </td>
        </tr>
        </tbody>
    </table>
</form:form>

<script type="text/javascript">
    jQuery(function() {
        var $ = jQuery,    // just in case. Make sure it's not an other libaray.

            $wrap = $('#uploader'),
            // 图片容器
            $queue = $('<ul class="filelist"></ul>')
                .appendTo( $wrap.find('.queueList') ),


            // 没选择文件之前的内容。
            $img = $wrap.find('.img'),

            // 优化retina, 在retina下这个值是2
            ratio = window.devicePixelRatio || 1,

            // 缩略图大小
            thumbnailWidth = 110 * ratio,
            thumbnailHeight = 110 * ratio,

            // 所有文件的进度信息，key为file id
            percentages = {},

            //图片选择初始化
            supportTransition = (function(){
                var s = document.createElement('p').style,
                    r = 'transition' in s ||
                        'WebkitTransition' in s ||
                        'MozTransition' in s ||
                        'msTransition' in s ||
                        'OTransition' in s;
                s = null;
                return r;
            })(),

            // WebUploader实例
            uploader;
        if ( !WebUploader.Uploader.support() ) {
            alert( 'Web Uploader 不支持您的浏览器！如果你使用的是IE浏览器，请尝试升级 flash 播放器');
            throw new Error( 'WebUploader does not support the browser you are using.' );
        }

        // 实例化
        uploader = WebUploader.create({
            pick: {
                id: '#filePicker',
            },
            dnd: '#uploader .queueList',
            paste: document.body,

            accept: {
                title: 'Images',
                extensions: 'gif,jpg,jpeg,bmp,png',
                mimeTypes: 'image/*'
            },
            // swf文件路径
            swf: BASE_URL + '/js/Uploader.swf',
            disableGlobalDnd: true,

            chunked: true,
            server: '${ctx}/qqc/teacher/imageUpload',
            fileNumLimit: 1,//一次最多上传多少张照片
        });

        // 当有文件添加进来时执行，负责view的创建
        function addFile( file ) {
            var $li = $( '<li id="' + file.id + '">' +
                '<p class="imgWrap"></p>'+
                '<p style="display:none;" class="title">' + file.name + '</p>' +
                '</li>' ),
                $btns = $('<div class="file-panel">' +
                    '<span title="删除" class="cancel">删除</span>' +
                    '<span title="向右旋转" class="rotateRight">向右旋转</span>' +
                    '<span title="向左旋转" class="rotateLeft">向左旋转</span></div>').appendTo( $li ),
                $wrap = $li.find( 'p.imgWrap' ),
                showError = function( code ) {
                    switch( code ) {
                        case 'exceed_size':
                            text = '文件大小超出';
                            break;
                        case 'interrupt':
                            text = '上传暂停';
                            break;
                        case 'Q_TYPE_DENIED':
                            text = '不支持该格式';
                            break;
                        default:
                            text = '上传失败，请重试';
                            break;
                    }
                };
            if ( file.getStatus() === 'invalid' ) {
                showError( file.statusText );
            } else {
                // @todo lazyload
                $wrap.text( '预览中' );
                uploader.makeThumb( file, function( error, src ) {
                    if ( error ) {
                        $wrap.text( '不能预览' );
                        return;
                    }
                    var width = file._info.width;
                    var height = file._info.height;
                    var img = $("<img style='width: "+width+";height: "+height+"' src="+src+">");
                    $wrap.empty().append( img );
                }, thumbnailWidth, thumbnailHeight );
                percentages[ file.id ] = [ file.size, 0 ];
                file.rotation = 0;
            }

            $li.on( 'mouseenter', function() {
                $btns.stop().animate({height: 30});
            });

            $li.on( 'mouseleave', function() {
                $btns.stop().animate({height: 0});
            });

            $btns.on( 'click', 'span', function() {
                var index = $(this).index(),
                    deg;
                switch ( index ) {
                    case 0:
                        uploader.removeFile( file );
                        return;
                    case 1:
                        file.rotation += 90;
                        break;
                    case 2:
                        file.rotation -= 90;
                        break;
                }
                if ( supportTransition ) {
                    deg = 'rotate(' + file.rotation + 'deg)';
                    $wrap.css({
                        '-webkit-transform': deg,
                        '-mos-transform': deg,
                        '-o-transform': deg,
                        'transform': deg
                    });
                } else {
                    $wrap.css( 'filter', 'progid:DXImageTransform.Microsoft.BasicImage(rotation='+ (~~((file.rotation/90)%4 + 4)%4) +')');
                }
            });
            $li.appendTo( $queue );
        }
        // 负责view的销毁
        function removeFile( file ) {
            var $li = $('#'+file.id);
            delete percentages[ file.id ];
            $li.off().find('.file-panel').off().end().remove();
        }
        //图片添加
        uploader.onFileQueued = function( file ) {
            $img.addClass( 'element-invisible' );
            uploader.upload();
        };

        //图片添加成功后回显
        uploader.on('uploadSuccess', function (file, response) {
            var imgurl = response._raw; //上传图片的路径
            addFile(file);
            $("#url").val(imgurl);
        });

        //图片删除
        uploader.onFileDequeued = function( file ) {
            $img.removeClass( 'element-invisible' );
            removeFile( file );
        };

        //图片上传错误信息
        uploader.onError = function( code ) {
            var text;
            switch( code ) {
                case 'exceed_size':
                    text = '文件大小超出';
                    break;
                case 'interrupt':
                    text = '上传暂停';
                    break;
                case 'Q_TYPE_DENIED':
                    text = '不支持该格式';
                    break;
                default:
                    text = '上传失败，请重试';
                    break;
            }
            alert("Eroor: " + text );
        };
    });
</script>
</body>
</html>
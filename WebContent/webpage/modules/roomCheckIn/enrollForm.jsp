<%@ taglib prefix="from" uri="http://www.springframework.org/tags/form" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>报名详情</title>
    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <link rel="stylesheet" href="${ctxStatic}/weui/css/table-css.css">
</head>
<body class="gray-bg">
<form:form method="post" class="form-horizontal">
    <table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
        <tbody>
        <tr>
            <td  class="width-15 active">
                <label class="pull-right">空间名称：</label>
            </td>
            <td colspan="3" class="width-35" >
                <input type="text" readonly value="${enroll.roomCheckIn.roomName}" class="form-control">
            </td>
            <td  class="width-15 active">
                <label class="pull-right">产业范围：</label>
            </td>
            <td colspan="3" class="width-35" >
                <input type="text" readonly value="${enroll.roomCheckIn.industryScope}" class="form-control">
            </td>
        </tr>
        </tbody>
    </table>
</form:form>
<div align="center">
    <h3>报名信息</h3>
    <table>
        <tbody>
        <c:forEach items="${configList}" var="config">
            <tr>
                <td data-label="${config.title}">${config.title}</td>
                <td data-label="${config.value}">
                    <c:if test="${config.title == '图片'}">
                          <img width="200px" height="200px" src="${config.value}">
                    </c:if>
                    <c:if test="${config.title != '图片'}">
                        ${config.value}
                    </c:if>
                </td>
            </tr>
        </c:forEach>
        </tbody>
    </table>
</div>
</body>
<script>
</script>
</html>
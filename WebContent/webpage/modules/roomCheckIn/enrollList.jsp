<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>报名列表</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
		.btn-default{
			color: #333 !important;
			background-color: #fff !important;
			border-color: #ccc !important;
		}
	</style>
	<script type="text/javascript">
		$(document).ready(function(){
			laydate({
				elem: '#beginDate',
				format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
				festival: true, //显示节日
				istime: false, //是否开启时间选择<br/>
				isclear: true, //是否显示清空<br/>
				istoday: true, //是否显示今天<br/>
				choose: function(datas){ //选择日期完毕的回调
					$("#beginDate").val(datas+" 00:00:00")
				}
			});
			laydate({
				elem: '#endDate',
				format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
				festival: true, //显示节日
				istime: false, //是否开启时间选择<br/>
				isclear: true, //是否显示清空<br/>
				istoday: true, //是否显示今天<br/>
				choose: function(datas){ //选择日期完毕的回调
					$("#endDate").val(datas+" 23:59:59")
				}
			});
		})
	</script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
<div class="ibox">
<div class="ibox-title">
		<h5>报名信息 </h5>
		<div class="ibox-tools">
			<a class="collapse-link">
				<i class="fa fa-chevron-up"></i>
			</a>
			<a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
				<i class="fa fa-wrench"></i>
			</a>
			<ul class="dropdown-menu dropdown-user">
				<li><a href="#">选项1</a>
				</li>
				<li><a href="#">选项2</a>
				</li>
			</ul>
			<a class="close-link">
				<i class="fa fa-times"></i>
			</a>
		</div>
	</div>
    
    <div class="ibox-content">
	<sys:message content="${message}"/>
	
	<!-- 查询条件 -->
	<div class="row">
	<div class="col-sm-12">
	<form:form id="searchForm" modelAttribute="roomCheckIn" action="" method="post" class="form-inline">
		<input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
		<div class="form-group">
			<span>活动标题：&nbsp;</span>
			<input autocomplete="off" name="roomName" value="${roomCheckIn.roomName}" style="cursor: pointer" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
			<span>报名日期范围：&nbsp;</span>
			<input autocomplete="off" id="beginDate" style="cursor: pointer" name="beginTime" type="text" maxlength="20" class="laydate-icon form-control layer-date input-sm"
				   value="${enroll.beginTime}"/>
			<label>&nbsp;--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label><input autocomplete="off" style="cursor: pointer" id="endDate" name="endTime" type="text" maxlength="20" class=" laydate-icon form-control layer-date input-sm"
																		value="${enroll.endTime}" />&nbsp;&nbsp;
		</div>
	</form:form>
	<br/>
	</div>
	</div>
	
	
	<!-- 工具栏 -->
	<div class="row">
	<div class="col-sm-12">
		<%--<div class="pull-left">
			<table:delRow url="${ctx}/qqc/active/deleteAll" id="activeTable"></table:delRow><!-- 删除按钮 -->
		</div>--%>
		<div class="pull-right">
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
		</div>
	</div>
	</div>
	
	
	
	
	<table id="activeTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
		<thead>
			<tr>
				<th> <input type="checkbox" class="i-checks"></th>
				<th>姓名</th>
				<th>电话</th>
				<th>报名时间</th>
				<th>操作</th>
			</tr>
		</thead>
		<tbody>
		<c:forEach items="${page.list}" var="enroll">
			<tr>
				<td> <input type="checkbox" id="${enroll.id}" class="i-checks"></td>
				<td>
					${enroll.name}
				</td>
				<td>
					${enroll.phone}
				</td>
				<td>
					${enroll.createTime}
				</td>
				<td>
					<a title="查看" href="#" onclick="openDialogView('查看信息', '${ctx}/qqc/enroll/form/room?id=${enroll.id}','1000px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
					<a title="删除" href="${ctx}/qqc/enroll/delete/room?id=${enroll.id}" onclick="return confirmx('确认要删除该信息吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
				</td>
			</tr>
		</c:forEach>
		</tbody>
	</table>
	<!-- 分页代码 -->
	<table:page page="${page}"></table:page>
	<br/>
	<br/>
	</div>
	</div>
</div>
</body>
<script>

</script>
</html>
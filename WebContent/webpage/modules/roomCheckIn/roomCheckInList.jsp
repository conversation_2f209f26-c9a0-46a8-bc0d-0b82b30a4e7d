<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>空间入住列表</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
	</style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
<div class="ibox">
<div class="ibox-title">
		<h5>空间入住信息 </h5>
		<div class="ibox-tools">
			<a class="collapse-link">
				<i class="fa fa-chevron-up"></i>
			</a>
			<a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
				<i class="fa fa-wrench"></i>
			</a>
			<ul class="dropdown-menu dropdown-user">
				<li><a href="#">选项1</a>
				</li>
				<li><a href="#">选项2</a>
				</li>
			</ul>
			<a class="close-link">
				<i class="fa fa-times"></i>
			</a>
		</div>
	</div>
    
    <div class="ibox-content">
	<sys:message content="${message}"/>
	
	<!-- 查询条件 -->
	<div class="row">
	<div class="col-sm-12">
	<form:form id="searchForm" modelAttribute="roomCheckIn" action="" method="post" class="form-inline">
		<input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
		<div class="form-group">
			<span>标题：&nbsp;</span>
			<form:input path="roomName" cssStyle="cursor: pointer" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
			<span>区域：</span>
			<sys:treeSelectMulti id="area" name="area.id" value="${roomCheckIn.area.id}" labelName="areaName" labelValue="${roomCheckIn.area.name}"
								 title="区域" url="/sys/area/treeData" extId="" cssClass="form-control m-s" allowClear="true"/>
		 </div>
	</form:form>
	<br/>
	</div>
	</div>
	
	
	<!-- 工具栏 -->
	<div class="row">
	<div class="col-sm-12">
		<div class="pull-left">
			<%--<shiro:hasPermission name="qqc:teacher:add">--%>
				<table:addRow width="1000px" height="700px" url="${ctx}/qqc/roomcheckin/form" title="空间入住"></table:addRow><!-- 增加按钮 -->
			<%--</shiro:hasPermission>--%>
			<%--<shiro:hasPermission name="qqc:teacher:del">--%>
				<table:delRow url="${ctx}/qqc/roomcheckin/deleteAll" id="newsTable"></table:delRow><!-- 删除按钮 -->
			<%--</shiro:hasPermission>--%>
				<%-- <table:importExcel url="${ctx}/qqc/companyInfo/import"></table:importExcel><!-- 导入按钮 -->
	       		<table:exportExcel url="${ctx}/qqc/companyInfo/export"></table:exportExcel><!-- 导出按钮 --> --%>
	       		<!-- <button class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" onclick="sortOrRefresh()" title="刷新"><i class="glyphicon glyphicon-repeat"></i> 刷新</button> -->
			</div>
		<div class="pull-right">
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
		</div>
	</div>
	</div>
	
	
	
	
	<table id="newsTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
		<thead>
			<tr>
				<th> <input type="checkbox" class="i-checks"></th>
				<th>标题</th>
				<th>区域</th>
				<th>详细地址</th>
				<th>产业范围</th>
				<th>操作</th>
			</tr>
		</thead>
		<tbody>
		<c:forEach items="${page.list}" var="roomCheckIn">
			<tr>
				<td> <input type="checkbox" id="${roomCheckIn.id}" class="i-checks"></td>
				<td><a href="#" onclick="openDialogView('查看新闻', '${ctx}/qqc/roomcheckin/form?id=${roomCheckIn.id}','1000px', '700px')">
					${fns:abbr(roomCheckIn.roomName,50)}
				</a></td>
				<td>
					${roomCheckIn.area.name}
				</td>
				<td>
					${fns:abbr(roomCheckIn.roomAddress,50)}
				</td>
				<td>
					${fns:abbr(roomCheckIn.industryScope,50)}
				</td>
				<td>
					<a title="设置报名表单" onclick="openDialogView('报名表单', '${ctx}/customer/form/form?activeId=${roomCheckIn.id}&type=room',window.screen.availWidth+'px',  window.screen.availHeight-100+'px')"  href="#" class="btn btn-success btn-xs btn-circle" >报名</a>
					 <%--<shiro:hasPermission name="qqc:teacher:view">--%>
						<a title="查看信息" href="#" onclick="openDialogView('查看信息', '${ctx}/qqc/roomcheckin/form?id=${roomCheckIn.id}','1000px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
					 <%--</shiro:hasPermission>--%>
					 <%--<shiro:hasPermission name="qqc:teacher:edit">--%>
    					<a title="修改信息" href="#" onclick="openDialog('修改信息', '${ctx}/qqc/roomcheckin/form?id=${roomCheckIn.id}','1000px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
    				<%-- </shiro:hasPermission>--%>
    				<%-- <shiro:hasPermission name="qqc:teacher:del">--%>
						<a title="删除信息" href="${ctx}/qqc/roomcheckin/delete?id=${roomCheckIn.id}" onclick="return confirmx('确认要删除该信息吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
					 <%--</shiro:hasPermission>--%>
				</td>
			</tr>
		</c:forEach>
		</tbody>
	</table>
	<!-- 分页代码 -->
	<table:page page="${page}"></table:page>
	<br/>
	<br/>
	</div>
	</div>
</div>
</body>
</html>
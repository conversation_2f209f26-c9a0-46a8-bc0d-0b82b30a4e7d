<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
    <title>项目评审列表</title>
    <meta name="decorator" content="default"/>
    <style type="text/css">
        .table-responsive {
            margin-top: 15px;
            overflow-x: hidden;
        }
    </style>
    <script type="text/javascript">
        function openScoreDialog(reviewId, projectId, projectName, currentScore) {
            var html = '<div style="padding: 20px;">' +
                       '    <p style="margin-bottom: 10px;">为项目【' + projectName + '】打分 (0-100):</p>' +
                       '    <input type="number" id="scoreInput" class="layui-layer-input" value="' + (currentScore && currentScore !== 'null' ? currentScore : '') + '">' +
                       '</div>';

            top.layer.open({
                type: 1,
                title: '项目打分',
                area: ['400px', '220px'],
                content: html,
                btn: ['确定', '取消'],
                yes: function(index, layero){
                    var value = layero.find('#scoreInput').val();
                    var score = parseFloat(value);

                    if (value === '' || isNaN(score) || score < 0 || score > 100) {
                        top.layer.msg('请输入0到100之间的有效分数！', {icon: 2});
                        return;
                    }

                    top.layer.close(index);
                    var loadingIndex = top.layer.load(1, { shade: [0.1, '#fff'] });

                    $.ajax({
                        url: '${ctx}/qqc/judgeReview/saveScore',
                        type: 'POST',
                        data: {
                            reviewId: reviewId,
                            projectId: projectId,
                            score: score
                        },
                        success: function(result) {
                            top.layer.close(loadingIndex);
                            if(result.success) {
                                top.layer.msg('打分成功！', {icon: 1, time: 1000}, function(){
                                    // 调用一个能同时刷新父页面和自身的函数
                                    refreshParentAndSelf();
                                });
                            } else {
                                top.layer.msg('打分失败: ' + result.msg, {icon: 2});
                            }
                        },
                        error: function() {
                            top.layer.close(loadingIndex);
                            top.layer.msg('操作失败，请稍后重试', {icon: 2});
                        }
                    });
                }
            });
        }

        function refreshParentAndSelf() {
            // 刷新父页面A (使用我们之前定义在top上的函数)
            if (top.refreshReviewListPage && typeof top.refreshReviewListPage === 'function') {
                top.refreshReviewListPage();
            } else if (top.mainFrame) { // 作为备用方案
                top.mainFrame.location.reload();
            }

            // 刷新本页面B
            location.reload();
        }
    </script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="ibox">
        <div class="ibox-title">
            <h5>项目评审</h5>
        </div>
        <div class="ibox-content">
            <sys:message content="${message}"/>
            <div class="table-responsive">
                <table id="contentTable" class="table table-striped table-bordered table-hover table-condensed">
                    <thead>
                    <tr>
                        <th class="text-center">参赛项目</th>
                        <th class="text-center">赛事分组</th>
                        <th class="text-center">第一申报人</th>
                        <th class="text-center">第一申报人手机号</th>
                        <th class="text-center" style="width: 100px;">我的打分</th>
                        <th class="text-center" style="width: 150px;">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <c:forEach items="${projectList}" var="project">
                        <tr>
                            <td>${project.participantProject}</td>
                            <td>${fns:getDictLabel(project.competitionGroup, 'competition_group', '')} ${fns:getDictLabel(project.competitionSubgroup, 'competition_subgroup', '')}</td>
                            <td>${project.firstApplicantName}</td>
                            <td>${project.firstApplicantMobile}</td>
                            <td class="text-center">
                                <c:choose>
                                    <c:when test="${not empty project.myScore}">
                                        <span class="label label-success">${project.myScore}</span>
                                    </c:when>
                                    <c:otherwise>
                                        <span class="label label-warning">未打分</span>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td class="text-center">
                                <a href="#" onclick="top.openDialogView('查看项目详情', '${ctx}/qqc/registration/view?id=${project.id}','900px', '600px')" class="btn btn-info btn-xs"><i class="fa fa-search-plus"></i> 查看</a>
                                <a href="#" onclick="openScoreDialog('${reviewId}', '${project.id}', '${project.participantProject}', '${project.myScore}')" class="btn btn-success btn-xs"><i class="fa fa-check-square-o"></i> 打分</a>
                            </td>
                        </tr>
                    </c:forEach>
                    <c:if test="${empty projectList}">
                        <tr>
                            <td colspan="6" class="text-center">该评审活动下没有找到关联的项目。</td>
                        </tr>
                    </c:if>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</body>
</html>
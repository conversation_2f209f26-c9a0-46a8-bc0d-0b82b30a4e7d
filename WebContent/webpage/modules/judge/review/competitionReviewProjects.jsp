<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
    <title>评审项目列表</title>
    <meta name="decorator" content="default"/>
    <script type="text/javascript">
        function eliminateProject(projectId) {
            top.layer.confirm('确定要淘汰该项目吗?', {icon: 3, title:'提示'}, function(index){
                $.ajax({
                    url: "${ctx}/judge/review/eliminateProject",
                    type: "POST",
                    data: { projectId: projectId },
                    dataType: "json",
                    success: function(data) {
                        if (data.success) {
                            top.layer.msg(data.msg, {icon: 1, time: 1000}, function(){
                                var row = $('#row-' + projectId);
                                row.find('.project-status').html('<span class="label label-danger">已淘汰</span>');
                                row.find('.eliminate-btn').remove();
                            });
                        } else {
                            top.layer.alert(data.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        top.layer.alert('请求失败!', {icon: 2});
                    }
                });
                top.layer.close(index);
            });
        }
        
        function promoteToFinal(projectId) {
            top.layer.confirm('是否确定进行复审晋级?', {icon: 3, title:'提示'}, function(index){
                $.ajax({
                    url: "${ctx}/judge/review/promoteToFinal",
                    type: "POST",
                    data: { projectId: projectId },
                    dataType: "json",
                    success: function(data) {
                        if (data.success) {
                            top.layer.msg(data.msg, {icon: 1, time: 1000}, function(){
                                var row = $('#row-' + projectId);
                                row.find('.project-status').html('<span class="label label-info">复审晋级</span>');
                                row.find('.promote-final-btn').remove();
                                row.find('.eliminate-btn').remove();
                            });
                        } else {
                            top.layer.alert(data.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        top.layer.alert('请求失败!', {icon: 2});
                    }
                });
                top.layer.close(index);
            });
        }

        function promoteProject(projectId) {
            top.layer.confirm('是否确定进行初审晋级?', {icon: 3, title:'提示'}, function(index){
                $.ajax({
                    url: "${ctx}/judge/review/promoteProject",
                    type: "POST",
                    data: { projectId: projectId },
                    dataType: "json",
                    success: function(data) {
                        if (data.success) {
                            top.layer.msg(data.msg, {icon: 1, time: 1000}, function(){
                                var row = $('#row-' + projectId);
                                row.find('.project-status').html('<span class="label label-primary">初审晋级</span>');
                                row.find('.promote-btn').remove();
                                row.find('.eliminate-btn').remove();
                            });
                        } else {
                            top.layer.alert(data.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        top.layer.alert('请求失败!', {icon: 2});
                    }
                });
                top.layer.close(index);
            });
        }

        function editScore(projectId, reviewId, currentScore) {
            top.layer.prompt({
                formType: 0, //输入框
                value: currentScore,
                title: '请输入最终得分'
            }, function(value, index){
                var newScore = parseFloat(value);
                if (isNaN(newScore) || newScore < 0 || newScore > 100) {
                    top.layer.alert('请输入0到100之间的有效数字!', {icon: 2});
                    return;
                }
                
                $.ajax({
                    url: "${ctx}/judge/review/updateScore",
                    type: "POST",
                    data: {
                        projectId: projectId,
                        reviewId: reviewId,
                        score: newScore
                    },
                    dataType: "json",
                    success: function(data) {
                        if (data.success) {
                            top.layer.msg(data.msg, {icon: 1, time: 1000}, function(){
                                var scoreCell = $('#row-' + projectId).find('.project-final-score');
                                scoreCell.html('<span class="text-danger" style="font-weight: bold;">' + newScore.toFixed(2) + '</span>');
                            });
                        } else {
                            top.layer.alert(data.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        top.layer.alert('请求失败!', {icon: 2});
                    }
                });
                top.layer.close(index);
            });
        }
        
        function showUnscoredJudges(projectId, reviewId) {
            $.ajax({
                url: "${ctx}/judge/review/getUnscoredJudges",
                type: "GET",
                data: { projectId: projectId, reviewId: reviewId },
                dataType: "json",
                success: function(data) {
                    if (data.success) {
                        var judges = (data.body && data.body.unscoredJudges) ? data.body.unscoredJudges : [];
                        var content = '<div style="padding: 20px;">';
                        if (judges.length > 0) {
                            content += '<ul class="list-group">';
                            judges.forEach(function(judge) {
                                content += '<li class="list-group-item">' + (judge.name || '未知姓名') + '</li>';
                            });
                            content += '</ul>';
                        } else {
                            content += '<p class="text-center">所有裁判都已完成评分。</p>';
                        }
                        content += '</div>';

                        top.layer.open({
                            type: 1,
                            title: '未评分裁判列表',
                            content: content,
                            area: ['400px', '300px']
                        });
                    } else {
                        top.layer.alert(data.msg || "获取列表失败，请稍后重试。", {icon: 2});
                    }
                },
                error: function() {
                    top.layer.alert('网络请求失败，请检查网络连接或联系管理员。', {icon: 2});
                }
            });
        }
    </script>
</head>
<body>
<div class="wrapper wrapper-content" style="padding: 0;">
    <div class="panel-body" style="padding: 10px;">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th>项目名称</th>
                    <th>参赛分组</th>
                    <th>平均分</th>
                    <th>最终得分</th>
                    <th>总裁判数</th>
                    <th>未评分裁判数</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <c:forEach items="${projectList}" var="project">
                    <tr id="row-${project.id}">
                        <td>${project.participantProject}</td>
                        <td>${fns:getDictLabel(project.competitionGroup, 'competition_group', '')}</td>
                        <td class="project-score">
                            <c:choose>
                                <c:when test="${not empty project.averageScore}">
                                    <span class="text-danger" style="font-weight: bold;">${project.averageScore}</span>
                                </c:when>
                                <c:otherwise>
                                    <span class="text-muted">无评分</span>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td class="project-final-score">
                            <c:if test="${not empty project.finalScore}">
                                <span class="text-danger" style="font-weight: bold;">${project.finalScore}</span>
                            </c:if>
                        </td>
                        <td>${project.totalJudges}</td>
                        <td>${project.unscoredJudges}</td>
                        <td class="project-status">
                            <c:choose>
                                <c:when test="${project.status == 4}">
                                    <span class="label label-danger">已淘汰</span>
                                </c:when>
                                <c:when test="${project.status == 6}">
                                    <span class="label label-info">复审晋级</span>
                                </c:when>
                                <c:when test="${project.status == 5 && reviewType == 0}">
                                    <span class="label label-primary">初审晋级</span>
                                </c:when>
                                <c:otherwise>
                                    <span class="label label-success">未淘汰</span>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td>
                            <a href="javascript:;" onclick="editScore('${project.id}', '${project.reviewId}', '${not empty project.finalScore ? project.finalScore : project.averageScore}')" class="btn btn-xs btn-success">修改评分</a>
                            <c:choose>
                                <c:when test="${project.unscoredJudges > 0}">
                                    <a href="javascript:;" onclick="showUnscoredJudges('${project.id}', '${project.reviewId}')" class="btn btn-xs btn-info">查看未评分裁判</a>
                                </c:when>
                                <c:otherwise>
                                    <a class="btn btn-xs btn-default disabled" title="所有裁判均已评分" style="pointer-events: none;">查看未评分裁判</a>
                                </c:otherwise>
                            </c:choose>
                            
                            <shiro:hasRole name="cqc_admin">
                                <c:choose>
                                    <%-- 如果是复赛评审 --%>
                                    <c:when test="${reviewType == 1}">
                                        <c:if test="${project.status != 6 && project.status != 4}">
                                            <a href="javascript:;" onclick="promoteToFinal('${project.id}')" class="btn btn-xs btn-warning promote-final-btn">复审晋级</a>
                                            <a href="javascript:;" onclick="eliminateProject('${project.id}')" class="btn btn-xs btn-danger eliminate-btn">淘汰</a>
                                        </c:if>
                                    </c:when>
                                    <%-- 如果是初赛评审 --%>
                                    <c:otherwise>
                                        <c:if test="${project.status != 4 && project.status != 5 && project.status != 6}">
                                            <a href="javascript:;" onclick="promoteProject('${project.id}')" class="btn btn-xs btn-primary promote-btn">初审晋级</a>
                                            <a href="javascript:;" onclick="eliminateProject('${project.id}')" class="btn btn-xs btn-danger eliminate-btn">淘汰</a>
                                        </c:if>
                                    </c:otherwise>
                                </c:choose>
                            </shiro:hasRole>
                        </td>
                    </tr>
                </c:forEach>
                <c:if test="${empty projectList}">
                    <tr>
                        <td colspan="8" class="text-center">该评审活动没有关联任何项目。</td>
                    </tr>
                </c:if>
            </tbody>
        </table>
    </div>
</div>
</body>
</html> 
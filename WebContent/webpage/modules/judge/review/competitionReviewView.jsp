<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
    <title>评审详情</title>
    <meta name="decorator" content="default"/>
    <script type="text/javascript">
       $(document).ready(function() {
          // 禁用所有交互
          $('input, select').prop('disabled', true);
       });
    </script>
</head>
<body>
<div style="padding: 20px;">
    <form:form id="viewActivityForm" modelAttribute="competitionReview" method="post" class="form-horizontal">
       <form:hidden path="id"/>
       
       <div class="form-group">
          <label class="col-sm-2 control-label">评审名称：</label>
          <div class="col-sm-10">
             <form:input path="reviewName" htmlEscape="false" class="form-control" readonly="true"/>
          </div>
       </div>

       <div class="table-responsive" style="margin-top:15px;">
          <table id="projectsTable" class="table table-bordered table-striped">
             <thead>
             <tr>
                <th>项目名称</th>
                <th>报名途径</th>
                <th>参赛地区</th>
                <th>赛事分组</th>
                <th>行业分类</th>
                <th>状态</th>
                <th>平均分</th>
             </tr>
             </thead>
             <tbody>
             <c:forEach items="${projectList}" var="registration">
                <tr>
                   <td>${registration.participantProject}</td>
                   <td>${fns:getDictLabel(registration.registrationChannel, 'registration_channel', '')}</td>
                   <td>${registration.competitionCityName} - ${registration.competitionDistrictName}</td>
                   <td>${fns:getDictLabel(registration.competitionGroup, 'competition_group', '')}</td>
                   <td>${fns:getDictLabel(registration.projectField, 'project_field', '')}</td>
                   <td>
                       <c:choose>
                           <c:when test="${registration.status == 4}">
                               <c:out value="已淘汰"/>
                           </c:when>
                           <c:otherwise>
                               <c:out value="未淘汰"/>
                           </c:otherwise>
                       </c:choose>
                   </td>
                   <td>
                       <c:choose>
                           <c:when test="${not empty registration.averageScore}">
                               <c:out value="${registration.averageScore}"/>
                           </c:when>
                           <c:otherwise>
                               <c:out value="未评分"/>
                           </c:otherwise>
                       </c:choose>
                   </td>
                </tr>
             </c:forEach>
             </tbody>
          </table>
       </div>

       <div class="form-group" style="margin-top: 15px;">
          <label class="col-sm-2 control-label">评审裁判：</label>
          <div class="col-sm-10">
             <input type="text" name="reviewJudges" class="form-control" readonly="true" value="已选择 ${fn:length(competitionReview.judgeIds)} 项"/>
          </div>
       </div>
       
       <div class="table-responsive" style="max-height: 200px; overflow-y: auto; margin-top:15px;">
          <table id="judgesTable" class="table table-bordered table-striped">
             <thead>
             <tr>
                <th>裁判姓名</th>
                <th>所属单位</th>
                <th>联系电话</th>
             </tr>
             </thead>
             <tbody>
             <c:forEach items="${judgePage.list}" var="judge">
                 <c:if test="${fn:contains(competitionReview.judgeIds, judge.id)}">
                    <tr>
                       <td>${judge.name}</td>
                       <td>${judge.city}</td>
                       <td>${judge.phone}</td>
                    </tr>
                 </c:if>
             </c:forEach>
             </tbody>
          </table>
       </div>
    </form:form>
    <div class="form-actions" style="padding: 10px 20px 0; text-align: right;">
       <input id="btnCancel" class="btn" type="button" value="返 回" onclick="history.go(-1)">
    </div>
</div>
</body>
</html> 
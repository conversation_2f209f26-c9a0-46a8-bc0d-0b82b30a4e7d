<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
    <title>裁判评审列表</title>
    <meta name="decorator" content="default"/>
    <script type="text/javascript">
        // 将刷新函数定义在顶层窗口，供子窗口调用
        top.refreshReviewListPage = function() {
            location.reload();
        }

        $(document).ready(function() {
        });
    </script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="ibox">
        <div class="ibox-title">
            <h5>裁判评审列表</h5>
        </div>
        <div class="ibox-content">
            <sys:message content="${message}"/>
            <div class="table-responsive">
                <table id="contentTable" class="table table-striped table-bordered table-hover table-condensed">
                    <thead>
                    <tr>
                        <th class="text-center">评审名称</th>
                        <th class="text-center">项目数</th>
                        <th class="text-center">已评审项目数</th>
                        <th class="text-center">未评审项目数</th>
                        <th class="text-center">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <c:forEach items="${reviewInfoList}" var="reviewInfo">
                        <tr>
                            <td class="text-center">${reviewInfo.reviewName}</td>
                            <td class="text-center">${reviewInfo.projectCount}</td>
                            <td class="text-center">${reviewInfo.projectCount - reviewInfo.unreviewedProjectCount}</td>
                            <td class="text-center">${reviewInfo.unreviewedProjectCount}</td>
                            <td class="text-center">                                <a href="#" onclick="top.openDialog('项目评审','${ctx}/qqc/judgeReview/projects?reviewId=${reviewInfo.reviewId}','1000px', '700px')" class="btn btn-info btn-xs"><i class="fa fa-pencil"></i> 评审</a>
                            </td>
                        </tr>
                    </c:forEach>
                    <c:if test="${empty reviewInfoList}">
                        <tr>
                            <td colspan="5" class="text-center">
                                <c:choose>
                                    <c:when test="${not empty error}">
                                        <p>${error}</p>
                                    </c:when>
                                    <c:otherwise>
                                        <p>没有找到相关的评审任务。</p>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                    </c:if>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</body>
</html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
    <title>审核列表</title>
    <meta name="decorator" content="default"/>
    <style type="text/css">
        .btn-default{
            color: #333 !important;
            background-color: #fff !important;
            border-color: #ccc !important;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function(){
        })
        //选择活动
        function selectActity(){
            top.layer.open({
                type: 2,
                area: ['1000px', '700px'],
                title:"选择活动",
                maxmin: true, //开启最大化最小化按钮
                content: "${ctx}/qqc/active/list/view",
                btn: ['确定', '关闭'],
                yes: function(index, layero){
                    var arr = layero.find("iframe")[0].contentWindow.selectActity();
                    console.log(arr);
                    if(!arr){
                        return false;
                    };
                    $("#actityName").val(arr.name);
                    $("#actityId").val(arr.id);
                    top.layer.close(index);
                },
                cancel: function(index){}
            });
        }
    </script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="ibox">
        <div class="ibox-title">
            <h5>审核列表 </h5>
            <div class="ibox-tools">
                <a class="collapse-link">
                    <i class="fa fa-chevron-up"></i>
                </a>
                <a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
                    <i class="fa fa-wrench"></i>
                </a>
                <ul class="dropdown-menu dropdown-user">
                    <li><a href="#">选项1</a>
                    </li>
                    <li><a href="#">选项2</a>
                    </li>
                </ul>
                <a class="close-link">
                    <i class="fa fa-times"></i>
                </a>
            </div>
        </div>

        <div class="ibox-content">
            <sys:message content="${message}"/>

            <!-- 查询条件 -->
            <div class="row">
                <div class="col-sm-12">
                    <form:form id="searchForm" modelAttribute="review" action="" method="post" class="form-inline">
                        <input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
                        <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                        <div class="form-group">
                            <span>活动标题：&nbsp;</span>
                            <input type="hidden" id="actityId" name="enroll.active.id" value="${review.enroll.active.id}">
                            <input style="width: 200px;" type="text" id="actityName" name="enroll.active.title" value="${review.enroll.active.title}"
                                   class="form-control required" readonly="readonly" onclick="selectActity()">
                            <button style="margin-left: -4px" type="button" onclick="selectActity()" class="btn btn-primary">
                                <i class="fa fa-search"></i>
                            </button>
                        </div>
                    </form:form>
                    <br/>
                </div>
            </div>


            <!-- 工具栏 -->
            <div class="row">
                <div class="col-sm-12">
                    <div class="pull-left">
                        <shiro:hasAnyPermissions name="qqc:review:del">
                            <table:delRow url="${ctx}/qqc/review/deleteAll" id="reviewTable"></table:delRow>
                        </shiro:hasAnyPermissions>
                    </div>
                    <div class="pull-right">
                        <button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
                        <button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
                    </div>
                </div>
            </div>




            <table id="reviewTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
                <thead>
                <tr>
                    <th> <input type="checkbox" class="i-checks"></th>
                    <th>报名人</th>
                    <th>报名时间</th>
                    <th>分配时间</th>
                    <c:if test="${fns:isAdmin()}">
                        <th>分配专家</th>
                    </c:if>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${page.list}" var="review">
                    <tr>
                        <td> <input id="${review.id}" type="checkbox" class="i-checks"></td>
                        <td>
                            <c:choose>
                                <c:when test="${not empty review.enroll}">
                                    ${review.enroll.applyId}
                                </c:when>
                                <c:when test="${not empty review.entrepreneurship}">
                                    ${review.entrepreneurship.name}
                                </c:when>
                            </c:choose>
                        </td>
                        <td>
                            <c:choose>
                                <c:when test="${not empty review.enroll}">
                                    ${review.enroll.createTime}
                                </c:when>
                                <c:when test="${not empty review.entrepreneurship}">
                                    <fmt:formatDate value="${review.entrepreneurship.createDate}" pattern="yyyy-MM-dd HH:mm:ss" />

                                </c:when>
                            </c:choose>

                        <td>
                            <fmt:formatDate value="${review.createDate}" pattern="yyyy-MM-dd HH:mm:ss" />
                        </td>
                        <c:if test="${fns:isAdmin()}">
                            <td>
                                    ${review.user.name}
                            </td>
                        </c:if>
                        <td>
                            <shiro:hasAnyPermissions name="qqc:review:view">
                                <a title="查看" href="#" onclick="openDialogView('查看信息', '${ctx}/qqc/review/form/view?id=${review.id}','1000px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
                            </shiro:hasAnyPermissions>
                            <shiro:hasAnyPermissions name="qqc:review:score">
                                <a title="打分" href="#" onclick="openDialog('打分', '${ctx}/qqc/review/form/score?id=${review.id}','1000px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
                            </shiro:hasAnyPermissions>
                            <shiro:hasAnyPermissions name="qqc:review:del">
                                <a title="删除信息" href="${ctx}/qqc/review/delete?id=${review.id}" onclick="return confirmx('确认要将这条信息取消分配给${review.user.name}专家吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
                            </shiro:hasAnyPermissions>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
            <!-- 分页代码 -->
            <table:page page="${page}"></table:page>
            <br/>
            <br/>
        </div>
    </div>
</div>
</body>
<script>

</script>
</html>
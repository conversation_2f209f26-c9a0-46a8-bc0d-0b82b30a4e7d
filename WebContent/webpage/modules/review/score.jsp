<%@ taglib prefix="from" uri="http://www.springframework.org/tags/form" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>评分详情</title>
    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <link rel="stylesheet" href="${ctxStatic}/weui/css/table-css.css">
    <script>
        var validateForm;
        function doSubmit(){//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
            if(validateForm.form()){
                $("#inputForm").submit();
                return true;
            }
            return false;
        }
        $(document).ready(function() {
            validateForm = $("#inputForm").validate({
                submitHandler: function(form){
                    loading('正在提交，请稍等...');
                    form.submit();
                },
                errorContainer: "#messageBox",
                errorPlacement: function(error, element) {
                    $("#messageBox").text("输入有误，请先更正。");
                    if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
                        error.appendTo(element.parent().parent());
                    } else {
                        error.insertAfter(element);
                    }
                }
            });
        });
        //加载扩展模块
        layer.config({
            extend: 'extend/layer.ext.js'
        });
        layer.ready(function(){
            //使用相册
            layer.photos({
                photos: '.img'
            });
        });

        $(document).ready(function () {
            $(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg img", function (e) {
                var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
                    (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                var imagep = $(".layui-layer-phimg").parent().parent();
                var image = $(".layui-layer-phimg").parent();
                var h = image.height();
                var w = image.width();
                if (delta > 0) {
                    if (h < (window.innerHeight)) {
                        h = h * 1.05;
                        w = w * 1.05;
                    }
                } else if (delta < 0) {
                    if (h > 100) {
                        h = h * 0.95;
                        w = w * 0.95;
                    }
                }
                imagep.css("top", (window.innerHeight - h) / 2);
                imagep.css("left", (window.innerWidth - w) / 2);
                image.height(h);
                image.width(w);
                imagep.height(h);
                imagep.width(w);
            });
        })
    </script>
</head>
<body class="gray-bg">
<form:form id="inputForm" action="${ctx}/qqc/review/save" method="post" class="form-horizontal">
    <table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
        <tbody>
        <tr>
            <input type="hidden" name="id" value="${review.id}" />
            <td  class="width-15 active">
                <label class="pull-right">活动标题：</label>
            </td>
            <td colspan="3" class="width-35" >
                <c:choose>
                    <c:when test="${not empty review.enroll}">
                        <input type="text" readonly value="${review.enroll.active.title}" class="form-control">
                    </c:when>
                    <c:when test="${not empty review.entrepreneurship}">
                        <input type="text" readonly value="${review.entrepreneurship.active.title}" class="form-control">
                    </c:when>
                </c:choose>
            </td>
            <td  class="width-15 active">
                <label class="pull-right">活动详情地址：</label>
            </td>
            <td colspan="3" class="width-35" >
                <c:choose>
                    <c:when test="${not empty review.enroll}">
                        <input type="text" readonly value="${review.enroll.active.indexAddress}" class="form-control">
                    </c:when>
                    <c:when test="${not empty review.entrepreneurship}">
                        <input type="text" readonly value="${review.entrepreneurship.active.indexAddress}" class="form-control">
                    </c:when>
                </c:choose>
            </td>
        </tr>
        <tr>
            <td  class="width-15 active">
                <label class="pull-right">得分：</label>
            </td>
            <td colspan="3" class="width-35" >
                <input type="text" name="score" value="${review.score}" class="form-control">
            </td>
            <td  class="width-15 active">
                <label class="pull-right">评分说明：</label>
            </td>
            <td colspan="3" class="width-35" >
                <textarea cols="3" name="content" rows="3" class="form-control">${review.content}</textarea>
            </td>
        </tr>
        </tbody>
    </table>
</form:form>
<c:choose>
    <c:when test="${not empty review.enroll}">
        <div align="center">
            <h3>报名信息</h3>
            <table>
                <tbody>
                <c:forEach items="${configList}" var="config">
                    <tr>
                        <td data-label="${config.title}">${config.title}</td>
                        <td data-label="${config.value}">
                            <c:choose>
                                <c:when test="${config.type == 'uploaderImg'}">

                                    <c:choose>
                                        <c:when test="${empty config.value}">
                                            <span style="color: red">未上传图片</span>
                                        </c:when>
                                        <c:otherwise>
                                            <c:forEach items="${config.value.split(',')}" var="img">
                                                <div class="img">
                                                    <img width="200px" height="200px" src="${img}">
                                                </div>
                                            </c:forEach>
                                        </c:otherwise>
                                    </c:choose>
                                </c:when>
                                <c:when test="${config.type == 'uploaderFile'}">
                                    <a href="${config.value}">附件</a>
                                </c:when>
                                <c:otherwise>
                                    ${config.value}
                                </c:otherwise>
                            </c:choose>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </c:when>
    <c:when test="${not empty review.entrepreneurship}">
        aSda
    </c:when>
</c:choose>
</body>
<script>
</script>
</html>
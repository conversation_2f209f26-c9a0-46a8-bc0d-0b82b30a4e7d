<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短信验证码登录</title>
    <!-- 引入Bootstrap -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/bootstrap/3.3.4/css/bootstrap.min.css">
    <!-- 引入自定义样式 -->
    <style>
        body {
            background-color: #f8f8f8;
        }
        .login-container {
            max-width: 400px;
            margin: 100px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .login-title {
            text-align: center;
            margin-bottom: 30px;
        }
        .btn-send-code {
            width: 100%;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .countdown {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-title">
                <h3>短信验证码登录</h3>
            </div>
            <form id="loginForm">
                <div class="form-group">
                    <label for="phoneNumber">手机号码</label>
                    <input type="text" class="form-control" id="phoneNumber" placeholder="请输入手机号码" required>
                </div>
                <div class="form-group">
                    <label for="verificationCode">验证码</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="verificationCode" placeholder="请输入验证码" required>
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-primary" id="sendCodeBtn">发送验证码</button>
                            <span class="countdown" id="countdown"></span>
                        </span>
                    </div>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-success btn-block">登录</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 引入jQuery和Bootstrap的JavaScript -->
    <script src="${pageContext.request.contextPath}/static/jquery/jquery-1.9.1.js"></script>
    <script src="${pageContext.request.contextPath}/static/bootstrap/3.3.4/js/bootstrap.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        $(document).ready(function() {
            // 发送验证码按钮点击事件
            $('#sendCodeBtn').click(function() {
                var phoneNumber = $('#phoneNumber').val().trim();
                
                // 验证手机号
                if (!phoneNumber) {
                    alert('请输入手机号码');
                    return;
                }
                
                if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
                    alert('请输入有效的手机号码');
                    return;
                }
                
                // 发送AJAX请求
                $.ajax({
                    url: '${pageContext.request.contextPath}/api/verification-code/send',
                    type: 'POST',
                    data: {
                        phoneNumber: phoneNumber
                    },
                    success: function(response) {
                        if (response.success) {
                            // 开始倒计时
                            startCountdown(60);
                            alert('验证码发送成功，请注意查收');
                        } else {
                            alert(response.message || '验证码发送失败，请稍后重试');
                        }
                    },
                    error: function() {
                        alert('网络错误，请稍后重试');
                    }
                });
            });
            
            // 登录表单提交事件
            $('#loginForm').submit(function(e) {
                e.preventDefault();
                
                var phoneNumber = $('#phoneNumber').val().trim();
                var verificationCode = $('#verificationCode').val().trim();
                
                // 验证输入
                if (!phoneNumber) {
                    alert('请输入手机号码');
                    return;
                }
                
                if (!verificationCode) {
                    alert('请输入验证码');
                    return;
                }
                
                // 发送AJAX请求
                $.ajax({
                    url: '${pageContext.request.contextPath}/api/user/login',
                    type: 'POST',
                    data: {
                        phoneNumber: phoneNumber,
                        verificationCode: verificationCode
                    },
                    success: function(response) {
                        if (response.success) {
                            // 登录成功，保存token并跳转
                            localStorage.setItem('token', response.token);
                            alert('登录成功');
                            window.location.href = '${pageContext.request.contextPath}/index';
                        } else {
                            alert(response.message || '登录失败，请检查验证码是否正确');
                        }
                    },
                    error: function() {
                        alert('网络错误，请稍后重试');
                    }
                });
            });
            
            // 倒计时函数
            function startCountdown(seconds) {
                var countdown = $('#countdown');
                var sendCodeBtn = $('#sendCodeBtn');
                
                sendCodeBtn.hide();
                countdown.show();
                
                var timer = setInterval(function() {
                    seconds--;
                    countdown.text(seconds + '秒后重新发送');
                    
                    if (seconds <= 0) {
                        clearInterval(timer);
                        countdown.hide();
                        sendCodeBtn.show();
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html> 
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
    <title>云创学院</title>
    <meta name="decorator" content="default"/>
    <script type="text/javascript">
        $(document).ready(function() {
            //外部js调用
            laydate({
                elem: '#beginDate', //目标元素。由于laydate.js封装了一个轻量级的选择器引擎，因此elem还允许你传入class、tag但必须按照这种方式 '#id .class'
                event: 'focus' //响应事件。如果没有传入event，则按照默认的click
            });
            laydate({
                elem: '#endDate', //目标元素。由于laydate.js封装了一个轻量级的选择器引擎，因此elem还允许你传入class、tag但必须按照这种方式 '#id .class'
                event: 'focus' //响应事件。如果没有传入event，则按照默认的click
            });
        })

    </script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="ibox">
        <div class="ibox-title">
            <h5>云创学院 </h5>
            <div class="ibox-tools">
                <a class="collapse-link">
                    <i class="fa fa-chevron-up"></i>
                </a>
                <a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
                    <i class="fa fa-wrench"></i>
                </a>
                <ul class="dropdown-menu dropdown-user">
                    <li><a href="#">选项1</a>
                    </li>
                    <li><a href="#">选项2</a>
                    </li>
                </ul>
                <a class="close-link">
                    <i class="fa fa-times"></i>
                </a>
            </div>
        </div>

        <div class="ibox-content">
            <sys:message content="${message}"/>

            <!-- 查询条件 -->
            <div class="row">
                <div class="col-sm-12">
                    <form:form id="searchForm" modelAttribute="ycCollege" action="" method="post" class="form-inline">
                        <input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
                        <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                        <div class="form-group">
                            <span>标题：&nbsp;</span>
                            <form:input path="title" cssStyle="cursor: pointer" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
                            <span>日期范围：&nbsp;</span>
                            <input autocomplete="off" id="beginDate" style="cursor: pointer" name="beginDate" type="text" maxlength="20" class="laydate-icon form-control layer-date input-sm"
                                   value="${ycCollege.beginDate}"/>
                            <label>&nbsp;--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label><input autocomplete="off" style="cursor: pointer" id="endDate" name="endDate" type="text" maxlength="20" class=" laydate-icon form-control layer-date input-sm"
                                                                                        value="${ycCollege.endDate}" />&nbsp;&nbsp;
                        </div>
                    </form:form>
                    <br/>
                </div>
            </div>


            <!-- 工具栏 -->
            <div class="row">
                <div class="col-sm-12">
                    <div class="pull-left">
                        <shiro:hasPermission name="qqc:ycCollege:add">
                            <table:addRow width="1000px" height="700px" url="${ctx}/qqc/ycCollege/form" title="新闻"></table:addRow><!-- 增加按钮 -->
                        </shiro:hasPermission>
                        <shiro:hasPermission name="qqc:ycCollege:del">
                            <table:delRow url="${ctx}/qqc/ycCollege/deleteAll" id="ycCollegeTable"></table:delRow><!-- 删除按钮 -->
                        </shiro:hasPermission>
                        <%-- <table:importExcel url="${ctx}/qqc/companyInfo/import"></table:importExcel><!-- 导入按钮 -->
                           <table:exportExcel url="${ctx}/qqc/companyInfo/export"></table:exportExcel><!-- 导出按钮 --> --%>
                        <!-- <button class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" onclick="sortOrRefresh()" title="刷新"><i class="glyphicon glyphicon-repeat"></i> 刷新</button> -->
                    </div>
                    <div class="pull-right">
                        <button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
                        <button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
                    </div>
                </div>
            </div>




            <table id="ycCollegeTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
                <thead>
                <tr>
                    <th> <input type="checkbox" class="i-checks"></th>
                    <th>标题</th>
                    <th>副标题</th>
                    <th>图片</th>
                    <th>添加时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${page.list}" var="ycCollege">
                    <tr>
                        <td> <input type="checkbox" id="${ycCollege.id}" class="i-checks"></td>
                        <td><a href="#" onclick="openDialogView('查看', '${ctx}/qqc/ycCollege/form?type=View&id=${ycCollege.id}','1000px', '700px')">
                                ${fns:abbr(ycCollege.title,50)}
                        </a></td>
                        <td>
                                ${fns:abbr(ycCollege.subtitle,50)}
                        </td>
                        <td>
                                <img src="${ycCollege.imgUrl}" style="width:30px;height:30px;" />
                        </td>
                        <td>
                            <fmt:formatDate value="${ycCollege.createDate}" pattern="yyyy-MM-dd hh:mm:ss" />
                        </td>
                        <td>
                            <shiro:hasPermission name="qqc:ycCollege:view">
                                <a title="查看信息" href="#" onclick="openDialogView('查看信息', '${ctx}/qqc/ycCollege/form?id=${ycCollege.id}','1000px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
                            </shiro:hasPermission>
                            <shiro:hasPermission name="qqc:ycCollege:edit">
                                <a title="修改信息" href="#" onclick="openDialog('修改信息', '${ctx}/qqc/ycCollege/form?id=${ycCollege.id}','1000px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
                            </shiro:hasPermission>
                            <shiro:hasPermission name="qqc:ycCollege:del">
                                <a title="删除信息" href="${ctx}/qqc/ycCollege/delete?id=${ycCollege.id}" onclick="return confirmx('确认要删除该信息吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
                            </shiro:hasPermission>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
            <!-- 分页代码 -->
            <table:page page="${page}"></table:page>
            <br/>
            <br/>
        </div>
    </div>
</div>
</body>
</html>

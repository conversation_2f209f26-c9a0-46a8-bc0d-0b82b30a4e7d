<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <!-- SUMMERNOTE -->
    <link href="${ctxStatic}/summernote/summernote.css" rel="stylesheet">
    <link href="${ctxStatic}/summernote/summernote-bs3.css" rel="stylesheet">

    <link rel="stylesheet" type="text/css" href="${ctxStatic}/webuploader-0.1.5/webuploader.css">
    <link rel="stylesheet" type="text/css" href="${ctxStatic}/webuploader-0.1.5/demo.css">

    <script src="${ctxStatic}/summernote/summernote.min.js"></script>
    <script src="${ctxStatic}/summernote/summernote-zh-CN.js"></script>

    <script type="text/javascript">
        //加载扩展模块
        layer.config({
            extend: 'extend/layer.ext.js'
        });
        layer.ready(function(){
            //使用相册
            layer.photos({
                photos: '.fileImg'
            });
        });

        $(document).ready(function () {
            $(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg img", function (e) {
                var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
                    (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                var imagep = $(".layui-layer-phimg").parent().parent();
                var image = $(".layui-layer-phimg").parent();
                var h = image.height();
                var w = image.width();
                if (delta > 0) {
                    if (h < (window.innerHeight)) {
                        h = h * 1.05;
                        w = w * 1.05;
                    }
                } else if (delta < 0) {
                    if (h > 100) {
                        h = h * 0.95;
                        w = w * 0.95;
                    }
                }
                imagep.css("top", (window.innerHeight - h) / 2);
                imagep.css("left", (window.innerWidth - w) / 2);
                image.height(h);
                image.width(w);
                imagep.height(h);
                imagep.width(w);
            });
        })
    </script>
    <script type="text/javascript" src="${ctxStatic}/webuploader-0.1.5/webuploader.js"></script>
    <style>
        #uploader .queueList {
            margin: 0px;
            border: none;
        }
        #uploader .webuploader-pick{
            padding: 0px;
        }
        #uploader .queueList.webuploader-dnd-over {
            border: none;
        }
    </style>

</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-sm-12 animated fadeInRight">
            <div class="mail-box">
                <div class="mail-body">
                    <form:form enctype="multipart/form-data"  id="inputForm" modelAttribute="ycCollege" action="${ctx}/qqc/ycCollege/save" method="post" class="form-horizontal">
                        <div class="form-group">
                            <form:hidden path="id"/>
                            <label class="col-sm-2 control-label"><font color="red">*</font>标题：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${ycCollege.title}" id="title" name="title"  class="form-control required" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">副标题：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${ycCollege.subtitle}" id="subtitle" name="subtitle"  class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">图片：</label>
                                <%-- <input style="display: none" value="${newsInfo.url}" name="url" id="url"> --%>
                            <div class="col-sm-8">
                                <c:if test="${not empty ycCollege.imgUrl}">
                                    <div class="fileImg">
                                        <img id="imgUrl" width="200px" src="${ycCollege.imgUrl}" />
                                    </div>
                                </c:if>
                                <input type="file" class="form-control" name="img" id="img"><br>
                                    <%-- <div id="uploader" class="wu-example">
                                        <div class="queueList">
                                            <div id="dndArea" class="img">
                                                <div id="filePicker">
                                                    <c:if test="${newsInfo.url != null}">
                                                        <img style="width: 110px;height: 110px" src="${newsInfo.url}">
                                                    </c:if>
                                                    <c:if test="${newsInfo.url == null}">
                                                        <img style="width: 110px;height: 110px" src="${ctxStatic}/img/addpic.jpg">
                                                    </c:if>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <font color="red">(如以上传图片可以点击图片重新上传)</font> --%>
                                <%--<form:hidden  path="imgUrl" htmlEscape="false"  maxlength="255" class="form-control "/>
                                <sys:ckfinder input="imgUrl" type="images" uploadPath="/qqchuang/ycCollege"  selectMultiple="false" enableJcrop="true" />--%>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">视频：</label>
                            <div class="col-sm-8">
                                <c:if test="${not empty ycCollege.videoUrl}">
                                    <video controls src="${ycCollege.videoUrl}"></video>
                                </c:if>
                                <input type="file" class="form-control" name="video" id="video"><br>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">内容：</label>
                            <form:textarea cssStyle="display: none" path="details" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
                            <div class="col-sm-8">
                                <div class="mail-text">
                                    <div id="summernote" class="summernote">
                                            ${ycCollege.details}
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </form:form>
                </div>
            </div>
        </div>

    </div>
</div>
<script type="text/javascript">
    var validateForm;
    function doSubmit(){//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
        if(validateForm.form()){
            $("#inputForm").submit();
            return true;
        }
        return false;
    }
    $(document).ready(function () {
        $("#summernote").summernote({
            height: 400,
            minHeight: 300,
            maxHeight: 500,
            lang:'zh-CN',
        });
        validateForm = $("#inputForm").validate({
            submitHandler: function(form){
                var str= $('#summernote').code();
                $("#details").val(str);
                loading('正在提交，请稍等...');
                form.submit();
            },
            errorContainer: "#messageBox",
            errorPlacement: function(error, element) {
                $("#messageBox").text("输入有误，请先更正。");
                if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
                    error.appendTo(element.parent().parent());
                } else {
                    error.insertAfter(element);
                }
            }
        });
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

    });
</script>
</body>
</html>

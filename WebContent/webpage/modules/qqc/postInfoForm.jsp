<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp" %>
<html>
<head>
    <title>岗位信息</title>
    <meta name="decorator" content="default"/>
    <style type="text/css">
    </style>
    <script type="text/javascript">
        var validateForm;
        function doSubmit() {//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
            if (validateForm.form()) {
                $("#inputForm").submit();
                return true;
            }
            return false;
        }

        $(document).ready(function () {
            //$("#name").focus();
            validateForm = $("#inputForm").validate({
                submitHandler: function (form) {
                    loading('正在提交，请稍等...');
                    form.submit();
                },
                errorContainer: "#messageBox",
                errorPlacement: function (error, element) {
                    console.log(element);
                    $("#messageBox").text("输入有误，请先更正。");
                    if (element.is(":checkbox") || element.is(":radio") || element.parent().is(".input-append")) {
                        error.appendTo(element.parent().parent());
                    } else {
                        error.insertAfter(element);
                    }
                }
            });
        });

        //div点击事件
        function divWelfareClick(obj) {
            if ($(obj).hasClass("btn-success")) {
                delWelfare(obj);
            } else if ($(obj).hasClass("btn-info")) {
                addWelfare(obj);
            }
        }
        //js福利添加
        function addWelfare(obj) {
            var $welfare = $("#welfare").val();
            var $objText = $(obj).attr("alt");
            if ($welfare == '') {
                $welfare = $objText;
            } else {
                $welfare = $welfare + ',' + $objText;
            }
            $("#welfare").val($welfare);
            $(obj).removeClass("btn-info").addClass("btn-success");
        }
        //js福利去除
        function delWelfare(obj) {
            var $welfare = '';
            var $welfareList = $("#welfare").val().split(",");
            for (var index in $welfareList) {
                if ($(obj).attr("alt") != $welfareList[index]) {
                    if ($welfare == '')
                        $welfare = $welfareList[index];
                    else
                        $welfare = $welfare + ',' + $welfareList[index];
                }
            }
            $("#welfare").val($welfare);
            $(obj).removeClass("btn-success").addClass("btn-info");
        }
        //单双休选择
        function welfareli(obj) {
            var bid = $(obj).attr("alt");
            if(bid == "welfare_single"){
                addWelfare($("#welfare_single"));
                delWelfare($("#welfare_double"));
            }else{
                delWelfare($("#welfare_single"));
                addWelfare($("#welfare_double"));
            }
            $(".welfare_1").text($(obj).text());
            $("#welfare_btn").addClass("btn-success").removeClass("btn-info");
        }
    </script>
</head>
<body>
<form:form id="inputForm" modelAttribute="postInfo" action="${ctx}/qqc/postInfo/save" method="post"
           class="form-horizontal">
    <form:hidden path="id"/>
    <form:hidden path="companyId"/>
    <sys:message content="${message}"/>
    <table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
        <tbody>
        <tr>
            <td class="width-15 active"><label class="pull-right"><font color="red">*</font>岗位名称：</label></td>
            <td class="width-35">
                <form:input path="postName" htmlEscape="false" maxlength="200" class="form-control required"/>
            </td>
            <td class="width-15 active"><label class="pull-right"><font color="red">*</font>岗位性质：</label></td>
            <td class="width-35">
                <form:select path="postType"  class="form-control required m-b">
                    <form:option value="" label=""/>
                    <form:options items="${fns:getDictList('qqc_post_nature')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
                </form:select>
            </td>
        </tr>
        <tr>
            <td class="width-15 active"><label class="pull-right"><font color="red">*</font>需求人数：</label></td>
            <td class="width-35">
                <form:input path="peopleNum" htmlEscape="false" maxlength="200" class="form-control required number integer"/>
            </td>
            <td class="width-15 active"><label class="pull-right"><font color="red">*</font>工作地：</label></td>
            <td>
                <sys:treeselect id="postPlace" name="placeId" value="${postInfo.placeId}" labelName="postPlace"
                                labelValue="${postInfo.placeName}"
                                title="区域" url="/sys/area/treeData" extId="" cssClass="form-control required m-s"
                                allowClear="true"/>
            </td>
        </tr>
        <tr>
            <td class="width-15 active"><label class="pull-right"><font color="red">*</font>
                <c:choose>
                    <c:when test="${1 eq fromType}">
                        薪资待遇：
                    </c:when>
                    <c:otherwise>
                        实习补贴：
                    </c:otherwise>
                </c:choose>
            </label></td>
            <td colspan="3" class="width-35">
                <form:input path="salaryMin" htmlEscape="false" maxlength="200" class="form-control required"/>&nbsp;&nbsp;
                &nbsp;<%----&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <form:input path="salaryMax" style="width:100px;margin:0;padding:0;text-align:center;"
                            htmlEscape="false" maxlength="200" class="form-control required number"/>&nbsp;&nbsp;元--%>
            </td>
        </tr>

        <c:if test="${1 eq fromType}">
            <tr>
                <td class="width-15 active"><label class="pull-right">岗位福利：</label></td>
                <td colspan="3" class="width-35">
                    <form:textarea path="welfare" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
                </td>
            </tr>
        </c:if>

        <c:if test="${1 eq fromType}">
            <tr>
                <td class="width-15 active"><label class="pull-right">更多福利：</label></td>
                <td colspan="3" class="width-35">
                    <form:textarea path="moreWelfare" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
                </td>
            </tr>
        </c:if>

        <tr>
            <td class="width-15 active"><label class="pull-right">
                <c:choose>
                    <c:when test="${1 eq fromType}">
                        岗位要求：
                    </c:when>
                    <c:otherwise>
                        专业要求：
                    </c:otherwise>
                </c:choose>

            </label></td>
            <td colspan="3" class="width-35">
                <form:textarea path="postDemand" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
            </td>
        </tr>
        <c:if test="${2 eq fromType or 7 eq fromType}">
            <tr>
                <td class="width-15 active"><label class="pull-right">联系人姓名：</label></td>
                <td class="width-35">
                    <form:input path="contactName" htmlEscape="false" maxlength="200" class="form-control"/>
                </td>
                <td class="width-15 active"><label class="pull-right">联系人手机：</label></td>
                <td class="width-35">
                    <form:input path="contactPho" htmlEscape="false" maxlength="200" class="form-control"/>
                </td>
            </tr>
            <tr>
                <td class="width-15 active"><label class="pull-right">HR邮箱：</label></td>
                <td class="width-35">

                    <form:input path="hrEmail" htmlEscape="false" maxlength="200" class="form-control"/>
                </td>
            </tr>
        </c:if>
        <c:if test="${7 eq fromType}">
            <tr>
                <td class="width-15 active"><label class="pull-right">保险缴纳：</label></td>
                <td colspan="3" class="width-35">
                    <form:textarea path="remarks" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
                </td>
            </tr>
        </c:if>
        </tbody>
    </table>
</form:form>
</body>
</html>
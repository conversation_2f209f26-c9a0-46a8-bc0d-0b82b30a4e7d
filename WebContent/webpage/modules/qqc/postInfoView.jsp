<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp" %>
<html>
<head>
    <title>岗位信息</title>
    <meta name="decorator" content="default"/>
    <style type="text/css">
    </style>
</head>
<body>
<form:form id="inputForm" modelAttribute="postInfo" action="" method="post"
           class="form-horizontal">
    <form:hidden path="id"/>
    <form:hidden path="companyId"/>
    <sys:message content="${message}"/>
    <table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
        <tbody>
        <tr>
            <td class="width-15 active"><label class="pull-right"><font color="red">*</font>岗位名称：</label></td>
            <td class="width-35">
                <form:input path="postName" readonly="true" htmlEscape="false" maxlength="200" class="form-control required"/>
            </td>
            <td class="width-15 active"><label class="pull-right"><font color="red">*</font>岗位性质：</label></td>
            <td class="width-35">
                <input readonly="true" class="form-control required" id="postType" name="postType" value="${fns:getDictLabel(postInfo.postType, 'qqc_post_nature', '')}" />
            </td>
        </tr>
        <tr>
            <td class="width-15 active"><label class="pull-right"><font color="red">*</font>需求人数：</label></td>
            <td class="width-35">
                <form:input path="peopleNum" readonly="true" htmlEscape="false" maxlength="200" class="form-control required number integer"/>
            </td>
            <td class="width-15 active"><label class="pull-right"><font color="red">*</font>工作地：</label></td>
            <td>
                <form:input path="placeName" readonly="true" htmlEscape="false" maxlength="200" class="form-control required number integer"/>
            </td>
        </tr>
        <tr>
            <td class="width-15 active"><label class="pull-right"><font color="red">*</font>薪资待遇：</label></td>
            <td colspan="3" class="width-35">
                <form:input path="salaryMin" readonly="true" htmlEscape="false" maxlength="200" class="form-control required"/>&nbsp;&nbsp;
                <%--<form:input readonly="true" path="salaryMin" style="width:100px;margin:0;padding:0;text-align:center;"
                            htmlEscape="false" maxlength="200" class="form-control required number"/>&nbsp;&nbsp;元
                &nbsp;--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <form:input readonly="true" path="salaryMax" style="width:100px;margin:0;padding:0;text-align:center;"
                            htmlEscape="false" maxlength="200" class="form-control required number"/>&nbsp;&nbsp;元--%>
            </td>

        </tr>
        <tr>
            <td class="width-15 active"><label class="pull-right">岗位福利：</label></td>
            <td colspan="3" class="width-35">
                <form:textarea readonly="true" path="welfare" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
            </td>
        </tr>
        <tr>
            <td class="width-15 active"><label class="pull-right">更多福利：</label></td>
            <td colspan="3" class="width-35">
                <form:textarea readonly="true" path="moreWelfare" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
            </td>
        </tr>
        <tr>
            <td class="width-15 active"><label class="pull-right">岗位要求：</label></td>
            <td colspan="3" class="width-35">
                <form:textarea readonly="true" path="postDemand" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
            </td>
        </tr>
        </tbody>
    </table>
</form:form>
</body>
</html>
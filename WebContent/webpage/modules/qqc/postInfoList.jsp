<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>公司信息</title>
	<meta name="decorator" content="default"/>
	<script>
		$(document).ready(function() {
			$("#uploadFile").on('change', function () {
				var filePath = this.value;
				var fileExt = filePath.substring(filePath.lastIndexOf("."))
						.toLowerCase();
				if (!checkFileExt(fileExt)) {
					top.layer.alert('您上传的文件不是Excel文件,请重新上传！', {icon: 0, title:'警告'});
					this.value = "";
					return;
				}
				if (this.files && this.files[0]) {
					var num = Number(this.files[0].size)/1048576;
					if (this.files[0].size > ${fileMaxSize}){
						top.layer.alert('当前上传文件的大小为:'+num.toFixed(2)+'MB,文件最大不能超过'+(Number(${fileMaxSize})/1048576)+'MB', {icon: 0, title:'警告'});
						return false;
					}
				}
				$("#importForm").submit();
				loading('正在导入，请稍等...');
			})
		});
		function importPost(){
			$("#uploadFile").click();
		}
		function checkFileExt(ext) {
			if (!ext.match(/.xlsx|.xls/i)) {
				return false;
			}
			return true;
		}
	</script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
<div class="ibox">
<div class="ibox-title">
		<h5>岗位信息 </h5>
	${fromType}
		<div class="ibox-tools">
			<a class="collapse-link">
				<i class="fa fa-chevron-up"></i>
			</a>
			<a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
				<i class="fa fa-wrench"></i>
			</a>
			<ul class="dropdown-menu dropdown-user">
				<li><a href="#">选项1</a>
				</li>
				<li><a href="#">选项2</a>
				</li>
			</ul>
			<a class="close-link">
				<i class="fa fa-times"></i>
			</a>
		</div>
	</div>
    
    <div class="ibox-content">
	<sys:message content="${message}"/>
	
		<!-- 查询条件 -->
	<div class="row">
	<div class="col-sm-12">
		<form:form id="searchForm" modelAttribute="postInfo" action="" method="post" class="form-inline">
		<input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
			<div class="form-group">
				<span>岗位名称：</span>
					<form:input path="postName" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
				<span>公司名称：</span>
					<form:input path="companyName" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
				<span>工作地：</span>
				<sys:treeselect id="place" name="placeId" value="" labelName="postPlace" labelValue=""
								title="区域" url="/sys/area/treeData?areaId=${fns:getUser().addressId}" extId="" cssClass="form-control m-s" allowClear="true"/>
			</div>
			<%--<div class="form-group" >
				<span>薪资待遇：	</span>
				<form:input path="salaryMin" style="width:100px;margin:0;padding:0;text-align:center;"
							htmlEscape="false" maxlength="200" class="form-control number"/>&nbsp;&nbsp;元
				&nbsp;--&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<form:input path="salaryMax" style="width:100px;margin:0;padding:0;text-align:center;"
							htmlEscape="false" maxlength="200" class="form-control number"/>&nbsp;&nbsp;元
			</div>--%>
	</form:form>
	<br/>
	</div>
	</div>
	
	
	<!-- 工具栏 -->
	<div class="row">
	<div class="col-sm-12">
		<div class="pull-left">
			<button id="btnImport" class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" title="下载模板">
				<c:choose>
					<c:when test="${'7' eq fromType}">
						<a href="https://chuang.qq.zjgqt.org/model/qqchuang_post_model3.xls">
							<i class="fa fa-folder-open-o"></i> 下载模板
						</a>
					</c:when>
					<c:otherwise>
						<a href="https://chuang.qq.zjgqt.org/model/qqchuang_post_model2.xls">
							<i class="fa fa-folder-open-o"></i> 下载模板
						</a>
					</c:otherwise>
				</c:choose>
			</button>
			<button id="btnImport" onclick="importPost()" class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" title="导入"><i class="fa fa-folder-open-o"></i> 导入</button>
			<div id="importBox" class="hide">
				<form id="importForm" action="${ctx}/qqc/postInfo/importV2?fromType=${fromType}" method="post" enctype="multipart/form-data"
					  style="padding-left:20px;text-align:center;" ><br/>
					<input id="uploadFile" name="file" type="file" style="width:330px"/>导入文件不能超过5M，仅允许导入“xls”或“xlsx”格式文件！<br/>　　
				</form>
			</div>
			<shiro:hasPermission name="qqc:postInfo:del">
				<table:delRow url="${ctx}/qqc/postInfo/deleteAll?fromType=${fromType}" id="postInfoTable"></table:delRow><!-- 删除按钮 -->
			</shiro:hasPermission>
				<%-- <table:importExcel url="${ctx}/qqc/companyInfo/import"></table:importExcel><!-- 导入按钮 -->
	       		<table:exportExcel url="${ctx}/qqc/companyInfo/export"></table:exportExcel><!-- 导出按钮 --> --%>
	       		<!-- <button class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" onclick="sortOrRefresh()" title="刷新"><i class="glyphicon glyphicon-repeat"></i> 刷新</button> -->
			</div>
		<div class="pull-right">
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
		</div>
	</div>
	</div>
		<c:choose>
			<c:when test="${'1' eq fromType}">
				<table id="postInfoTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
					<thead>
					<tr>
						<th> <input type="checkbox" class="i-checks"></th>
						<!-- <th>岗位编号</th> -->
						<th>岗位名称</th>
						<th>公司名称</th>
						<th>岗位性质</th>
						<th>工作地</th>
						<th>需求人数(个)</th>
						<th>薪资待遇(元)</th>
						<th>操作</th>
					</tr>
					</thead>
					<tbody>
					<c:forEach items="${page.list}" var="postInfo">
						<tr>
							<td> <input type="checkbox" id="${postInfo.id}" class="i-checks"></td>
							<td><a  href="#" onclick="openDialogView('查看岗位', '${ctx}/qqc/postInfo/form?type=View&id=${postInfo.id}','800px', '700px')">
									${fns:abbr(postInfo.postName,50)}
							</a></td>
							<td>
								<a href="#" onclick="openDialogView('查看公司信息', '${ctx}/qqc/companyInfo/form?type=View&id=${postInfo.companyId}','800px', '700px')">
										${fns:abbr(postInfo.companyName,50)}
								</a>
							</td>
							<td>
									${fns:getDictLabel(postInfo.postType, 'qqc_post_nature', '')}
							</td>
							<td>
									${fns:abbr(postInfo.placeName,50)}
							</td>
							<td>
									${fns:abbr(postInfo.peopleNum,50)}
							</td>
							<td>
									${fns:abbr(postInfo.salaryMin,50)}
							</td>
							<td>
								<shiro:hasPermission name="qqc:postInfo:view">
									<a href="#" onclick="openDialogView('查看信息', '${ctx}/qqc/postInfo/form?type=View&id=${postInfo.id}','800px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
								</shiro:hasPermission>
								<shiro:hasPermission name="qqc:postInfo:edit">
									<a href="#" onclick="openDialog('修改信息', '${ctx}/qqc/postInfo/form?type=Form&id=${postInfo.id}&fromType=1','800px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
								</shiro:hasPermission>
								<shiro:hasPermission name="qqc:postInfo:del">
									<a href="${ctx}/qqc/postInfo/delete?id=${postInfo.id}&fromType=${fromType}" onclick="return confirmx('确认要删除该信息吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
								</shiro:hasPermission>
							</td>
						</tr>
					</c:forEach>
					</tbody>
				</table>
			</c:when>
			<c:when test="${'7' eq fromType}">
				<table id="postInfoTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
					<thead>
					<tr>
						<th> <input type="checkbox" class="i-checks"></th>
						<th>岗位编号</th>
						<th>岗位名称</th>
						<th>岗位类型</th>
						<th>公司名称</th>
						<th>专业要求</th>
						<th>工作地</th>
						<th>需求人数(个)</th>
						<th>保险缴纳</th>
						<th>操作</th>
					</tr>
					</thead>
					<tbody>
					<c:forEach items="${page.list}" var="postInfo">
						<tr>
							<td> <input type="checkbox" id="${postInfo.id}" class="i-checks"></td>
							<td>${postInfo.postNo}</td>
							<td><a  href="#" onclick="openDialogView('查看岗位', '${ctx}/qqc/postInfo/form?type=View&id=${postInfo.id}','800px', '700px')">
									${fns:abbr(postInfo.postName,50)}
							</a></td>
							<td>
									${fns:getDictLabel(postInfo.postType, 'qqc_post_nature', '')}
							</td>
							<td>
								<a href="#" onclick="openDialogView('查看公司信息', '${ctx}/qqc/companyInfo/form?type=View&id=${postInfo.companyId}','800px', '700px')">
									<c:choose>
										<c:when test="${postInfo.companyName ne null}">
											${fns:abbr(postInfo.companyName,50)}
										</c:when>
										<c:otherwise>
											${fns:abbr(postInfo.companyName2,50)}
										</c:otherwise>
									</c:choose>
								</a>
							</td>
							<td>
									${postInfo.postDemand}
							</td>
							<td>
									${fns:abbr(postInfo.placeName,50)}
							</td>
							<td>
									${fns:abbr(postInfo.peopleNum,50)}
							</td>
							<td>
									${fns:abbr(postInfo.remarks,50)}
							</td>
							<td>
								<shiro:hasPermission name="qqc:postInfo:view">
									<a href="#" onclick="openDialogView('查看信息', '${ctx}/qqc/postInfo/form?type=View&id=${postInfo.id}','800px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
								</shiro:hasPermission>
								<shiro:hasPermission name="qqc:postInfo:edit">
									<a href="#" onclick="openDialog('修改信息', '${ctx}/qqc/postInfo/form?type=Form&id=${postInfo.id}&fromType=${fromType}','800px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
								</shiro:hasPermission>
								<shiro:hasPermission name="qqc:postInfo:del">
									<a href="${ctx}/qqc/postInfo/delete?id=${postInfo.id}&fromType=${fromType}" onclick="return confirmx('确认要删除该信息吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
								</shiro:hasPermission>
							</td>
						</tr>
					</c:forEach>
					</tbody>
				</table>
			</c:when>
			<c:otherwise>
				<table id="postInfoTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
					<thead>
					<tr>
						<th> <input type="checkbox" class="i-checks"></th>
						<th>岗位编号</th>
						<th>岗位名称</th>
						<th>岗位类型</th>
						<th>公司名称</th>
						<th>专业要求</th>
						<th>工作地</th>
						<th>需求人数(个)</th>
						<th>实习补贴</th>
						<th>操作</th>
					</tr>
					</thead>
					<tbody>
					<c:forEach items="${page.list}" var="postInfo">
						<tr>
							<td> <input type="checkbox" id="${postInfo.id}" class="i-checks"></td>
							<td>${postInfo.postNo}</td>
							<td><a  href="#" onclick="openDialogView('查看岗位', '${ctx}/qqc/postInfo/form?type=View&id=${postInfo.id}','800px', '700px')">
									${fns:abbr(postInfo.postName,50)}
							</a></td>
							<td>
									${fns:getDictLabel(postInfo.postType, 'qqc_post_nature', '')}
							</td>
							<td>
								<a href="#" onclick="openDialogView('查看公司信息', '${ctx}/qqc/companyInfo/form?type=View&id=${postInfo.companyId}','800px', '700px')">
										<c:choose>
											<c:when test="${postInfo.companyName ne null}">
												${fns:abbr(postInfo.companyName,50)}
											</c:when>
											<c:otherwise>
												${fns:abbr(postInfo.companyName2,50)}
											</c:otherwise>
										</c:choose>
								</a>
							</td>
							<td>
									${postInfo.postDemand}
							</td>
							<td>
									${fns:abbr(postInfo.placeName,50)}
							</td>
							<td>
									${fns:abbr(postInfo.peopleNum,50)}
							</td>
							<td>
									${fns:abbr(postInfo.salaryMin,50)}
							</td>
							<td>
								<shiro:hasPermission name="qqc:postInfo:view">
									<a href="#" onclick="openDialogView('查看信息', '${ctx}/qqc/postInfo/form?type=View&id=${postInfo.id}','800px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
								</shiro:hasPermission>
								<shiro:hasPermission name="qqc:postInfo:edit">
									<a href="#" onclick="openDialog('修改信息', '${ctx}/qqc/postInfo/form?type=Form&id=${postInfo.id}&fromType=${fromType}','800px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
								</shiro:hasPermission>
								<shiro:hasPermission name="qqc:postInfo:del">
									<a href="${ctx}/qqc/postInfo/delete?id=${postInfo.id}&fromType=${fromType}" onclick="return confirmx('确认要删除该信息吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
								</shiro:hasPermission>
							</td>
						</tr>
					</c:forEach>
					</tbody>
				</table>
			</c:otherwise>
		</c:choose>
	<!-- 分页代码 -->
	<table:page page="${page}"></table:page>
	<br/>
	<br/>
	</div>
	</div>
</div>
</body>
</html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>公司信息</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
		#img{
			height: 20px;
			width: 20px;
			margin: 0 auto;
			vertical-align:bottom;
		}
	</style>

	<script>
		$(document).ready(function() {
			$("#uploadFile").on('change', function () {
				var filePath = this.value;
				var fileExt = filePath.substring(filePath.lastIndexOf("."))
						.toLowerCase();
				if (!checkFileExt(fileExt)) {
					top.layer.alert('您上传的文件不是Excel文件,请重新上传！', {icon: 0, title:'警告'});
					this.value = "";
					return;
				}
				if (this.files && this.files[0]) {
					var num = Number(this.files[0].size)/1048576;
					if (this.files[0].size > ${fileMaxSize}){
						top.layer.alert('当前上传文件的大小为:'+num.toFixed(2)+'MB,文件最大不能超过'+(Number(${fileMaxSize})/1048576)+'MB', {icon: 0, title:'警告'});
						return false;
					}
				}
				$("#importForm").submit();
				loading('正在导入，请稍等...');
			})
		});

		function checkFileExt(ext) {
			if (!ext.match(/.xlsx|.xls/i)) {
				return false;
			}
			return true;
		}

		function applicationStatus(id,statusText,status){
			top.layer.confirm('是否'+ statusText + '?', {icon: 3, title:'系统提示'}, function(index){
				window.location = "${ctx}/qqc/companyInfo/applicationStatus?id="+id + "&status="+ status;
				top.layer.close(index);
			});
		}

		function importCompany(){
			$("#uploadFile").click();
		}

		function deduplication(){
			var xhr = new XMLHttpRequest();
			xhr.open('POST', '${ctx}/qqc/companyInfo/deduplication',true);
			xhr.responseType = 'blob';
			xhr.setRequestHeader('Content-Type', 'application/json;charset=utf-8');

			//回传的数据加载完毕后执行
			xhr.onload = function () {
				if (this.status === 200) {
					var blob = this.response;
					var reader = new FileReader();
					reader.readAsDataURL(blob); // 转换为base64，可以直接放入a标签href
					reader.onload = function (e) {
						var a = document.createElement("a"); // 转换完成，创建一个a标签用于下载
						a.download = new Date(parseInt(new Date().getTime())).toLocaleString().replace(/:\d{1,2}$/,' ')+"删除重复记录.xls";
						a.href = e.target.result;
						$("body").append(a); // 修复firefox中无法触发click
						a.click();
						$(a).remove();
					};
				}else{
				}
				top.layer.close(loadingIndex);
				location.reload();
			}

			//设置参数
			//发送
			var loadingIndex = top.layer.load(2, { //icon支持传入0-2
				content: '正在导出操作记录...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'paddingTop': '40px',
						'width': '120px',
						'textAlign': 'center',
						'backgroundPositionX': 'center'
					});
				}
			});
			xhr.send();
		}
	</script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
<div class="ibox">
<div class="ibox-title">
		<h5>公司信息 </h5>
		<div class="ibox-tools">
			<a class="collapse-link">
				<i class="fa fa-chevron-up"></i>
			</a>
			<a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
				<i class="fa fa-wrench"></i>
			</a>
			<ul class="dropdown-menu dropdown-user">
				<li><a href="#">选项1</a>
				</li>
				<li><a href="#">选项2</a>
				</li>
			</ul>
			<a class="close-link">
				<i class="fa fa-times"></i>
			</a>
		</div>
	</div>
    
    <div class="ibox-content">
	<sys:message content="${message}"/>
	
	<!-- 查询条件 -->
	<div class="row">
	<div class="col-sm-12">
		<form:form id="searchForm" modelAttribute="companyInfo" action="" method="post" class="form-inline">
		<input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
		<div class="form-group">
			<span>公司名称：</span>
				<form:input autocomplete="off" path="companyName" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
			<span>区域：</span>
				<sys:treeSelectMulti id="workplace" name="workplaceId" value="${companyInfo.workplaceId}" labelName="workplaceName" labelValue="${companyInfo.workplaceName}"
					title="区域" url="/sys/area/treeData?areaId=${fns:getUser().addressId}" extId="" cssClass="form-control m-s" allowClear="true"/>
			<span>状态：</span>
				<form:select path="status"  class="form-control m-b">
					<form:option value="" label=""/>
					<form:options items="${fns:getDictList('qqc_company_status')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
				</form:select>
		 </div>
	</form:form>
	<br/>
	</div>
	</div>
	
	
	<!-- 工具栏 -->
	<div class="row">
	<div class="col-sm-12">
		<div class="pull-left">
			<button id="btnImport" class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" title="下载模板">
				<a href="https://chuang.qq.zjgqt.org/model/qqchuang_company_model.xlsx">
					<i class="fa fa-folder-open-o"></i> 下载模板
				</a>

			</button>
			<button id="btnImport" onclick="importCompany()" class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" title="导入"><i class="fa fa-folder-open-o"></i> 导入</button>
			<div id="importBox" class="hide">
				<form id="importForm" action="${ctx}/qqc/companyInfo/importCompany" method="post" enctype="multipart/form-data"
					  style="padding-left:20px;text-align:center;" ><br/>
					<input id="uploadFile" name="file" type="file" style="width:330px"/>导入文件不能超过5M，仅允许导入“xls”或“xlsx”格式文件！<br/>　　
				</form>
			</div>
			<shiro:hasPermission name="qqc:companyInfo:add">
				<table:addRow url="${ctx}/qqc/companyInfo/form?type=Form" title="公司"></table:addRow><!-- 增加按钮 -->
			</shiro:hasPermission>
			<shiro:hasPermission name="qqc:companyInfo:del">
				<table:delRow url="${ctx}/qqc/companyInfo/deleteAll" id="companyInfoTable"></table:delRow><!-- 删除按钮 -->
			</shiro:hasPermission>
			<shiro:hasPermission name="qqc:companyInfo:del">
				<button class="btn btn-white btn-sm" onclick="deduplication()" data-toggle="tooltip" data-placement="top"><i class="fa fa-trash-o"> 删除重复</i>
				</button>
			</shiro:hasPermission>
				<%-- <table:importExcel url="${ctx}/qqc/companyInfo/import"></table:importExcel><!-- 导入按钮 -->
	       		<table:exportExcel url="${ctx}/qqc/companyInfo/export"></table:exportExcel><!-- 导出按钮 --> --%>
	       		<!-- <button class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" onclick="sortOrRefresh()" title="刷新"><i class="glyphicon glyphicon-repeat"></i> 刷新</button> -->
			</div>
		<div class="pull-right">
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
		</div>
	</div>
	</div>
	
	
	
	
	<table id="companyInfoTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
		<thead>
			<tr>
				<th> <input type="checkbox" class="i-checks"></th>
				<th>公司名称</th>
				<th>公司地址</th>
				<th>联系人姓名</th>
				<th>联系人电话</th>
				<th>联系人邮箱</th>
				<th>状态</th>
				<th>操作</th>
			</tr>
		</thead>
		<tbody>
		<c:forEach items="${page.list}" var="companyInfo">
			<tr>
				<td> <input type="checkbox" id="${companyInfo.id}" class="i-checks"></td>
				<td><a href="#" onclick="openDialogView('查看通知', '${ctx}/qqc/companyInfo/form?type=View&id=${companyInfo.id}','800px', '700px')">
					<c:if test="${companyInfo.isUpBusiness == '2'}">
						<img title="警告:未上传营业执照" id="img" src="${ctxStatic}/img/1.jpg">
					</c:if>
					${fns:abbr(companyInfo.companyName,50)}
				</a></td>
				<td>
					${companyInfo.indexAddress}
				</td>
				<td>
					${fns:abbr(companyInfo.contactsName,50)}
				</td>
				<td>
					${fns:abbr(companyInfo.contactsPhone,50)}
				</td>
				<td>
					${fns:abbr(companyInfo.contactsEmail,50)}
				</td>
				<td>
					${fns:getDictLabel(companyInfo.status, 'qqc_company_status', '')}
				</td>
				<td>
					<c:if test="${companyInfo.status == '2'}">
						<a onclick="applicationStatus('${companyInfo.id}','${fns:getDictLabel('3', 'qqc_company_status', '')}','3')" class="btn btn-info btn-xs">通过</a>
						<a onclick="applicationStatus('${companyInfo.id}','${fns:getDictLabel('1', 'qqc_company_status', '')}','1')" class="btn btn-info btn-xs">不通过</a>
					</c:if>
					<shiro:hasPermission name="qqc:companyInfo:edit">
    					<a title="修改信息" href="#" onclick="openDialog('修改信息', '${ctx}/qqc/companyInfo/form?type=Form&id=${companyInfo.id}','800px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
    				</shiro:hasPermission>
    				<shiro:hasPermission name="qqc:companyInfo:del">
						<a title="删除信息" href="${ctx}/qqc/companyInfo/delete?id=${companyInfo.id}" onclick="return confirmx('确认要删除该信息吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
					</shiro:hasPermission>
					<shiro:hasPermission name="qqc:companyInfo:post">
					<a title="发布岗位信息" href="#" onclick="openDialog('发布岗位信息', '${ctx}/qqc/postInfo/form?type=Form&companyId=${companyInfo.id}','800px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-plus"></i></a>
					</shiro:hasPermission>
				</td>
			</tr>
		</c:forEach>
		</tbody>
	</table>
	<!-- 分页代码 -->
	<table:page page="${page}"></table:page>
	<br/>
	<br/>
	</div>
	</div>
</div>
</body>
</html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>岗位申请列表</title>
	<meta name="decorator" content="default"/>
	<script>
		function applicationStatus(id,statusText,status){
			top.layer.confirm('是否'+ statusText + '?', {icon: 3, title:'系统提示'}, function(index){
				window.location = "${ctx}/post/application/applicationStatus?id="+id + "&status="+ status;
				top.layer.close(index);
			});
		}

		function downloadFile(){
			var xhr = new XMLHttpRequest();
			xhr.open('POST', '${ctx}/post/application/downloadFile',true);
			xhr.responseType = 'blob';
			xhr.setRequestHeader('Content-Type', 'application/json;charset=utf-8');

			//回传的数据加载完毕后执行
			xhr.onload = function () {
				if (this.status === 200) {
					var blob = this.response;
					var reader = new FileReader();
					reader.readAsDataURL(blob); // 转换为base64，可以直接放入a标签href
					reader.onload = function (e) {
						var a = document.createElement("a"); // 转换完成，创建一个a标签用于下载
						a.download = new Date(parseInt(new Date().getTime())).toLocaleString().replace(/:\d{1,2}$/,' ')+"申请记录.xls";
						a.href = e.target.result;
						$("body").append(a); // 修复firefox中无法触发click
						a.click();
						$(a).remove();
					};
				}else{
				}
				top.layer.close(loadingIndex);
				location.reload();
			}

			//设置参数
			//发送
			var loadingIndex = top.layer.load(2, { //icon支持传入0-2
				content: '正在导出记录...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'paddingTop': '40px',
						'width': '120px',
						'textAlign': 'center',
						'backgroundPositionX': 'center'
					});
				}
			});
			//设置参数
			var data = {
				postName:$("#postName").val(),
				companyName:$("#companyName").val(),
				post:{
					placeId:$("#placeId").val()
				}
			}
			//发送
			xhr.send(JSON.stringify(data));
		}
	</script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
<div class="ibox">
<div class="ibox-title">
		<h5>岗位申请列表 </h5>
		<div class="ibox-tools">
			<a class="collapse-link">
				<i class="fa fa-chevron-up"></i>
			</a>
			<a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
				<i class="fa fa-wrench"></i>
			</a>
			<ul class="dropdown-menu dropdown-user">
				<li><a href="#">选项1</a>
				</li>
				<li><a href="#">选项2</a>
				</li>
			</ul>
			<a class="close-link">
				<i class="fa fa-times"></i>
			</a>
		</div>
	</div>
    
    <div class="ibox-content">
	<sys:message content="${message}"/>
	
		<!-- 查询条件 -->
	<div class="row">
	<div class="col-sm-12">
		<form:form id="searchForm" modelAttribute="postApplication" action="" method="post" class="form-inline">
		<input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
			<div class="form-group">
				<span>岗位名称：</span>
					<form:input path="postName" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
				<span>公司名称：</span>
					<form:input path="companyName" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
				<span>工作地：</span>
				<sys:treeselect id="place" name="post.placeId" value="${postApplication.post.placeId}" labelName="post.placeName" labelValue="${postApplication.post.placeName}"
								title="区域" url="/sys/area/treeData?areaId=${fns:getUser().addressId}" extId="" cssClass="form-control m-s" allowClear="true"/>
			</div>
	</form:form>
	<br/>
	</div>
	</div>
	
	
	<!-- 工具栏 -->
	<div class="row">
	<div class="col-sm-12">
		<div class="pull-left">
			<button id="btnExport" onclick="downloadFile()" class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" title="导出"><i class="fa fa-file-excel-o"></i> 导出</button>
			<shiro:hasPermission name="qqc:postApplication:del">
				<table:delRow url="${ctx}/post/Application/deleteAll" id="postInfoTable"></table:delRow><!-- 删除按钮 -->
			</shiro:hasPermission>
				<%-- <table:importExcel url="${ctx}/qqc/companyInfo/import"></table:importExcel><!-- 导入按钮 -->
	       		<table:exportExcel url="${ctx}/qqc/companyInfo/export"></table:exportExcel><!-- 导出按钮 --> --%>
	       		<!-- <button class="btn btn-white btn-sm " data-toggle="tooltip" data-placement="left" onclick="sortOrRefresh()" title="刷新"><i class="glyphicon glyphicon-repeat"></i> 刷新</button> -->
			</div>
		<div class="pull-right">
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
		</div>
	</div>
	</div>
	
	
	
	
	<table id="postInfoTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
		<thead>
			<tr>
				<th> <input type="checkbox" class="i-checks"></th>
				<!-- <th>岗位编号</th> -->
				<th>岗位名称</th>
				<th>公司名称</th>
				<th>工作地</th>
				<th>申请人</th>
				<th>申请时间</th>
				<!-- <th>状态</th>
				<th>操作</th> -->
			</tr>
		</thead>
		<tbody>
		<c:forEach items="${page.list}" var="postApplication">
			<tr>
				<td> <input type="checkbox" id="${postInfo.id}" class="i-checks"></td>
				<td><a  href="#" onclick="openDialogView('查看岗位', '${ctx}/qqc/postInfo/form?type=View&id=${postApplication.post.id}','800px', '700px')">
						${fns:abbr(postApplication.post.postName,50)}
				</a></td>
				<td>
					<a href="#" onclick="openDialogView('查看公司信息', '${ctx}/qqc/companyInfo/form?type=View&id=${postApplication.company.id}','800px', '700px')">
						${fns:abbr(postApplication.company.companyName,50)}
					</a>
				</td>
				<td>
					${fns:abbr(postApplication.post.placeName,50)}
				</td>
				<td>
						<c:choose>
					    <c:when test="${empty postApplication.wechatUser.nickname}">
					         ${fns:abbr(postApplication.postName,50)}
					    </c:when>
					    <c:otherwise>
					        <a href="#" onclick="openDialogView('查看申请人信息', '${ctx}/sys/wechatuser/form?type=View&id=${postApplication.wechatUser.id}&rid=${postApplication.resumeId}','800px', '700px')">
						         ${fns:abbr(postApplication.wechatUser.nickname,50)}
					        </a>
					    </c:otherwise>
					</c:choose>
					
				</td>
				<td>
					<fmt:formatDate pattern='yyyy-MM-dd HH:mm:ss'  value='${postApplication.createDate}'/>

				</td>
				<%-- <td>
					${postApplication.status == '0'?'未处理':'已处理'}
				</td>
				<td>
					 <shiro:hasPermission name="qqc:postInfo:view">
						<a href="#" onclick="openDialogView('查看信息', '${ctx}/post/Application/form?id=${postApplication.id}','800px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
					 </shiro:hasPermission>
    				 <shiro:hasPermission name="qqc:postInfo:del">
						<a href="${ctx}/post/application/delete?id=${postApplication.id}" onclick="return confirmx('确认要删除该信息吗？', this.href)"   class="btn btn-danger btn-xs ">删除</a>
					 </shiro:hasPermission>
					 <shiro:hasPermission name="qqc:postApplication:status">
						<a onclick="applicationStatus('${postApplication.id}','${postApplication.status == '0'?'通过':'不通过'}','${postApplication.status == '0'?'1':'0'}')" class="btn btn-info btn-xs">${postApplication.status == '0'?'通过':'不通过'}</a>
					 </shiro:hasPermission>
				</td> --%>
			</tr>
		</c:forEach>
		</tbody>
	</table>
	<!-- 分页代码 -->
	<table:page page="${page}"></table:page>
	<br/>
	<br/>
	</div>
	</div>
</div>
</body>
</html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>赛事报名详情列表</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
		.direct-content {
			padding: 15px;
		}
		.layui-layer-content .form-horizontal .form-group {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }
		.audit-form .form-group {
			margin-bottom: 15px;
		}
		.audit-form .control-label {
			padding-top: 7px;
			margin-bottom: 0;
			text-align: right;
		}
		.reject-reason {
			display: none;
		}
		.form-section {
            margin: 20px 0 15px;
            padding-bottom: 8px;
        }
        .page-title {
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
        .search-area {
            margin-bottom: 15px;
        }
        .table-responsive {
            margin-top: 15px;
        }
        #auditDialog {
            overflow-x: hidden;
            width: 100%;
            box-sizing: border-box;
        }
	</style>
	<script type="text/javascript">
		$(document).ready(function() {
			//外部js调用
			laydate({
				elem: '#beginDate', //目标元素
				event: 'focus' //响应事件。如果没有传入event，则按照默认的click
			});
			laydate({
				elem: '#endDate', //目标元素
				event: 'focus' //响应事件。如果没有传入event，则按照默认的click
			});

			// 审核状态选择变化时显示或隐藏拒绝理由
			$('input[name="status"]').change(function() {
				if($(this).val() == '2') { // 2表示审核不通过
					$('.reject-reason').show();
				} else {
					$('.reject-reason').hide();
				}
			});
		});

		// 自定义搜索函数
		function search() {
			// 确保表单中包含competitionId参数
			if (!$("#competitionId").val() && $("#hdId").val()) {
				$("#competitionId").val($("#hdId").val());
			}
			$("#searchForm").submit();
		}

		// 重置表单
		function reset() {
			// 保留hdId和competitionId
			var hdId = $("#hdId").val();
			var competitionId = $("#competitionId").val() || hdId;
			$("#searchForm")[0].reset();
			$("#hdId").val(hdId);
			$("#competitionId").val(competitionId);
			$("#searchForm").submit();
		}

		// 打开审核弹窗
		// 打开审核弹窗
		function openAuditDialog(id, projectName) {
			// $('#auditForm')[0].reset();
			// $('.reject-reason').hide();

			$('#registrationId').val(id);

			// 打开弹窗
			var index = top.layer.open({
				type: 1,
				title: '审核报名',
				area: ['900px', '600px'],
				shadeClose: false,
				scrollbar: false,
				content: $('#auditDialog').html(), //

				success: function(layero, index){

					layero.find('.form-group').css({
						'margin-left': '0',
						'margin-right': '0'
					});
					// layero 是当前弹窗的 jQuery 对象
					var form = layero.find('form'); // 找到弹窗内的表单

					form.find('#projectName').text(projectName);

					form.find('input[name="status"]').change(function() {
						if($(this).val() == '-1') { // -1表示退回
							form.find('.reject-reason').show();
						} else {
							form.find('.reject-reason').hide();
						}
					});
				},

				btn: ['确定', '取消'],

				// 使用 yes 回调函数处理确定按钮的逻辑
				yes: function(index, layero) {
					var auditStatus = layero.find('input[name="status"]:checked').val();
					var rejectReason = layero.find('#rejectReason').val();

					// 执行验证
					if(!auditStatus) {
						top.layer.msg('请选择审核结果', {icon: 2});
						return; // 阻止弹窗关闭
					}

					if(auditStatus == '-1' && !rejectReason) {
						top.layer.msg('请填写退回理由', {icon: 2});
						return; // 阻止弹窗关闭
					}

					// 验证通过，手动关闭弹窗
					top.layer.close(index);

					// 显示加载中
					var loadingIndex = top.layer.load(1, {
						shade: [0.1, '#fff']
					});

					// 使用AJAX提交表单
					$.ajax({
						url: '${ctx}/qqc/registration/audit',
						type: 'POST',
						data: {
							id: $('#registrationId').val(), // 这个ID是在弹窗外部设置的，可以直接获取
							status: auditStatus,          // 使用从弹窗内获取的值
							rejectReason: rejectReason    // 使用从弹窗内获取的值
						},
						success: function(result) {
							top.layer.close(loadingIndex);
							if(result.success) {
								top.layer.msg('审核操作成功', {icon: 1});
								location.reload();
							} else {
								top.layer.msg('审核操作失败: ' + result.msg, {icon: 2});
							}
						},
						error: function() {
							top.layer.close(loadingIndex);
							top.layer.msg('审核操作失败，请稍后重试', {icon: 2});
							location.reload();
						}
					});
				},
				cancel: function(index) {
					// 取消按钮默认就会关闭弹窗，无需额外代码
				}
			});
		}
	</script>
</head>
<body>
<div class="direct-content">
    <h3 class="page-title">${competitionInfo.title} - 报名详情列表</h3>

    <sys:message content="${message}"/>

	<!-- 查询条件 -->
	<div class="row search-area">
	    <div class="col-sm-12">
	        <form:form id="searchForm" modelAttribute="competitionRegistration" action="${ctx}/qqc/competition/registrationList" method="post" class="form-inline">
		        <input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
		        <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
		        <input id="hdId" name="hdId" type="hidden" value="${competitionRegistration.hdId}"/>
		        <input id="competitionId" name="competitionId" type="hidden" value="${competitionRegistration.hdId}"/>
		        <div class="form-group">
			        <span>参赛者姓名：</span>
			        <form:input path="firstApplicantName" cssStyle="cursor: pointer" htmlEscape="false" maxlength="50" class="form-control input-sm"/>
			        <span>参赛项目：</span>
			        <form:input path="participantProject" cssStyle="cursor: pointer" htmlEscape="false" maxlength="255" class="form-control input-sm"/>
		        </div>
	        </form:form>
	    </div>
	</div>

	<!-- 工具栏 -->
	<div class="row">
	    <div class="col-sm-12">
		    <div class="pull-right">
			    <button class="btn btn-primary btn-rounded btn-outline btn-sm" onclick="search()"><i class="fa fa-search"></i> 查询</button>
			    <button class="btn btn-primary btn-rounded btn-outline btn-sm" onclick="reset()"><i class="fa fa-refresh"></i> 重置</button>
		    </div>
	    </div>
	</div>

	<div class="table-responsive">
	    <table id="registrationTable" class="table table-striped table-bordered table-hover table-condensed dataTables-example dataTable no-footer">
		    <thead>
			    <tr>
				    <th>参赛项目</th>
				    <th>报名途径</th>
				    <th>参赛地区</th>
				    <th>赛事分组</th>
				    <th>第一申报人</th>
				    <th>联系方式</th>
				    <th>公司名称</th>
				    <th>项目简介</th>
				    <th>审核状态</th>
				    <th>操作</th>
			    </tr>
		    </thead>
		    <tbody>
		    <c:forEach items="${page.list}" var="registration">
		        <c:if test="${registration.status != 0}">
			    <tr>
				    <td>${fns:abbr(registration.participantProject, 20)}</td>
				    <td>${fns:getDictLabel(registration.registrationChannel, 'registration_channel', '')}</td>
				    <td>${registration.competitionCityName} ${registration.competitionDistrictName}</td>
				    <td>${fns:getDictLabel(registration.competitionGroup, 'competition_group', '')} ${fns:getDictLabel(registration.competitionSubgroup, 'competition_subgroup', '')}</td>
				    <td>${registration.firstApplicantName}</td>
				    <td>${registration.firstApplicantMobile}</td>
				    <td>${fns:abbr(registration.companyName, 20)}</td>
				    <td>${fns:abbr(registration.projectBrief, 30)}</td>
				    <td>
					    <c:choose>
						    <c:when test="${registration.status == 0}">
							    <span class="label label-default">草稿</span>
						    </c:when>
						    <c:when test="${registration.status == 1}">
							    <span class="label label-warning">待审核</span>
						    </c:when>
						    <c:when test="${registration.status == 3}">
							    <span class="label label-success">审核通过</span>
						    </c:when>
						    <c:when test="${registration.status == -1}">
							    <span class="label label-danger">已退回</span>
						    </c:when>
						    <c:otherwise>
							    <span class="label label-default">未知状态</span>
						    </c:otherwise>
					    </c:choose>
				    </td>
				    <td>
					    <shiro:hasPermission name="qqc:competition:view">
						    <a title="查看详情" href="#" onclick="openDialogView('报名详情', '${ctx}/qqc/registration/view?id=${registration.id}','1100px', '900px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
					    </shiro:hasPermission>
					    <shiro:hasPermission name="qqc:competition:edit">
						    <a title="编辑报名" href="#" onclick="openDialog('编辑报名信息', '${ctx}/qqc/registration/form?id=${registration.id}','1100px', '900px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
					    </shiro:hasPermission>
					    <shiro:hasPermission name="qqc:competition:del">
						    <a title="删除报名" href="${ctx}/qqc/registration/delete?id=${registration.id}" onclick="return confirmx('确认要删除该报名信息吗？', this.href)" class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
					    </shiro:hasPermission>
					    <shiro:hasPermission name="qqc:competition:edit">
						    <a title="审核报名" href="#" onclick="openAuditDialog('${registration.id}', '${registration.participantProject}')" class="btn btn-primary btn-xs btn-circle" ><i class="fa fa-check-square-o"></i></a>
					    </shiro:hasPermission>
				    </td>
			    </tr>
			    </c:if>
		    </c:forEach>
		    </tbody>
	    </table>
	</div>

	<!-- 分页代码 -->
	<table:page page="${page}"></table:page>
</div>

<!-- 审核弹窗 -->
<div id="auditDialog" style="display: none; padding: 20px;">
	<form id="auditForm" class="form-horizontal audit-form">
		<input type="hidden" id="registrationId" name="id">
		<div class="form-group">
			<label class="col-sm-3 control-label">项目名称：</label>
			<div class="col-sm-9">
				<p class="form-control-static" id="projectName"></p>
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-3 control-label">审核结果：</label>
			<div class="col-sm-9">
				<div class="radio">
					<label>
						<input type="radio" name="status" value="3"> 通过
					</label>
				</div>
				<div class="radio">
					<label>
						<input type="radio" name="status" value="-1"> 退回
					</label>
				</div>
			</div>
		</div>
		<div class="form-group reject-reason">
			<label class="col-sm-3 control-label">退回理由：</label>
			<div class="col-sm-9">
				<textarea id="rejectReason" name="rejectReason" class="form-control" rows="5" placeholder="请填写退回理由"></textarea>
			</div>
		</div>
	</form>
</div>
</body>
</html>
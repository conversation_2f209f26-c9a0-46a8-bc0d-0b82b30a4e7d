<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <title>报名信息编辑</title>

    <style type="text/css">
        .form-section {
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0 15px;
            border-bottom: 1px solid #e5e5e5;
            padding-bottom: 8px;
            color: #333;
        }
        .required-field {
            color: red;
        }
        .file-preview {
            margin-top: 10px;
        }
        .file-preview img {
            max-width: 300px;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
        }
        .remove-file {
            margin-top: 5px;
        }
        .member-item, .mentor-item {
            margin-bottom: 20px;
        }
        .panel {
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 1px 1px rgba(0,0,0,.05);
        }
        .panel-heading {
            padding: 10px 15px;
            border-bottom: 1px solid #ddd;
            background-color: #f5f5f5;
        }
        .panel-body {
            padding: 15px;
        }
        .panel-title {
            margin: 0;
            font-size: 16px;
        }
        .remove-member, .remove-mentor {
            margin-left: 10px;
        }
        .add-btn {
            background-color: #337ab7;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .add-btn:hover {
            background-color: #286090;
        }
        .hide {
            display: none;
        }

        /* HTML5日期控件样式 */
        input[type="date"] {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 14px;
            line-height: 1.42857143;
            color: #555;
            width: 100%;
        }

        input[type="date"]::-webkit-calendar-picker-indicator {
            cursor: pointer;
            border-radius: 4px;
            margin-left: 2px;
            opacity: 0.6;
        }

        input[type="date"]:hover::-webkit-calendar-picker-indicator {
            opacity: 1;
        }

        /* 为弹窗内的日期控件添加相对定位容器 */
        .form-group {
            position: relative;
        }
    </style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-sm-12 animated fadeInRight">
            <div class="mail-box">
                <div class="mail-body">
                    <h3 class="text-center">报名信息编辑</h3>

                    <form:form enctype="multipart/form-data" id="inputForm" modelAttribute="registration"
                               action="${ctx}/qqc/registration/save" method="post" class="form-horizontal">
                        <form:hidden path="id"/>
                        <form:hidden path="hdId"/>

                        <!-- 基本信息 -->
                        <div class="form-section">基本信息</div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span class="required-field">*</span>参赛项目：</label>
                                    <div class="col-sm-8">
                                        <form:input path="participantProject" htmlEscape="false" maxlength="200"
                                                    class="form-control required"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span class="required-field">*</span>参赛地区：</label>
                                    <div class="col-sm-8">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <select id="competitionCity" name="competitionCity" class="form-control required">
                                                    <option value="">--请选择市--</option>
                                                    <option value="330100" ${registration.competitionCity == '330100' ? 'selected' : ''}>杭州市</option>
                                                    <option value="330200" ${registration.competitionCity == '330200' ? 'selected' : ''}>宁波市</option>
                                                    <option value="330300" ${registration.competitionCity == '330300' ? 'selected' : ''}>温州市</option>
                                                    <option value="330400" ${registration.competitionCity == '330400' ? 'selected' : ''}>嘉兴市</option>
                                                    <option value="330500" ${registration.competitionCity == '330500' ? 'selected' : ''}>湖州市</option>
                                                    <option value="330600" ${registration.competitionCity == '330600' ? 'selected' : ''}>绍兴市</option>
                                                    <option value="330700" ${registration.competitionCity == '330700' ? 'selected' : ''}>金华市</option>
                                                    <option value="330800" ${registration.competitionCity == '330800' ? 'selected' : ''}>衢州市</option>
                                                    <option value="330900" ${registration.competitionCity == '330900' ? 'selected' : ''}>舟山市</option>
                                                    <option value="331000" ${registration.competitionCity == '331000' ? 'selected' : ''}>台州市</option>
                                                    <option value="331100" ${registration.competitionCity == '331100' ? 'selected' : ''}>丽水市</option>
                                                </select>
                                                <input type="hidden" name="competitionCityName" id="competitionCityName" value="${registration.competitionCityName}"/>
                                            </div>
                                            <div class="col-sm-6">
                                                <select id="competitionDistrict" name="competitionDistrict" class="form-control">
                                                    <option value="">--请选择区县--</option>
                                                </select>
                                                <input type="hidden" name="competitionDistrictName" id="competitionDistrictName" value="${registration.competitionDistrictName}"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group" id="ruralSubgroupDiv" style="display: none;">
                                    <label class="col-sm-4 control-label">农村分组子项：</label>
                                    <div class="col-sm-8">
                                        <form:select path="ruralCompetitionSubgroup" class="form-control">
                                            <form:option value="" label="--请选择--"/>
                                            <form:options items="${fns:getDictList('rural_subgroup')}"
                                                          itemLabel="label" itemValue="value" htmlEscape="false"/>
                                        </form:select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span class="required-field">*</span>报名途径：</label>
                                    <div class="col-sm-8">
                                        <form:select path="registrationChannel" class="form-control required">
                                            <form:option value="" label="--请选择--"/>
                                            <form:options items="${fns:getDictList('registration_channel')}"
                                                          itemLabel="label" itemValue="value" htmlEscape="false"/>
                                        </form:select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span class="required-field">*</span>赛事分组：</label>
                                    <div class="col-sm-8">
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <form:select path="competitionGroup" class="form-control required">
                                                    <form:option value="" label="--请选择--"/>
                                                    <form:options items="${fns:getDictList('competition_group')}"
                                                                  itemLabel="label" itemValue="value" htmlEscape="false"/>
                                                </form:select>
                                            </div>
                                            <div class="col-sm-6">
                                                <form:select path="competitionSubgroup" class="form-control">
                                                    <form:option value="" label="--请选择--"/>
                                                    <form:options items="${fns:getDictList('competition_subgroup')}"
                                                                  itemLabel="label" itemValue="value" htmlEscape="false"/>
                                                </form:select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">项目领域：</label>
                                    <div class="col-sm-8">
                                        <form:select path="projectField" class="form-control">
                                            <form:option value="" label="--请选择--"/>
                                            <form:options items="${fns:getDictList('project_field')}"
                                                          itemLabel="label" itemValue="value" htmlEscape="false"/>
                                        </form:select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 第一申报人信息 -->
                        <div class="form-section">第一申报人信息</div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span class="required-field">*</span>姓名：</label>
                                    <div class="col-sm-8">
                                        <form:input path="firstApplicantName" htmlEscape="false" maxlength="50"
                                                    class="form-control required"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span class="required-field">*</span>邮箱：</label>
                                    <div class="col-sm-8">
                                        <form:input path="firstApplicantEmail" htmlEscape="false" maxlength="100"
                                                    class="form-control required email"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span class="required-field">*</span>身份证：</label>
                                    <div class="col-sm-8">
                                        <form:input path="firstApplicantIdCard" htmlEscape="false" maxlength="18"
                                                    class="form-control required"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span class="required-field">*</span>出生日期：</label>
                                    <div class="col-sm-8">
                                        <input id="firstApplicantBirthday" name="firstApplicantBirthday" type="date"
                                               maxlength="20" class="form-control required"
                                               value="<fmt:formatDate value="${registration.firstApplicantBirthday}" pattern="yyyy-MM-dd"/>"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span class="required-field">*</span>手机号：</label>
                                    <div class="col-sm-8">
                                        <form:input path="firstApplicantMobile" htmlEscape="false" maxlength="11"
                                                    class="form-control required mobile"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">户籍：</label>
                                    <div class="col-sm-8">
                                        <form:input path="firstApplicantHukou" htmlEscape="false" maxlength="100"
                                                    class="form-control"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span class="required-field">*</span>性别：</label>
                                    <div class="col-sm-8">
                                        <form:select path="firstApplicantGender" class="form-control required">
                                            <form:option value="" label="--请选择--"/>
                                            <form:option value="1" label="男"/>
                                            <form:option value="2" label="女"/>
                                        </form:select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">所任职位：</label>
                                    <div class="col-sm-8">
                                        <form:input path="firstApplicantPosition" htmlEscape="false" maxlength="100"
                                                    class="form-control"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">毕业时间：</label>
                                    <div class="col-sm-8">
                                        <input id="firstApplicantGraduationTime" name="firstApplicantGraduationTime" type="date"
                                               maxlength="20" class="form-control"
                                               value="<fmt:formatDate value="${registration.firstApplicantGraduationTime}" pattern="yyyy-MM-dd"/>"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 身份证照片上传 -->
                        <div class="form-group">
                            <label class="col-sm-2 control-label">身份证照片：</label>
                            <div class="col-sm-10">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <label>正面：</label>
                                        <input type="file" id="idCardFrontFile" class="form-control" accept="image/*" />
                                        <form:hidden path="firstApplicantIdCardFrontFile"/>
                                        <div id="idCardFrontPreview" class="file-preview ${empty registration.firstApplicantIdCardFrontFile ? 'hide' : ''}">
                                            <img id="idCardFrontImage" src="${registration.firstApplicantIdCardFrontFile}" />
                                            <button type="button" class="btn btn-xs btn-danger remove-file" onclick="removeFile('idCardFront')">
                                                <i class="fa fa-times"></i> 移除
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label>反面：</label>
                                        <input type="file" id="idCardBackFile" class="form-control" accept="image/*" />
                                        <form:hidden path="firstApplicantIdCardBackFile"/>
                                        <div id="idCardBackPreview" class="file-preview ${empty registration.firstApplicantIdCardBackFile ? 'hide' : ''}">
                                            <img id="idCardBackImage" src="${registration.firstApplicantIdCardBackFile}" />
                                            <button type="button" class="btn btn-xs btn-danger remove-file" onclick="removeFile('idCardBack')">
                                                <i class="fa fa-times"></i> 移除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 团队成员信息 -->
                        <div class="form-section">团队成员信息</div>
                        <div class="form-group">
                            <div class="col-sm-12">
                                <button type="button" id="addMember" class="add-btn">
                                    <i class="fa fa-plus"></i> 添加团队成员
                                </button>
                                <span class="text-muted" style="margin-left: 10px;">（最多可添加4个团队成员）</span>
                            </div>
                        </div>
                        <div id="membersContainer">
                            <!-- 团队成员将通过JavaScript动态添加 -->
                        </div>

                        <!-- 指导老师信息 -->
                        <div class="form-section">指导老师信息</div>
                        <div class="form-group">
                            <div class="col-sm-12">
                                <button type="button" id="addMentor" class="add-btn">
                                    <i class="fa fa-plus"></i> 添加指导老师
                                </button>
                                <span class="text-muted" style="margin-left: 10px;">（最多可添加2个指导老师）</span>
                            </div>
                        </div>
                        <div id="mentorsContainer">
                            <!-- 指导老师将通过JavaScript动态添加 -->
                        </div>

                        <!-- 公司信息 -->
                        <div id="companyInfoSection">
                            <div class="form-section">公司信息</div>
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">公司名称：</label>
                                        <div class="col-sm-8">
                                            <form:input path="companyName" htmlEscape="false" maxlength="200"
                                                        class="form-control"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">公司地址：</label>
                                        <div class="col-sm-8">
                                            <form:input path="companyAddress" htmlEscape="false" maxlength="300"
                                                        class="form-control"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">成立时间：</label>
                                        <div class="col-sm-8">
                                            <input id="companyEstablishTime" name="companyEstablishTime" type="date"
                                                   maxlength="20" class="form-control"
                                                   value="<fmt:formatDate value="${registration.companyEstablishTime}" pattern="yyyy-MM-dd"/>"/>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label">营业执照：</label>
                                <div class="col-sm-10">
                                    <input type="file" id="businessLicenseFile" class="form-control" accept="image/*" />
                                    <form:hidden path="companyBusinessLicenseFile"/>
                                    <div id="businessLicensePreview" class="file-preview ${empty registration.companyBusinessLicenseFile ? 'hide' : ''}">
                                        <img id="businessLicenseImage" src="${registration.companyBusinessLicenseFile}" />
                                        <button type="button" class="btn btn-xs btn-danger remove-file" onclick="removeFile('businessLicense')">
                                            <i class="fa fa-times"></i> 移除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 项目信息 -->
                        <div class="form-section">项目信息</div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">项目简介：</label>
                            <div class="col-sm-10">
                                <form:textarea path="projectBrief" htmlEscape="false" rows="4" maxlength="500"
                                               class="form-control"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">同行业竞争优势：</label>
                            <div class="col-sm-10">
                                <form:textarea path="industryCompetitiveAdvantage" htmlEscape="false" rows="4" maxlength="1000"
                                               class="form-control"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">社会效益：</label>
                            <div class="col-sm-10">
                                <form:textarea path="socialBenefits" htmlEscape="false" rows="4" maxlength="1000"
                                               class="form-control"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">团队素质：</label>
                            <div class="col-sm-10">
                                <form:textarea path="teamQuality" htmlEscape="false" rows="4" maxlength="1000"
                                               class="form-control"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">财务运营：</label>
                            <div class="col-sm-10">
                                <form:textarea path="financialOperation" htmlEscape="false" rows="4" maxlength="1000"
                                               class="form-control"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">市场前景：</label>
                            <div class="col-sm-10">
                                <form:textarea path="marketProspect" htmlEscape="false" rows="4" maxlength="1000"
                                               class="form-control"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">产品服务：</label>
                            <div class="col-sm-10">
                                <form:textarea path="productService" htmlEscape="false" rows="4" maxlength="1000"
                                               class="form-control"/>
                            </div>
                        </div>

                        <!-- 项目计划书上传 -->
                        <div class="form-group">
                            <label class="col-sm-2 control-label">项目计划书：</label>
                            <div class="col-sm-10">
                                <input type="file" id="projectPlanFile" class="form-control" accept=".pdf" />
                                <form:hidden path="projectPlanFile"/>
                                <div id="projectPlanPreview" class="file-preview ${empty registration.projectPlanFile ? 'hide' : ''}">
                                    <p><i class="fa fa-file-pdf-o"></i> 项目计划书.pdf</p>
                                    <a href="${registration.projectPlanFile}" target="_blank" class="btn btn-xs btn-info">
                                        <i class="fa fa-eye"></i> 查看
                                    </a>
                                    <button type="button" class="btn btn-xs btn-danger remove-file" onclick="removeFile('projectPlan')">
                                        <i class="fa fa-times"></i> 移除
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 声明信息 -->
                        <div class="form-section">声明信息</div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">是否同意公开展示：</label>
                            <div class="col-sm-10">
                                <form:select path="isAgreePublic" class="form-control">
                                    <form:option value="" label="--请选择--"/>
                                    <form:option value="1" label="是"/>
                                    <form:option value="0" label="否"/>
                                </form:select>
                            </div>
                        </div>

                        <!-- 表单按钮 -->
                        <div class="form-actions">
                            <div class="row">
                                <div class="col-md-12">
                                    <button class="btn btn-primary" type="button" onclick="doSubmit()">
                                        <i class="fa fa-save"></i> 保存
                                    </button>
                                    <button class="btn btn-default" type="button" onclick="history.back()">
                                        <i class="fa fa-arrow-left"></i> 返回
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form:form>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    var memberIndex = 0;
    var mentorIndex = 0;

    $(document).ready(function() {
        console.log('页面加载完成，开始初始化');

        //控制公司信息显示的函数
        function toggleCompanyInfo() {
            var subgroupValue = $('#competitionSubgroup').val();
            if (subgroupValue === 'startup' || subgroupValue === 'growth') {
                $('#companyInfoSection').show();
            } else {
                $('#companyInfoSection').hide();
            }
        }

        //为子分组下拉框绑定change事件
        $('#competitionSubgroup').on('change', toggleCompanyInfo);

        // 监听赛事分组变化，控制农村分组子项显示
        $('#competitionGroup').on('change', function() {
            var selectedValue = $(this).val();
            var selectedText = $(this).find('option:selected').text();

            console.log('赛事分组改变:', selectedValue, selectedText);

            if (selectedValue === '3') {
                $('#ruralSubgroupDiv').show();
            } else {
                $('#ruralSubgroupDiv').hide();
                $('[name="ruralCompetitionSubgroup"]').val('');
            }
        });

        // 页面加载时检查初始状态
        setTimeout(function() {
            var currentGroup = '${registration.competitionGroup}';
            if (currentGroup) {
                var currentGroupText = $('#competitionGroup option:selected').text();
                if (currentGroupText.indexOf('乡村') !== -1 || currentGroupText.indexOf('农村') !== -1) {
                    $('#ruralSubgroupDiv').show();
                }
            }
            toggleCompanyInfo();
        }, 500);

        // 绑定添加按钮事件
        $(document).off('click', '#addMember').on('click', '#addMember', function(e) {
            e.preventDefault();
            console.log('点击添加团队成员按钮');
            addMember();
        });

        $(document).off('click', '#addMentor').on('click', '#addMentor', function(e) {
            e.preventDefault();
            console.log('点击添加指导老师按钮');
            addMentor();
        });

        // 初始化现有数据
        initExistingData();

        // 绑定其他事件
        bindFileUpload();

        // 绑定动态添加的成员文件上传事件
        $('#membersContainer').on('change', '.member-id-card-front-file', function() {
            var index = $(this).data('index');
            uploadFile(this, 'member_idCardFront_' + index, 'member_idCardFrontPreview_' + index, 'member_idCardFrontImage_' + index, 'TEAM_MEMBER_IDCARD_FRONT');
        });

        $('#membersContainer').on('change', '.member-id-card-back-file', function() {
            var index = $(this).data('index');
            uploadFile(this, 'member_idCardBack_' + index, 'member_idCardBackPreview_' + index, 'member_idCardBackImage_' + index, 'TEAM_MEMBER_IDCARD_BACK');
        });


        // 延迟初始化城市级联，确保DOM完全渲染
        setTimeout(function() {
            initCityDistrictCascade();
        }, 200);

        console.log('页面初始化完成');
    });

    // HTML转义函数，防止特殊字符破坏HTML结构
    function htmlEscape(str) {
        if (!str) return '';
        return String(str)
            .replace(/&/g, '&amp;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;');
    }

    // 初始化现有数据
    function initExistingData() {
        console.log('开始初始化现有数据');

        // 团队成员数据
        try {
            var membersDataRaw = '${registration.projectMembers}';
            console.log('团队成员原始数据:', membersDataRaw);

            if (membersDataRaw && membersDataRaw !== 'null' && membersDataRaw !== '' && membersDataRaw !== '[]') {
                membersDataRaw = membersDataRaw.replace(/&quot;/g, '"');
                console.log('处理编码后的团队成员数据:', membersDataRaw);

                var members = JSON.parse(membersDataRaw);
                console.log('解析后的团队成员数据:', members);

                if (Array.isArray(members) && members.length > 0) {
                    members.forEach(function(member, index) {
                        console.log('添加团队成员', index + 1, ':', member);
                        addMemberWithData(member, index);
                    });
                    memberIndex = members.length;
                    console.log('团队成员索引设置为:', memberIndex);
                }
            }
        } catch (e) {
            console.error('解析团队成员数据失败:', e);
        }

        // 指导老师数据
        try {
            var mentorsDataRaw = '${registration.mentors}';
            console.log('指导老师原始数据:', mentorsDataRaw);

            if (mentorsDataRaw && mentorsDataRaw !== 'null' && mentorsDataRaw !== '' && mentorsDataRaw !== '[]') {
                mentorsDataRaw = mentorsDataRaw.replace(/&quot;/g, '"');
                console.log('处理编码后的指导老师数据:', mentorsDataRaw);

                var mentors = JSON.parse(mentorsDataRaw);
                console.log('解析后的指导老师数据:', mentors);

                if (Array.isArray(mentors) && mentors.length > 0) {
                    mentors.forEach(function(mentor, index) {
                        console.log('添加指导老师', index + 1, ':', mentor);
                        addMentorWithData(mentor, index);
                    });
                    mentorIndex = mentors.length;
                    console.log('指导老师索引设置为:', mentorIndex);
                }
            }
        } catch (e) {
            console.error('解析指导老师数据失败:', e);
        }

        // 检查按钮状态
        setTimeout(function() {
            // 检查团队成员数量，如果达到上限则隐藏添加按钮
            if (memberIndex >= 4) {
                $('#addMember').hide();
            }

            // 检查指导老师数量，如果达到上限则隐藏添加按钮
            if (mentorIndex >= 2) {
                $('#addMentor').hide();
            }
        }, 100);

        console.log('现有数据初始化完成');
    }

    // 添加团队成员
    function addMember() {
        // 检查数量限制
        if (memberIndex >= 4) {
            top.layer.alert('项目成员最多只能添加4个', {icon: 2});
            return;
        }

        addMemberWithData({}, memberIndex);
        memberIndex++;

        // 检查是否达到上限，如果是则隐藏添加按钮
        if (memberIndex >= 4) {
            $('#addMember').hide();
        }
    }

    // 添加带数据的团队成员 - 修复版本
    function addMemberWithData(data, index) {
        console.log('添加团队成员，索引:', index, '数据:', data);

        // 提取数据并进行HTML转义处理
        var name = htmlEscape(data.name || '');
        var mobile = htmlEscape(data.mobile || data.contact || '');
        var email = htmlEscape(data.email || '');
        var idCard = htmlEscape(data.idCard || '');
        var birthday = htmlEscape(data.birthday || '');
        var gender = data.gender || '';
        var hukou = htmlEscape(data.hukou || '');
        var position = htmlEscape(data.position || '');
        var idCardFront = data.idCardFront || data.idCardFrontFile || '';
        var idCardBack = data.idCardBack || data.idCardBackFile || '';


        var html = '<div class="member-item panel" data-index="' + index + '">';
        html += '<div class="panel-heading">';
        html += '<h4 class="panel-title">团队成员 #' + (index + 1);
        html += '<button type="button" class="btn btn-xs btn-danger remove-member pull-right" onclick="removeMember(' + index + ')">';
        html += '<i class="fa fa-times"></i> 移除';
        html += '</button>';
        html += '</h4>';
        html += '</div>';
        html += '<div class="panel-body">';
        html += '<div class="row">';

        // 第一行
        html += '<div class="col-sm-3">';
        html += '<div class="form-group">';
        html += '<label class="control-label">姓名</label>';
        html += '<input type="text" name="member_name_' + index + '" class="form-control member-name" value="' + name + '">';
        html += '</div>';
        html += '</div>';

        html += '<div class="col-sm-3">';
        html += '<div class="form-group">';
        html += '<label class="control-label">手机号</label>';
        html += '<input type="text" name="member_mobile_' + index + '" class="form-control member-mobile" value="' + mobile + '">';
        html += '</div>';
        html += '</div>';

        html += '<div class="col-sm-3">';
        html += '<div class="form-group">';
        html += '<label class="control-label">邮箱</label>';
        html += '<input type="email" name="member_email_' + index + '" class="form-control member-email" value="' + email + '">';
        html += '</div>';
        html += '</div>';

        html += '<div class="col-sm-3">';
        html += '<div class="form-group">';
        html += '<label class="control-label">性别</label>';
        html += '<select name="member_gender_' + index + '" class="form-control member-gender">';
        html += '<option value="">--请选择--</option>';
        html += '<option value="1"' + (gender == '1' ? ' selected' : '') + '>男</option>';
        html += '<option value="2"' + (gender == '2' ? ' selected' : '') + '>女</option>';
        html += '</select>';
        html += '</div>';
        html += '</div>';

        // 第二行
        html += '<div class="col-sm-4">';
        html += '<div class="form-group">';
        html += '<label class="control-label">身份证</label>';
        html += '<input type="text" name="member_idCard_' + index + '" class="form-control member-idCard" value="' + idCard + '">';
        html += '</div>';
        html += '</div>';

        html += '<div class="col-sm-4">';
        html += '<div class="form-group">';
        html += '<label class="control-label">出生日期</label>';
        html += '<input type="date" name="member_birthday_' + index + '" class="form-control member-birthday" value="' + birthday + '">';
        html += '</div>';
        html += '</div>';

        html += '<div class="col-sm-4">';
        html += '<div class="form-group">';
        html += '<label class="control-label">户籍</label>';
        html += '<input type="text" name="member_hukou_' + index + '" class="form-control member-hukou" value="' + hukou + '">';
        html += '</div>';
        html += '</div>';

        // 第三行
        html += '<div class="col-sm-12">';
        html += '<div class="form-group">';
        html += '<label class="control-label">所任职位</label>';
        html += '<input type="text" name="member_position_' + index + '" class="form-control member-position" value="' + position + '">';
        html += '</div>';
        html += '</div>';

        // 身份证照片上传
        html += '<div class="col-sm-12">';
        html += '<div class="form-group">';
        // html += '<label class="control-label">身份证照片</label>';
        html += '<div class="row">';
        html += '    <div class="col-sm-6">';
        html += '        <label>身份证正面：</label>';
        html += '        <input type="file" id="member_idCardFrontFile_' + index + '" class="form-control member-id-card-front-file" accept="image/*" data-index="' + index + '"/>';
        html += '        <input type="hidden" name="member_idCardFront_' + index + '" value="' + idCardFront + '"/>';
        html += '        <div id="member_idCardFrontPreview_' + index + '" class="file-preview ' + (idCardFront ? '' : 'hide') + '">';
        html += '            <img id="member_idCardFrontImage_' + index + '" src="' + idCardFront + '" />';
        html += '            <button type="button" class="btn btn-xs btn-danger remove-file" onclick="removeMemberFile(' + index + ', \'idCardFront\')">';
        html += '                <i class="fa fa-times"></i> 移除';
        html += '            </button>';
        html += '        </div>';
        html += '    </div>';
        html += '    <div class="col-sm-6">';
        html += '        <label>身份证反面：</label>';
        html += '        <input type="file" id="member_idCardBackFile_' + index + '" class="form-control member-id-card-back-file" accept="image/*" data-index="' + index + '"/>';
        html += '        <input type="hidden" name="member_idCardBack_' + index + '" value="' + idCardBack + '"/>';
        html += '        <div id="member_idCardBackPreview_' + index + '" class="file-preview ' + (idCardBack ? '' : 'hide') + '">';
        html += '            <img id="member_idCardBackImage_' + index + '" src="' + idCardBack + '" />';
        html += '            <button type="button" class="btn btn-xs btn-danger remove-file" onclick="removeMemberFile(' + index + ', \'idCardBack\')">';
        html += '                <i class="fa fa-times"></i> 移除';
        html += '            </button>';
        html += '        </div>';
        html += '    </div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';

        html += '</div>'; // row
        html += '</div>'; // panel-body
        html += '</div>'; // member-item

        $('#membersContainer').append(html);

        console.log('团队成员添加完成，索引:', index);
    }

    // 添加指导老师
    function addMentor() {
        // 检查数量限制
        if (mentorIndex >= 2) {
            top.layer.alert('指导老师最多只能添加2个', {icon: 2});
            return;
        }

        addMentorWithData({}, mentorIndex);
        mentorIndex++;

        // 检查是否达到上限，如果是则隐藏添加按钮
        if (mentorIndex >= 2) {
            $('#addMentor').hide();
        }
    }

    // 添加带数据的指导老师 - 修复版本
    function addMentorWithData(data, index) {
        console.log('添加指导老师，索引:', index, '数据:', data);

        // 提取数据并进行HTML转义处理
        var name = htmlEscape(data.name || '');
        var mobile = htmlEscape(data.mobile || '');
        var email = htmlEscape(data.email || '');
        var workplace = htmlEscape(data.workplace || '');
        var position = htmlEscape(data.position || '');

        var html = '<div class="mentor-item panel" data-index="' + index + '">';
        html += '<div class="panel-heading">';
        html += '<h4 class="panel-title">指导老师 #' + (index + 1);
        html += '<button type="button" class="btn btn-xs btn-danger remove-mentor pull-right" onclick="removeMentor(' + index + ')">';
        html += '<i class="fa fa-times"></i> 移除';
        html += '</button>';
        html += '</h4>';
        html += '</div>';
        html += '<div class="panel-body">';
        html += '<div class="row">';

        html += '<div class="col-sm-4">';
        html += '<div class="form-group">';
        html += '<label class="control-label">姓名</label>';
        html += '<input type="text" name="mentor_name_' + index + '" class="form-control mentor-name" value="' + name + '">';
        html += '</div>';
        html += '</div>';

        html += '<div class="col-sm-4">';
        html += '<div class="form-group">';
        html += '<label class="control-label">手机号</label>';
        html += '<input type="text" name="mentor_mobile_' + index + '" class="form-control mentor-mobile" value="' + mobile + '">';
        html += '</div>';
        html += '</div>';

        html += '<div class="col-sm-4">';
        html += '<div class="form-group">';
        html += '<label class="control-label">邮箱</label>';
        html += '<input type="email" name="mentor_email_' + index + '" class="form-control mentor-email" value="' + email + '">';
        html += '</div>';
        html += '</div>';

        html += '<div class="col-sm-6">';
        html += '<div class="form-group">';
        html += '<label class="control-label">工作单位</label>';
        html += '<input type="text" name="mentor_workplace_' + index + '" class="form-control mentor-workplace" value="' + workplace + '">';
        html += '</div>';
        html += '</div>';

        html += '<div class="col-sm-6">';
        html += '<div class="form-group">';
        html += '<label class="control-label">所任职位</label>';
        html += '<input type="text" name="mentor_position_' + index + '" class="form-control mentor-position" value="' + position + '">';
        html += '</div>';
        html += '</div>';

        html += '</div>'; // row
        html += '</div>'; // panel-body
        html += '</div>'; // mentor-item

        $('#mentorsContainer').append(html);

        console.log('指导老师添加完成，索引:', index);
    }

    // 城市区县级联初始化
    function initCityDistrictCascade() {
        console.log('开始初始化城市区县级联');

        // 预定义的区县数据
        var districtData = {
            '330100': [ // 杭州市
                {code: '330102', name: '上城区'},
                {code: '330105', name: '拱墅区'},
                {code: '330106', name: '西湖区'},
                {code: '330108', name: '滨江区'},
                {code: '330109', name: '萧山区'},
                {code: '330110', name: '余杭区'},
                {code: '330111', name: '富阳区'},
                {code: '330112', name: '临安区'},
                {code: '330113', name: '临平区'},
                {code: '330114', name: '钱塘区'},
                {code: '330122', name: '桐庐县'},
                {code: '330127', name: '淳安县'},
                {code: '330182', name: '建德市'}
            ],
            '330200': [ // 宁波市
                {code: '330203', name: '海曙区'},
                {code: '330205', name: '江北区'},
                {code: '330206', name: '北仑区'},
                {code: '330211', name: '镇海区'},
                {code: '330212', name: '鄞州区'},
                {code: '330213', name: '奉化区'},
                {code: '330225', name: '象山县'},
                {code: '330226', name: '宁海县'},
                {code: '330281', name: '余姚市'},
                {code: '330282', name: '慈溪市'}
            ],
            '330300': [ // 温州市
                {code: '330302', name: '鹿城区'},
                {code: '330303', name: '龙湾区'},
                {code: '330304', name: '瓯海区'},
                {code: '330305', name: '洞头区'},
                {code: '330324', name: '永嘉县'},
                {code: '330326', name: '平阳县'},
                {code: '330327', name: '苍南县'},
                {code: '330328', name: '文成县'},
                {code: '330329', name: '泰顺县'},
                {code: '330381', name: '瑞安市'},
                {code: '330382', name: '乐清市'}
            ],
            '330400': [ // 嘉兴市
                {code: '330402', name: '南湖区'},
                {code: '330411', name: '秀洲区'},
                {code: '330421', name: '嘉善县'},
                {code: '330424', name: '海盐县'},
                {code: '330481', name: '海宁市'},
                {code: '330482', name: '平湖市'},
                {code: '330483', name: '桐乡市'}
            ],
            '330500': [ // 湖州市
                {code: '330502', name: '吴兴区'},
                {code: '330503', name: '南浔区'},
                {code: '330521', name: '德清县'},
                {code: '330522', name: '长兴县'},
                {code: '330523', name: '安吉县'}
            ],
            '330600': [ // 绍兴市
                {code: '330602', name: '越城区'},
                {code: '330603', name: '柯桥区'},
                {code: '330604', name: '上虞区'},
                {code: '330624', name: '新昌县'},
                {code: '330681', name: '诸暨市'},
                {code: '330683', name: '嵊州市'}
            ],
            '330700': [ // 金华市
                {code: '330702', name: '婺城区'},
                {code: '330703', name: '金东区'},
                {code: '330723', name: '武义县'},
                {code: '330726', name: '浦江县'},
                {code: '330727', name: '磐安县'},
                {code: '330781', name: '兰溪市'},
                {code: '330782', name: '义乌市'},
                {code: '330783', name: '东阳市'},
                {code: '330784', name: '永康市'}
            ],
            '330800': [ // 衢州市
                {code: '330802', name: '柯城区'},
                {code: '330803', name: '衢江区'},
                {code: '330822', name: '常山县'},
                {code: '330824', name: '开化县'},
                {code: '330825', name: '龙游县'},
                {code: '330881', name: '江山市'}
            ],
            '330900': [ // 舟山市
                {code: '330902', name: '定海区'},
                {code: '330903', name: '普陀区'},
                {code: '330921', name: '岱山县'},
                {code: '330922', name: '嵊泗县'}
            ],
            '331000': [ // 台州市
                {code: '331002', name: '椒江区'},
                {code: '331003', name: '黄岩区'},
                {code: '331004', name: '路桥区'},
                {code: '331022', name: '三门县'},
                {code: '331023', name: '天台县'},
                {code: '331024', name: '仙居县'},
                {code: '331081', name: '温岭市'},
                {code: '331082', name: '临海市'},
                {code: '331083', name: '玉环市'}
            ],
            '331100': [ // 丽水市
                {code: '331102', name: '莲都区'},
                {code: '331121', name: '青田县'},
                {code: '331122', name: '缙云县'},
                {code: '331123', name: '遂昌县'},
                {code: '331124', name: '松阳县'},
                {code: '331125', name: '云和县'},
                {code: '331126', name: '庆元县'},
                {code: '331127', name: '景宁畲族自治县'},
                {code: '331181', name: '龙泉市'}
            ]
        };

        // 城市改变时，重新加载区县数据
        $('#competitionCity').off('change').on('change', function() {
            var cityCode = $(this).val();
            var cityName = $(this).find('option:selected').text();
            var districtSelect = $('#competitionDistrict');

            console.log('城市改变，选中的城市代码：', cityCode, '城市名称：', cityName);

            // 更新隐藏字段
            $('#competitionCityName').val(cityCode ? cityName : '');
            $('#competitionDistrictName').val(''); // 清空区县名称

            // 清空区县选项
            districtSelect.empty().append('<option value="">--请选择区县--</option>');

            if (cityCode && districtData[cityCode]) {
                console.log('找到对应区县数据，数量：', districtData[cityCode].length);
                $.each(districtData[cityCode], function(index, district) {
                    districtSelect.append('<option value="' + district.code + '">' + district.name + '</option>');
                });
                console.log('区县选项添加完成');
            } else {
                console.log('未找到对应区县数据，城市代码：', cityCode);
            }
        });

        // 区县改变时，更新隐藏字段
        $('#competitionDistrict').off('change').on('change', function() {
            var districtName = $(this).find('option:selected').text();
            $('#competitionDistrictName').val($(this).val() ? districtName : '');
            console.log('区县改变，选中的区县：', $(this).val(), districtName);
        });

        // 如果有现有数据，需要初始化选中状态
        setTimeout(function() {
            var currentCity = '${registration.competitionCity}';
            var currentDistrict = '${registration.competitionDistrict}';

            console.log('初始化现有数据，城市：', currentCity, '区县：', currentDistrict);

            if (currentCity) {
                $('#competitionCity').val(currentCity);
                $('#competitionCity').trigger('change'); // 触发级联加载
                if (currentDistrict) {
                    setTimeout(function() {
                        $('#competitionDistrict').val(currentDistrict);
                        $('#competitionDistrict').trigger('change'); // 更新隐藏字段
                    }, 300);
                }
            }
        }, 200);
    }

    // 移除团队成员
    function removeMember(index) {
        console.log('移除团队成员，索引:', index);
        $(".member-item[data-index='" + index + "']").remove();

        // 重新计算当前成员数量
        var currentCount = $('.member-item').length;
        memberIndex = currentCount; // 更新成员计数
        if (currentCount < 4) {
            $('#addMember').show(); // 如果少于4个，显示添加按钮
        }
    }

    // 移除指导老师
    function removeMentor(index) {
        console.log('移除指导老师，索引:', index);
        $(".mentor-item[data-index='" + index + "']").remove();

        // 重新计算当前老师数量
        var currentCount = $('.mentor-item').length;
        mentorIndex = currentCount; // 更新指导老师计数
        if (currentCount < 2) {
            $('#addMentor').show(); // 如果少于2个，显示添加按钮
        }
    }

    // 移除团队成员上传的文件
    function removeMemberFile(index, type) {
        if (type === 'idCardFront') {
            $('input[name="member_idCardFront_' + index + '"]').val('');
            $('#member_idCardFrontFile_' + index).val('');
            $('#member_idCardFrontPreview_' + index).addClass('hide');
        } else if (type === 'idCardBack') {
            $('input[name="member_idCardBack_' + index + '"]').val('');
            $('#member_idCardBackFile_' + index).val('');
            $('#member_idCardBackPreview_' + index).addClass('hide');
        }
    }


    // 绑定文件上传事件
    function bindFileUpload() {
        console.log('绑定文件上传事件');

        // 身份证正面
        $('#idCardFrontFile').on('change', function() {
            uploadFile(this, 'firstApplicantIdCardFrontFile', 'idCardFrontPreview', 'idCardFrontImage', 'APPLICANT_IDCARD_FRONT');
        });

        // 身份证反面
        $('#idCardBackFile').on('change', function() {
            uploadFile(this, 'firstApplicantIdCardBackFile', 'idCardBackPreview', 'idCardBackImage', 'APPLICANT_IDCARD_BACK');
        });

        // 营业执照
        $('#businessLicenseFile').on('change', function() {
            uploadFile(this, 'companyBusinessLicenseFile', 'businessLicensePreview', 'businessLicenseImage', 'BUSINESS_LICENSE');
        });

        // 项目计划书
        $('#projectPlanFile').on('change', function() {
            uploadFile(this, 'projectPlanFile', 'projectPlanPreview', null, 'PROJECT_PLAN');
        });
    }

    // 文件上传函数
    function uploadFile(fileInput, hiddenInputId, previewDivId, previewImgId, fileType) {
        var file = fileInput.files[0];
        if (!file) { return; }

        // 文件类型验证
        if (fileType === 'PROJECT_PLAN' && file.type !== 'application/pdf') {
            top.layer.alert('项目计划书只能上传PDF文件', {icon: 2});
            return;
        } else if (fileType !== 'PROJECT_PLAN' && !file.type.match('image.*')) {
            top.layer.alert('请选择图片文件', {icon: 2});
            return;
        }

        // 文件大小验证
        if (file.size > 10 * 1024 * 1024) { // 10MB 限制
            top.layer.alert('文件大小不能超过10MB', {icon: 2});
            return;
        }

        var loadIndex = top.layer.load(1, {shade: [0.3, '#000']});
        var formData = new FormData();
        formData.append('file', file);
        formData.append('fileType', fileType);

        $.ajax({
            url: '${ctx}/qqc/registration/uploadFile',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                top.layer.close(loadIndex);
                if (response.success) {
                    $("input[name='" + hiddenInputId + "']").val(response.body.ossUrl);
                    if (previewImgId) {
                        $("#" + previewImgId).attr('src', response.body.ossUrl);
                    }
                    $("#" + previewDivId).removeClass('hide');
                    top.layer.msg('上传成功', {icon: 1});
                } else {
                    top.layer.alert(response.msg || '上传失败', {icon: 2});
                }
            },
            error: function() {
                top.layer.close(loadIndex);
                top.layer.alert('上传请求失败', {icon: 2});
            }
        });
    }

    // 移除文件
    function removeFile(type) {
        switch(type) {
            case 'idCardFront':
                $("input[name='firstApplicantIdCardFrontFile']").val('');
                $('#idCardFrontFile').val('');
                $('#idCardFrontPreview').addClass('hide');
                break;
            case 'idCardBack':
                $("input[name='firstApplicantIdCardBackFile']").val('');
                $('#idCardBackFile').val('');
                $('#idCardBackPreview').addClass('hide');
                break;
            case 'businessLicense':
                $("input[name='companyBusinessLicenseFile']").val('');
                $('#businessLicenseFile').val('');
                $('#businessLicensePreview').addClass('hide');
                break;
            case 'projectPlan':
                $("input[name='projectPlanFile']").val('');
                $('#projectPlanFile').val('');
                $('#projectPlanPreview').addClass('hide');
                break;
        }
    }

    // 表单提交
    function doSubmit() {
        console.log('开始提交表单');

        // 验证必填字段
        var isValid = true;
        $('.required').each(function() {
            if (!$(this).val()) {
                isValid = false;
                $(this).addClass('error');
                top.layer.alert('请填写所有必填字段', {icon: 2});
                return false;
            } else {
                $(this).removeClass('error');
            }
        });

        if (!isValid) {
            return;
        }

        var loadIndex = top.layer.load(1, {shade: [0.3, '#000']});

        // 收集团队成员数据
        var members = [];
        $('.member-item').each(function() {
            var $this = $(this);
            var index = $this.data('index');
            var member = {
                name: $.trim($this.find('.member-name').val() || ''),
                mobile: $.trim($this.find('.member-mobile').val() || ''),
                email: $.trim($this.find('.member-email').val() || ''),
                idCard: $.trim($this.find('.member-idCard').val() || ''),
                birthday: $.trim($this.find('.member-birthday').val() || ''),
                gender: $.trim($this.find('.member-gender').val() || ''),
                hukou: $.trim($this.find('.member-hukou').val() || ''),
                position: $.trim($this.find('.member-position').val() || ''),
                idCardFront: $.trim($('input[name="member_idCardFront_' + index + '"]').val() || ''),
                idCardBack: $.trim($('input[name="member_idCardBack_' + index + '"]').val() || '')
            };

            // 只有当姓名不为空时才添加到数组中
            if (member.name) {
                members.push(member);
            }
        });

        // 收集指导老师数据
        var mentors = [];
        $('.mentor-item').each(function() {
            var $this = $(this);
            var mentor = {
                name: $.trim($this.find('.mentor-name').val() || ''),
                mobile: $.trim($this.find('.mentor-mobile').val() || ''),
                email: $.trim($this.find('.mentor-email').val() || ''),
                workplace: $.trim($this.find('.mentor-workplace').val() || ''),
                position: $.trim($this.find('.mentor-position').val() || '')
            };

            // 只有当姓名不为空时才添加到数组中
            if (mentor.name) {
                mentors.push(mentor);
            }
        });

        // 转换为JSON字符串
        var membersJson = JSON.stringify(members);
        var mentorsJson = JSON.stringify(mentors);

        console.log('团队成员JSON:', membersJson);
        console.log('指导老师JSON:', mentorsJson);

        // 验证JSON格式
        try {
            JSON.parse(membersJson);
            JSON.parse(mentorsJson);
            console.log('JSON格式验证通过');
        } catch (e) {
            console.error('JSON格式错误:', e);
            top.layer.close(loadIndex);
            top.layer.alert('数据格式错误，请检查输入内容', {icon: 2});
            return;
        }

        // 移除旧的隐藏字段（如果存在）
        $('input[name="projectMembersJson"]').remove();
        $('input[name="mentorsJson"]').remove();

        // 创建新的隐藏字段
        var membersInput = $('<input>').attr({
            type: 'hidden',
            name: 'projectMembersJson',
            value: membersJson
        });

        var mentorsInput = $('<input>').attr({
            type: 'hidden',
            name: 'mentorsJson',
            value: mentorsJson
        });

        // 添加到表单
        $('#inputForm').append(membersInput);
        $('#inputForm').append(mentorsInput);

        console.log('准备提交的表单数据:');
        console.log('- 团队成员数量:', members.length);
        console.log('- 指导老师数量:', mentors.length);

        // 提交表单
        $.ajax({
            url: '${ctx}/qqc/registration/save',
            type: 'POST',
            data: $('#inputForm').serialize(),
            dataType: 'json',
            success: function(data) {
                top.layer.close(loadIndex);
                if(data.success) {
                    top.layer.msg(data.msg || '保存成功!', {icon: 1, time: 1500}, function() {
                        // 关闭当前编辑弹窗
                        var index = parent.layer.getFrameIndex(window.name);
                        if (index) {
                            parent.layer.close(index);

                            // 刷新父页面内容
                            try {
                                // 方法1: 如果父页面有刷新函数，直接调用
                                if (typeof parent.refreshRegistrationList === 'function') {
                                    parent.refreshRegistrationList();
                                }
                                // 方法2: 如果父页面是报名列表弹窗，重新加载iframe内容
                                else if (parent.window.location.href.indexOf('registrationList') !== -1) {
                                    parent.location.reload();
                                }
                                // 方法3: 如果是在更深层的弹窗中，尝试刷新最外层的报名列表
                                else {
                                    // 查找包含报名列表的iframe并刷新
                                    try {
                                        var registrationListFrame = top.document.querySelector('iframe[src*="registrationList"]');
                                        if (registrationListFrame && registrationListFrame.contentWindow) {
                                            registrationListFrame.contentWindow.location.reload();
                                        } else {
                                            // 如果找不到iframe，尝试直接刷新父页面
                                            parent.location.reload();
                                        }
                                    } catch (e) {
                                        console.error('刷新报名列表失败:', e);
                                        parent.location.reload();
                                    }
                                }
                            } catch (e) {
                                console.error('刷新页面失败:', e);
                                parent.location.reload();
                            }
                        } else {
                            // 不在弹窗中，直接跳转到报名列表页面
                            var hdId = '${registration.hdId}';
                            window.location.href = '${ctx}/qqc/competition/registrationList?hdId=' + hdId;
                        }
                    });
                } else {
                    top.layer.alert(data.msg || "保存失败", {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                top.layer.close(loadIndex);
                console.error('提交失败:', xhr.responseText);
                top.layer.alert("系统错误，请联系管理员", {icon: 2});
            }
        });
    }
</script>
</body>
</html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <link href="${ctxStatic}/summernote/summernote.css" rel="stylesheet">
    <link href="${ctxStatic}/summernote/summernote-bs3.css" rel="stylesheet">
    <script src="${ctxStatic}/summernote/summernote.min.js"></script>
    <script src="${ctxStatic}/summernote/summernote-zh-CN.js"></script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-sm-12 animated fadeInRight">
            <div class="mail-box">
                <div class="mail-body">
                    <form:form enctype="multipart/form-data" id="inputForm" modelAttribute="competitionInfo"
                               action="${ctx}/qqc/competition/save" method="post" class="form-horizontal">
                        <form:hidden path="id"/>

                        <%-- 开始两列布局 --%>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>标题：</label>
                                    <div class="col-sm-8">
                                        <input type="text" autocomplete="off" value="${competitionInfo.title}" id="title"
                                               name="title" class="form-control required">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>类型：</label>
                                    <div class="col-sm-8">
                                        <select id="type" name="type" class="form-control required">
                                            <option value="">--请选择--</option>
                                            <option value="1" ${competitionInfo.type == 1 ? 'selected' : ''}>个人赛</option>
                                            <option value="2" ${competitionInfo.type == 2 ? 'selected' : ''}>团体赛</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">级别：</label>
                                    <div class="col-sm-8">
                                        <select id="level" name="level" class="form-control">
                                            <option value="">--请选择--</option>
                                            <option value="市级" ${competitionInfo.level == '市级' ? 'selected' : ''}>市级</option>
                                            <option value="省级" ${competitionInfo.level == '省级' ? 'selected' : ''}>省级</option>
                                            <option value="国级" ${competitionInfo.level == '国级' ? 'selected' : ''}>国级</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">比赛具体地点：</label>
                                    <div class="col-sm-8">
                                        <input type="text" autocomplete="off" value="${competitionInfo.competitionArea}"
                                               id="competitionArea" name="competitionArea" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">主办单位：</label>
                                    <div class="col-sm-8">
                                        <input type="text" autocomplete="off" value="${competitionInfo.hostUnit}" id="hostUnit"
                                               name="hostUnit" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">执行单位：</label>
                                    <div class="col-sm-8">
                                        <input type="text" autocomplete="off" value="${competitionInfo.executiveUnit}"
                                               id="executiveUnit" name="executiveUnit" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>报名开始时间：</label>
                                    <div class="col-sm-8">
                                        <input id="registrationStartTime" name="registrationStartTime" type="text"
                                               maxlength="20" class="laydate-icon form-control layer-date required"
                                               value="<fmt:formatDate value="${competitionInfo.registrationStartTime}" pattern="yyyy-MM-dd HH:mm:ss"/>" readonly/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>比赛开始时间：</label>
                                    <div class="col-sm-8">
                                        <input id="competitionStartTime" name="competitionStartTime" type="text" maxlength="20"
                                               class="laydate-icon form-control layer-date required"
                                               value="<fmt:formatDate value="${competitionInfo.competitionStartTime}" pattern="yyyy-MM-dd HH:mm:ss"/>" readonly/>
                                    </div>
                                </div>
<%--                                <div class="form-group">--%>
<%--                                    <label class="col-sm-4 control-label">缩略图：</label>--%>
<%--                                    <div class="col-sm-8">--%>
<%--                                        <form:hidden path="thumbnailImg" htmlEscape="false" maxlength="255" class="form-control"/>--%>
<%--                                        <sys:ckfinder input="thumbnailImg" type="images" uploadPath="/qqchuang/competition/thumbnail" selectMultiple="false"/>--%>
<%--                                    </div>--%>
<%--                                </div>--%>
                                <!-- 缩略图上传 -->
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">缩略图：</label>
                                    <div class="col-sm-8">
                                        <input type="file" id="thumbnailImgFile" class="form-control" accept=".jpg,.jpeg,.png" />
                                        <input type="hidden" id="thumbnailImg" name="thumbnailImg" value="${competitionInfo.thumbnailImg}" />
                                        <div id="thumbnailPreview" class="mt-2 ${empty competitionInfo.thumbnailImg ? 'hide' : ''}">
                                            <img id="thumbnailPreviewImage" src="${competitionInfo.thumbnailImg}" class="img-thumbnail" style="max-height: 100px;" />
                                            <button type="button" class="btn btn-xs btn-danger mt-1" id="removeThumbnail">
                                                <i class="fa fa-times"></i> 移除图片
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">简介：</label>
                                    <div class="col-sm-8">
                                        <textarea id="introduction" name="introduction" class="form-control" rows="3">${competitionInfo.introduction}</textarea>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>状态：</label>
                                    <div class="col-sm-8">
                                        <select id="status" name="status" class="form-control required">
                                            <option value="">--请选择--</option>
                                            <option value="0" ${competitionInfo.status == 0 ? 'selected' : ''}>未开始</option>
                                            <option value="1" ${competitionInfo.status == 1 ? 'selected' : ''}>报名中</option>
                                            <option value="2" ${competitionInfo.status == 2 ? 'selected' : ''}>报名结束</option>
                                            <option value="3" ${competitionInfo.status == 3 ? 'selected' : ''}>比赛中</option>
                                            <option value="4" ${competitionInfo.status == 4 ? 'selected' : ''}>已结束</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">比赛地区：</label>
                                    <div class="col-sm-8">
                                        <sys:treeselect id="competitionRegion" name="competitionRegion" value="${competitionInfo.competitionRegion}"
                                                        labelName="competitionRegionName" labelValue="${competitionInfo.competitionRegion}"
                                                        title="区域" url="/sys/area/treeData" cssClass="form-control required" allowClear="true"/>
                                        <input type="hidden" id="regionName" name="regionName" value="${competitionInfo.competitionRegion}"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">承办单位：</label>
                                    <div class="col-sm-8">
                                        <input type="text" autocomplete="off" value="${competitionInfo.organizerUnit}"
                                               id="organizerUnit" name="organizerUnit" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">支持单位：</label>
                                    <div class="col-sm-8">
                                        <input type="text" autocomplete="off" value="${competitionInfo.supportUnit}"
                                               id="supportUnit" name="supportUnit" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>报名结束时间：</label>
                                    <div class="col-sm-8">
                                        <input id="registrationEndTime" name="registrationEndTime" type="text" maxlength="20"
                                               class="laydate-icon form-control layer-date required"
                                               value="<fmt:formatDate value="${competitionInfo.registrationEndTime}" pattern="yyyy-MM-dd HH:mm:ss"/>" readonly/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>比赛结束时间：</label>
                                    <div class="col-sm-8">
                                        <input id="competitionEndTime" name="competitionEndTime" type="text" maxlength="20"
                                               class="laydate-icon form-control layer-date required"
                                               value="<fmt:formatDate value="${competitionInfo.competitionEndTime}" pattern="yyyy-MM-dd HH:mm:ss"/>" readonly/>
                                    </div>
                                </div>
<%--                                <div class="form-group">--%>
<%--                                    <label class="col-sm-4 control-label">轮播图：</label>--%>
<%--                                    <div class="col-sm-8">--%>
<%--                                        <form:hidden path="posterImg" htmlEscape="false" maxlength="255" class="form-control"/>--%>
<%--                                        <sys:ckfinder input="posterImg" type="images" uploadPath="/qqchuang/competition/poster" selectMultiple="false"/>--%>
<%--                                    </div>--%>
<%--                                </div>--%>
                                <!-- 轮播图上传 -->
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">轮播图：</label>
                                    <div class="col-sm-8">
                                        <input type="file" id="posterImgFile" class="form-control" accept=".jpg,.jpeg,.png" />
                                        <input type="hidden" id="posterImg" name="posterImg" value="${competitionInfo.posterImg}" />
                                        <div id="posterPreview" class="mt-2 ${empty competitionInfo.posterImg ? 'hide' : ''}">
                                            <img id="posterPreviewImage" src="${competitionInfo.posterImg}" class="img-thumbnail" style="max-height: 100px;" />
                                            <button type="button" class="btn btn-xs btn-danger mt-1" id="removePoster">
                                                <i class="fa fa-times"></i> 移除图片
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <%-- 详情部分（占满整行） --%>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">详情：</label>
                            <div class="col-sm-9"> <%-- 调整为col-sm-9或10，根据视觉效果决定 --%>
                                <form:textarea cssStyle="display: none" path="details" htmlEscape="false" rows="6"
                                               maxlength="2000" class="form-control"/>
                                <div id="summernote" class="summernote">${competitionInfo.details}</div>
                            </div>
                        </div>

                        <%-- 底部按钮 --%>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-4">
                                <button type="button" class="btn btn-primary" onclick="doSubmit()">保存</button>
                                <button type="button" class="btn btn-default" onclick="window.history.back()" style="margin-left: 10px;">返回</button>
                            </div>
                        </div>
                    </form:form>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var validateForm;

    $(document).ready(function () {
        //------------------------------------//
        //----- 1. 图片上传与处理部分 -----//
        //------------------------------------//

        // 缩略图上传处理
        $("#thumbnailImgFile").on('change', function() {
            uploadImage(this, 'thumbnailImg', 'thumbnailPreview', 'thumbnailPreviewImage', 'COMPETITION_THUMBNAIL');
        });

        // 轮播图上传处理
        $("#posterImgFile").on('change', function() {
            uploadImage(this, 'posterImg', 'posterPreview', 'posterPreviewImage', 'COMPETITION_POSTER');
        });

        // 移除缩略图
        $("#removeThumbnail").on('click', function() {
            $("#thumbnailImg").val('');
            $("#thumbnailImgFile").val(''); // 清空文件选择框
            $("#thumbnailPreview").addClass('hide');
        });

        // 移除轮播图
        $("#removePoster").on('click', function() {
            $("#posterImg").val('');
            $("#posterImgFile").val(''); // 清空文件选择框
            $("#posterPreview").addClass('hide');
        });

        // ✅ 正确的图片上传函数
        function uploadImage(fileInput, hiddenInputId, previewDivId, previewImgId, fileType) {
            var file = fileInput.files[0];
            if (!file) { return; }

            if (!file.type.match('image.*')) {
                top.layer.alert('请选择图片文件', {icon: 2});
                return;
            }
            if (file.size > 5 * 1024 * 1024) { // 5MB 限制
                top.layer.alert('图片大小不能超过5MB', {icon: 2});
                return;
            }

            var loadIndex = top.layer.load(1, {shade: [0.3, '#000']});
            var formData = new FormData();
            formData.append('file', file);
            formData.append('fileType', fileType);

            $.ajax({
                url: '${ctx}/qqc/competition/uploadFile', // URL 修正为文件上传接口
                type: 'POST',
                data: formData,                         // Data 修正为包含文件的 formData
                contentType: false,
                processData: false,
                success: function(response) {
                    top.layer.close(loadIndex);
                    if (response.success) {
                        $("#" + hiddenInputId).val(response.body.ossUrl);
                        $("#" + previewImgId).attr('src', response.body.ossUrl);
                        $("#" + previewDivId).removeClass('hide');
                        top.layer.msg('上传成功', {icon: 1});
                    } else {
                        top.layer.alert(response.msg || '上传失败', {icon: 2});
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    top.layer.close(loadIndex);
                    console.error("Upload failed:", jqXHR.status, jqXHR.responseText, errorThrown);
                    top.layer.alert('上传请求失败，请检查浏览器控制台获取详情。', {icon: 2});
                }
            });
        }

        //------------------------------------//
        //----- 2. 表单初始化与提交部分 -----//
        //------------------------------------//

        // Summernote 富文本编辑器初始化
        $("#summernote").summernote({
            height: 400,
            minHeight: 300,
            maxHeight: 500,
            lang: 'zh-CN',
        });

        // laydate 日期控件初始化
        laydate({ elem: '#registrationStartTime', event: 'click', istime: true, format: 'YYYY-MM-DD hh:mm:ss' });
        laydate({ elem: '#registrationEndTime', event: 'click', istime: true, format: 'YYYY-MM-DD hh:mm:ss' });
        laydate({ elem: '#competitionStartTime', event: 'click', istime: true, format: 'YYYY-MM-DD hh:mm:ss' });
        laydate({ elem: '#competitionEndTime', event: 'click', istime: true, format: 'YYYY-MM-DD hh:mm:ss' });

        // ✅ 正确的表单验证与最终提交
        validateForm = $("#inputForm").validate({
            submitHandler: function (form) {
                var regionName = $("#competitionRegionName").val();
                $("#regionName").val(regionName);

                // 获取富文本内容并处理 p 标签
                var str = $('#summernote').code();
                // 只有当内容不只是一个空段落时才处理
                if (str !== '<p><br></p>') {
                    // 移除内容首尾的 <p></p> 标签
                    str = str.replace(/^<p>(.*)<\/p>$/, '$1');
                }
                $("#details").val(str);

                loading('正在提交，请稍等...');

                $.ajax({
                    url: '${ctx}/qqc/competition/save', // URL 是保存接口
                    type: 'POST',
                    data: $(form).serialize(),        // Data 是序列化的表单文本
                    dataType: 'json',
                    success: function(data) {
                        top.layer.closeAll('loading');
                        if(data.success) {
                            try {
                                var activeTab = top.getActiveTab();
                                if (activeTab && typeof activeTab.attr === 'function') {
                                    var frameName = activeTab.attr("name");
                                    top.frames[frameName].location.reload();
                                } else {
                                    parent.location.reload();
                                }
                            } catch (e) {
                                parent.location.reload();
                            }
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                            top.layer.msg(data.msg || '保存成功!', {icon: 1, time: 1500});
                        } else {
                            top.layer.alert(data.msg || "保存失败", {icon: 2});
                        }
                    },
                    error: function() {
                        top.layer.closeAll('loading');
                        top.layer.alert("系统错误，请联系管理员", {icon: 2});
                    }
                });
            },
            errorContainer: "#messageBox",
            errorPlacement: function (error, element) {
                $("#messageBox").text("输入有误，请先更正。");
                if (element.is(":checkbox") || element.is(":radio") || element.parent().is(".input-append")) {
                    error.appendTo(element.parent().parent());
                } else {
                    error.insertAfter(element);
                }
            }
        });
    });

    function doSubmit() {
        $("#inputForm").submit();
    }
</script>
</body>
</html>
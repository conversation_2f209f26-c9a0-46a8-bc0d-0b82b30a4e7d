<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <link href="${ctxStatic}/summernote/summernote.css" rel="stylesheet">
    <link href="${ctxStatic}/summernote/summernote-bs3.css" rel="stylesheet">
    <script src="${ctxStatic}/summernote/summernote.min.js"></script>
    <script src="${ctxStatic}/summernote/summernote-zh-CN.js"></script>

    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/lang/summernote-zh-CN.min.js"></script>

    <style type="text/css">
        /* HTML5日期控件样式 */
        input[type="date"] {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 14px;
            line-height: 1.42857143;
            color: #555;
            width: 100%;
        }

        input[type="date"]::-webkit-calendar-picker-indicator {
            cursor: pointer;
            border-radius: 4px;
            margin-left: 2px;
            opacity: 0.6;
        }

        input[type="date"]:hover::-webkit-calendar-picker-indicator {
            opacity: 1;
        }

        /* 为表单组添加相对定位 */
        .form-group {
            position: relative;
        }

        .hide {
            display: none;
        }

        .mt-1 {
            margin-top: 5px;
        }

        .mt-2 {
            margin-top: 10px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-sm-12 animated fadeInRight">
            <div class="mail-box">
                <div class="mail-body">
                    <form:form enctype="multipart/form-data" id="inputForm" modelAttribute="competitionInfo"
                               action="${ctx}/qqc/competition/save" method="post" class="form-horizontal">
                        <form:hidden path="id"/>

                        <%-- 开始两列布局 --%>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>标题：</label>
                                    <div class="col-sm-8">
                                        <input type="text" autocomplete="off" value="${competitionInfo.title}" id="title"
                                               name="title" class="form-control required">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>类型：</label>
                                    <div class="col-sm-8">
                                        <select id="type" name="type" class="form-control required">
                                            <option value="">--请选择--</option>
                                            <option value="1" ${competitionInfo.type == 1 ? 'selected' : ''}>个人赛</option>
                                            <option value="2" ${competitionInfo.type == 2 ? 'selected' : ''}>团体赛</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">级别：</label>
                                    <div class="col-sm-8">
                                        <select id="level" name="level" class="form-control">
                                            <option value="">--请选择--</option>
                                            <option value="市级" ${competitionInfo.level == '市级' ? 'selected' : ''}>市级</option>
                                            <option value="省级" ${competitionInfo.level == '省级' ? 'selected' : ''}>省级</option>
                                            <option value="国级" ${competitionInfo.level == '国级' ? 'selected' : ''}>国级</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">比赛具体地点：</label>
                                    <div class="col-sm-8">
                                        <input type="text" autocomplete="off" value="${competitionInfo.competitionArea}"
                                               id="competitionArea" name="competitionArea" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">主办单位：</label>
                                    <div class="col-sm-8">
                                        <input type="text" autocomplete="off" value="${competitionInfo.hostUnit}" id="hostUnit"
                                               name="hostUnit" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">执行单位：</label>
                                    <div class="col-sm-8">
                                        <input type="text" autocomplete="off" value="${competitionInfo.executiveUnit}"
                                               id="executiveUnit" name="executiveUnit" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>报名开始时间：</label>
                                    <div class="col-sm-8">
                                        <input id="registrationStartTime" name="registrationStartTime" type="date"
                                               maxlength="20" class="form-control required"
                                               value="<fmt:formatDate value="${competitionInfo.registrationStartTime}" pattern="yyyy-MM-dd"/>"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>比赛开始时间：</label>
                                    <div class="col-sm-8">
                                        <input id="competitionStartTime" name="competitionStartTime" type="date" maxlength="20"
                                               class="form-control required"
                                               value="<fmt:formatDate value="${competitionInfo.competitionStartTime}" pattern="yyyy-MM-dd"/>"/>
                                    </div>
                                </div>
                                <!-- 缩略图上传 -->
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">缩略图：</label>
                                    <div class="col-sm-8">
                                        <input type="file" id="thumbnailImgFile" class="form-control" accept=".jpg,.jpeg,.png" />
                                        <input type="hidden" id="thumbnailImg" name="thumbnailImg" value="${competitionInfo.thumbnailImg}" />
                                        <div id="thumbnailPreview" class="mt-2 ${empty competitionInfo.thumbnailImg ? 'hide' : ''}">
                                            <img id="thumbnailPreviewImage" src="${competitionInfo.thumbnailImg}" class="img-thumbnail" style="max-height: 100px;" />
                                            <button type="button" class="btn btn-xs btn-danger mt-1" id="removeThumbnail">
                                                <i class="fa fa-times"></i> 移除图片
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">简介：</label>
                                    <div class="col-sm-8">
                                        <textarea id="introduction" name="introduction" class="form-control" rows="3">${competitionInfo.introduction}</textarea>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>状态：</label>
                                    <div class="col-sm-8">
                                        <select id="status" name="status" class="form-control required">
                                            <option value="">--请选择--</option>
                                            <option value="0" ${competitionInfo.status == 0 ? 'selected' : ''}>未开始</option>
                                            <option value="1" ${competitionInfo.status == 1 ? 'selected' : ''}>报名中</option>
                                            <option value="2" ${competitionInfo.status == 2 ? 'selected' : ''}>报名结束</option>
                                            <option value="3" ${competitionInfo.status == 3 ? 'selected' : ''}>比赛中</option>
                                            <option value="4" ${competitionInfo.status == 4 ? 'selected' : ''}>已结束</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">比赛地区：</label>
                                    <div class="col-sm-8">
                                        <sys:treeselect id="competitionRegion" name="competitionRegion" value="${competitionInfo.competitionRegion}"
                                                        labelName="competitionRegionName" labelValue="${competitionInfo.competitionRegion}"
                                                        title="区域" url="/sys/area/treeData" cssClass="form-control required" allowClear="true"/>
                                        <input type="hidden" id="regionName" name="regionName" value="${competitionInfo.competitionRegion}"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">承办单位：</label>
                                    <div class="col-sm-8">
                                        <input type="text" autocomplete="off" value="${competitionInfo.organizerUnit}"
                                               id="organizerUnit" name="organizerUnit" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">支持单位：</label>
                                    <div class="col-sm-8">
                                        <input type="text" autocomplete="off" value="${competitionInfo.supportUnit}"
                                               id="supportUnit" name="supportUnit" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>报名结束时间：</label>
                                    <div class="col-sm-8">
                                        <input id="registrationEndTime" name="registrationEndTime" type="date" maxlength="20"
                                               class="form-control required"
                                               value="<fmt:formatDate value="${competitionInfo.registrationEndTime}" pattern="yyyy-MM-dd"/>"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="red">*</font>比赛结束时间：</label>
                                    <div class="col-sm-8">
                                        <input id="competitionEndTime" name="competitionEndTime" type="date" maxlength="20"
                                               class="form-control required"
                                               value="<fmt:formatDate value="${competitionInfo.competitionEndTime}" pattern="yyyy-MM-dd"/>"/>
                                    </div>
                                </div>
                                <!-- 轮播图上传 -->
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">轮播图：</label>
                                    <div class="col-sm-8">
                                        <input type="file" id="posterImgFile" class="form-control" accept=".jpg,.jpeg,.png" />
                                        <input type="hidden" id="posterImg" name="posterImg" value="${competitionInfo.posterImg}" />
                                        <div id="posterPreview" class="mt-2 ${empty competitionInfo.posterImg ? 'hide' : ''}">
                                            <img id="posterPreviewImage" src="${competitionInfo.posterImg}" class="img-thumbnail" style="max-height: 100px;" />
                                            <button type="button" class="btn btn-xs btn-danger mt-1" id="removePoster">
                                                <i class="fa fa-times"></i> 移除图片
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <%-- 详情部分（占满整行） --%>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">详情：</label>
                            <div class="col-sm-9">
                                <form:textarea cssStyle="display: none" path="details" htmlEscape="false" rows="6"
                                               maxlength="2000" class="form-control"/>
                                <div id="summernote" class="summernote">${competitionInfo.details}</div>
                            </div>
                        </div>

                        <%-- 底部按钮 --%>
                        <div class="form-group">
                            <div class="col-sm-4 col-sm-offset-4">
                                <button type="button" class="btn btn-primary" onclick="doSubmit()">保存</button>
                                <button type="button" class="btn btn-default" onclick="window.history.back()" style="margin-left: 10px;">返回</button>
                            </div>
                        </div>
                    </form:form>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    var validateForm;

    $(document).ready(function () {
        // 为"缩略图"和"轮播图"准备的图片上传函数
        function uploadImage(fileInput, hiddenInputId, previewDivId, previewImgId, fileType) {
            var file = fileInput.files[0];
            if (!file) { return; }
            if (!file.type.match('image.*')) {
                top.layer.alert('请选择图片文件', {icon: 2});
                return;
            }
            if (file.size > 5 * 1024 * 1024) { // 5MB 限制
                top.layer.alert('图片大小不能超过5MB', {icon: 2});
                return;
            }
            var loadIndex = top.layer.load(1, {shade: [0.3, '#000']});
            var formData = new FormData();
            formData.append('file', file);
            formData.append('fileType', fileType);

            $.ajax({
                url: '${ctx}/qqc/competition/uploadFile',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    top.layer.close(loadIndex);
                    if (response.success) {
                        $("#" + hiddenInputId).val(response.body.ossUrl);
                        $("#" + previewImgId).attr('src', response.body.ossUrl);
                        $("#" + previewDivId).removeClass('hide');
                        top.layer.msg('上传成功', {icon: 1});
                    } else {
                        top.layer.alert(response.msg || '上传失败', {icon: 2});
                    }
                },
                error: function() {
                    top.layer.close(loadIndex);
                    top.layer.alert('上传请求失败', {icon: 2});
                }
            });
        }

        //Summernote富文本编辑器准备的图片上传函数
        function uploadSummernoteImage(file) {
            console.log("开始上传Summernote图片:", file.name, file.size);

            // 文件类型和大小验证
            if (!file.type.match('image.*')) {
                top.layer.alert('请选择图片文件', {icon: 2});
                return;
            }
            if (file.size > 5 * 1024 * 1024) { // 5MB 限制
                top.layer.alert('图片大小不能超过5MB', {icon: 2});
                return;
            }

            var formData = new FormData();
            formData.append('file', file);
            formData.append('fileType', 'COMPETITION_DETAILS');
            var loadIndex = top.layer.load(1, {shade: [0.3, '#000']});

            $.ajax({
                url: '${ctx}/qqc/competition/uploadFile',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    top.layer.close(loadIndex);
                    console.log("图片上传响应:", response);

                    if (response.success && response.body && response.body.ossUrl) {
                        // 使用insertImage方法插入图片
                        $('#summernote').summernote('insertImage', response.body.ossUrl);
                        top.layer.msg('图片上传成功', {icon: 1, time: 1000});
                        console.log("图片已插入编辑器:", response.body.ossUrl);
                    } else {
                        console.error("上传失败:", response);
                        top.layer.alert(response.msg || '图片上传失败', {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    top.layer.close(loadIndex);
                    console.error("上传请求失败:", xhr, status, error);
                    top.layer.alert('图片上传请求失败: ' + error, {icon: 2});
                }
            });
        }

        // 绑定"缩略图"和"轮播图"的事件
        $("#thumbnailImgFile").on('change', function() {
            uploadImage(this, 'thumbnailImg', 'thumbnailPreview', 'thumbnailPreviewImage', 'COMPETITION_THUMBNAIL');
        });
        $("#posterImgFile").on('change', function() {
            uploadImage(this, 'posterImg', 'posterPreview', 'posterPreviewImage', 'COMPETITION_POSTER');
        });
        $("#removeThumbnail").on('click', function() {
            $("#thumbnailImg").val('');
            $("#thumbnailImgFile").val('');
            $("#thumbnailPreview").addClass('hide');
        });
        $("#removePoster").on('click', function() {
            $("#posterImg").val('');
            $("#posterImgFile").val('');
            $("#posterPreview").addClass('hide');
        });
        if ($('#summernote').data('summernote')) {
            $('#summernote').summernote('destroy');
        }
        if ($('#summernote').length === 0) {
            console.error("找不到#summernote元素");
            return;
        }

        console.log("开始初始化Summernote...");

        // 初始化Summernote富文本编辑器
        $("#summernote").summernote({
            height: 400,
            minHeight: 300,
            maxHeight: 500,
            lang: 'zh-CN',
            // 添加工具栏配置，确保包含图片按钮
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']], // 确保包含picture按钮
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks: {
                // 图片上传回调
                onImageUpload: function(files) {
                    console.log("onImageUpload 回调被触发，文件数量:", files.length);
                    console.log("文件列表:", files);

                    // 处理所有选中的图片文件
                    for (var i = 0; i < files.length; i++) {
                        console.log("正在处理第", i + 1, "个文件:", files[i].name);
                        uploadSummernoteImage(files[i]);
                    }
                },
                onInit: function() {
                    console.log("Summernote初始化完成");
                    // 验证初始化是否成功
                    setTimeout(function() {
                        var testContent = $('#summernote').summernote('code');
                        console.log("初始化验证 - 当前内容:", testContent);
                        if (testContent && testContent.toString().indexOf('e.fn.init') !== -1) {
                            console.error("Summernote初始化可能有问题，code()方法返回jQuery对象");
                        }
                    }, 100);
                },
                onChange: function(contents, $editable) {
                    // 实时同步内容到隐藏字段
                    $("#details").val(contents);
                }
            }
        });

        // 初始化表单验证
        validateForm = $("#inputForm").validate({
            submitHandler: function (form) {
                var regionName = $("#competitionRegionName").val();
                $("#regionName").val(regionName);

                // 获取富文本内容 - 多种方式确保获取成功
                var summernoteContent = '';
                try {
                    // 方法1：使用code()方法
                    summernoteContent = $('#summernote').summernote('code');
                    console.log("方法1获取的内容:", summernoteContent);

                    // 如果方法1失败，尝试其他方法
                    if (!summernoteContent || summernoteContent.toString().indexOf('e.fn.init') !== -1) {
                        console.log("方法1失败，尝试方法2");
                        // 方法2：直接获取HTML内容
                        summernoteContent = $('#summernote').html();
                        console.log("方法2获取的内容:", summernoteContent);
                    }

                    // 如果还是失败，尝试第三种方法
                    if (!summernoteContent || summernoteContent.toString().indexOf('e.fn.init') !== -1) {
                        console.log("方法2失败，尝试方法3");
                        // 方法3：检查是否有note-editable元素
                        var editableContent = $('.note-editable').html();
                        if (editableContent) {
                            summernoteContent = editableContent;
                            console.log("方法3获取的内容:", summernoteContent);
                        }
                    }

                } catch (e) {
                    console.error("获取富文本内容时出错:", e);
                    summernoteContent = $('#summernote').html(); // 降级方案
                }

                console.log("最终提交的富文本内容:", summernoteContent);
                $("#details").val(summernoteContent);

                loading('正在提交，请稍等...');
                $.ajax({
                    url: '${ctx}/qqc/competition/save',
                    type: 'POST',
                    data: $(form).serialize(),
                    dataType: 'json',
                    success: function(data) {
                        top.layer.closeAll('loading');
                        if(data.success) {
                            try {
                                var activeTab = top.getActiveTab();
                                if (activeTab && typeof activeTab.attr === 'function') {
                                    var frameName = activeTab.attr("name");
                                    top.frames[frameName].location.reload();
                                } else {
                                    parent.location.reload();
                                }
                            } catch (e) {
                                parent.location.reload();
                            }
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                            top.layer.msg(data.msg || '保存成功!', {icon: 1, time: 1500});
                        } else {
                            top.layer.alert(data.msg || "保存失败", {icon: 2});
                        }
                    },
                    error: function() {
                        top.layer.closeAll('loading');
                        top.layer.alert("系统错误，请联系管理员", {icon: 2});
                    }
                });
            },
            errorContainer: "#messageBox",
            errorPlacement: function(error, element) {
                $("#messageBox").text("输入有误，请先更正。");
                if (element.is(":checkbox") || element.is(":radio") || element.parent().is(".input-append")) {
                    error.appendTo(element.parent().parent());
                } else {
                    error.insertAfter(element);
                }
            }
        });
    });

    function doSubmit() {
        $("#inputForm").submit();
    }
</script>
</body>
</html>
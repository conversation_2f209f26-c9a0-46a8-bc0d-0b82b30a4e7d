<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <title>报名信息详情</title>
    <style type="text/css">
        .form-section {
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0 15px;
            border-bottom: 1px solid #e5e5e5;
            padding-bottom: 8px;
            color: #333;
        }
        .member-card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }
        .info-row {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            width: 120px;
        }
        .id-card-images {
            margin-top: 10px;
        }
        .id-card-images img {
            max-width: 200px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
        }
        .member-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-sm-12 animated fadeInRight">
            <div class="mail-box">
                <div class="mail-body">
                    <h3 class="text-center">报名信息详情</h3>

                    <form class="form-horizontal">
                        <div class="form-section">基本信息</div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">参赛项目：</label>
                            <div class="col-sm-10">
                                <p class="form-control-static">${registration.participantProject}</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">报名途径：</label>
                            <div class="col-sm-10">
                                <p class="form-control-static">${fns:getDictLabel(registration.registrationChannel, 'registration_channel', '未填写')}</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">参赛地区：</label>
                            <div class="col-sm-10">
                                <p class="form-control-static">${registration.competitionCityName} ${registration.competitionDistrictName}</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">赛事分组：</label>
                            <div class="col-sm-10">
                                <p class="form-control-static">
                                    ${fns:getDictLabel(registration.competitionGroup, 'competition_group', '未填写')}
                                    <c:if test="${not empty registration.competitionSubgroup}">
                                        - ${fns:getDictLabel(registration.competitionSubgroup, 'competition_subgroup', '')}
                                    </c:if>
                                </p>
                            </div>
                        </div>
                        <c:if test="${registration.competitionGroup == '3'}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">乡村振兴分组：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${fns:getDictLabel(registration.ruralCompetitionSubgroup, 'rural_subgroup', '未填写')}</p>
                                </div>
                            </div>
                        </c:if>
                        <c:if test="${not empty registration.projectField}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目领域：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${fns:getDictLabel(registration.projectField, 'project_field', '未填写')}</p>
                                </div>
                            </div>
                        </c:if>

                        <div class="form-section">第一申报人信息</div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">姓名：</label>
                            <div class="col-sm-10">
                                <p class="form-control-static">${registration.firstApplicantName}</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">手机号：</label>
                            <div class="col-sm-10">
                                <p class="form-control-static">${registration.firstApplicantMobile}</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">邮箱：</label>
                            <div class="col-sm-10">
                                <p class="form-control-static">${registration.firstApplicantEmail}</p>
                            </div>
                        </div>

                        <c:if test="${not empty registration.firstApplicantHukou}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">户籍：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${registration.firstApplicantHukou}</p>
                                </div>
                            </div>
                        </c:if>

                        <c:if test="${not empty registration.firstApplicantPosition}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">职位：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${registration.firstApplicantPosition}</p>
                                </div>
                            </div>
                        </c:if>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">性别：</label>
                            <div class="col-sm-10">
                                <p class="form-control-static">
                                    <c:choose>
                                        <c:when test="${registration.firstApplicantGender == 1}">男</c:when>
                                        <c:when test="${registration.firstApplicantGender == 2}">女</c:when>
                                        <c:otherwise>未填写</c:otherwise>
                                    </c:choose>
                                </p>
                            </div>
                        </div>

                        <c:if test="${not empty registration.firstApplicantBirthday}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">出生日期：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">
                                        <fmt:formatDate value="${registration.firstApplicantBirthday}" pattern="yyyy-MM-dd"/>
                                    </p>
                                </div>
                            </div>
                        </c:if>

                        <c:if test="${not empty registration.firstApplicantGraduationTime}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">毕业时间：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">
                                        <fmt:formatDate value="${registration.firstApplicantGraduationTime}" pattern="yyyy-MM-dd"/>
                                    </p>
                                </div>
                            </div>
                        </c:if>

                        <c:if test="${not empty registration.firstApplicantIdCard}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">身份证号：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${registration.firstApplicantIdCard}</p>
                                </div>
                            </div>
                        </c:if>

                        <c:if test="${not empty registration.firstApplicantIdCardFrontFile || not empty registration.firstApplicantIdCardBackFile}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">身份证照片：</label>
                                <div class="col-sm-10">
                                    <div class="id-card-images">
                                        <c:if test="${not empty registration.firstApplicantIdCardFrontFile}">
                                            <div style="display: inline-block; margin-right: 20px;">
                                                <p><strong>正面：</strong></p>
                                                <img src="${registration.firstApplicantIdCardFrontFile}" alt="身份证正面">
                                            </div>
                                        </c:if>
                                        <c:if test="${not empty registration.firstApplicantIdCardBackFile}">
                                            <div style="display: inline-block;">
                                                <p><strong>反面：</strong></p>
                                                <img src="${registration.firstApplicantIdCardBackFile}" alt="身份证反面">
                                            </div>
                                        </c:if>
                                    </div>
                                </div>
                            </div>
                        </c:if>

                        <div class="form-section">团队成员信息</div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">团队成员：</label>
                            <div class="col-sm-10">
                                <div id="membersList">
                                </div>
                            </div>
                        </div>

                        <div class="form-section">指导老师信息</div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">指导老师：</label>
                            <div class="col-sm-10">
                                <div id="mentorsList">
                                </div>
                            </div>
                        </div>
                        <c:if test="${registration.competitionSubgroup == 'startup' || registration.competitionSubgroup == 'growth'}">
                            <div class="form-section">公司信息</div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">公司名称：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${registration.companyName}</p>
                                </div>
                            </div>

                            <c:if test="${not empty registration.companyEstablishTime}">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">成立时间：</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static">
                                            <fmt:formatDate value="${registration.companyEstablishTime}" pattern="yyyy-MM-dd"/>
                                        </p>
                                    </div>
                                </div>
                            </c:if>

                            <c:if test="${not empty registration.companyAddress}">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">公司地址：</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static">${registration.companyAddress}</p>
                                    </div>
                                </div>
                            </c:if>

                            <c:if test="${not empty registration.companyBusinessLicenseFile}">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">营业执照：</label>
                                    <div class="col-sm-10">
                                        <img src="${registration.companyBusinessLicenseFile}" style="max-width: 400px;" class="img-thumbnail">
                                    </div>
                                </div>
                            </c:if>
                        </c:if>

                        <div class="form-section">项目信息</div>
                        <c:if test="${not empty registration.projectBrief}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目简介：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${registration.projectBrief}</p>
                                </div>
                            </div>
                        </c:if>

                        <c:if test="${not empty registration.industryCompetitiveAdvantage}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">同行业竞争优势：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${registration.industryCompetitiveAdvantage}</p>
                                </div>
                            </div>
                        </c:if>

                        <c:if test="${not empty registration.socialBenefits}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">社会效益：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${registration.socialBenefits}</p>
                                </div>
                            </div>
                        </c:if>

                        <c:if test="${not empty registration.teamQuality}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">团队素质：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${registration.teamQuality}</p>
                                </div>
                            </div>
                        </c:if>

                        <c:if test="${not empty registration.financialOperation}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">财务运营：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${registration.financialOperation}</p>
                                </div>
                            </div>
                        </c:if>

                        <c:if test="${not empty registration.marketProspect}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">市场前景：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${registration.marketProspect}</p>
                                </div>
                            </div>
                        </c:if>

                        <c:if test="${not empty registration.productService}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">产品服务：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">${registration.productService}</p>
                                </div>
                            </div>
                        </c:if>

                        <c:if test="${not empty registration.projectPlanFile}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目计划书：</label>
                                <div class="col-sm-10">
                                    <a href="${registration.projectPlanFile}" target="_blank" class="btn btn-primary">
                                        <i class="fa fa-file-pdf-o"></i> 查看项目计划书
                                    </a>
                                </div>
                            </div>
                        </c:if>

                        <c:if test="${not empty registration.isAgreePublic}">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">是否同意公开展示：</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static">
                                        <c:choose>
                                            <c:when test="${registration.isAgreePublic == 1}">是</c:when>
                                            <c:when test="${registration.isAgreePublic == 0}">否</c:when>
                                            <c:otherwise>未填写</c:otherwise>
                                        </c:choose>
                                    </p>
                                </div>
                            </div>
                        </c:if>

<%--                        <div class="form-group">--%>
<%--                            <div class="col-sm-4 col-sm-offset-4">--%>
<%--                                <button type="button" class="btn btn-default" onclick="window.history.back()">返回</button>--%>
<%--                            </div>--%>
<%--                        </div>--%>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    // HTML转义函数，确保安全显示
    function htmlEscape(str) {
        if (!str) return '未填写';
        return String(str)
            .replace(/&/g, '&amp;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;');
    }

    $(document).ready(function() {
        // 安全地解析团队成员数据
        try {
            var membersDataRaw = '${registration.projectMembers}';
            console.log('团队成员原始数据:', membersDataRaw);

            if (membersDataRaw && membersDataRaw !== 'null' && membersDataRaw !== '' && membersDataRaw !== '[]') {
                // 处理HTML编码
                membersDataRaw = membersDataRaw.replace(/&quot;/g, '"');
                console.log('处理编码后的团队成员数据:', membersDataRaw);

                var members = JSON.parse(membersDataRaw);
                console.log('解析后的团队成员数据:', members);

                if (Array.isArray(members) && members.length > 0) {
                    displayMembers(members);
                } else {
                    $('#membersList').html('<p class="form-control-static">暂无团队成员信息</p>');
                }
            } else {
                $('#membersList').html('<p class="form-control-static">暂无团队成员信息</p>');
            }
        } catch (e) {
            console.error('解析团队成员数据失败:', e);
            $('#membersList').html('<p class="form-control-static">团队成员数据格式错误</p>');
        }

        // 安全地解析指导老师数据
        try {
            var mentorsDataRaw = '${registration.mentors}';
            console.log('指导老师原始数据:', mentorsDataRaw);

            if (mentorsDataRaw && mentorsDataRaw !== 'null' && mentorsDataRaw !== '' && mentorsDataRaw !== '[]') {
                // 处理HTML编码
                mentorsDataRaw = mentorsDataRaw.replace(/&quot;/g, '"');
                console.log('处理编码后的指导老师数据:', mentorsDataRaw);

                var mentors = JSON.parse(mentorsDataRaw);
                console.log('解析后的指导老师数据:', mentors);

                if (Array.isArray(mentors) && mentors.length > 0) {
                    displayMentors(mentors);
                } else {
                    $('#mentorsList').html('<p class="form-control-static">暂无指导老师信息</p>');
                }
            } else {
                $('#mentorsList').html('<p class="form-control-static">暂无指导老师信息</p>');
            }
        } catch (e) {
            console.error('解析指导老师数据失败:', e);
            $('#mentorsList').html('<p class="form-control-static">指导老师数据格式错误</p>');
        }
    });

    function displayMembers(members) {
        var html = '';
        members.forEach(function(member, index) {
            console.log('显示团队成员', index + 1, ':', member);

            html += '<div class="member-card">';
            html += '<div class="member-title">团队成员' + (index + 1) + '</div>';

            // 第一行：姓名、手机号、性别
            html += '<div class="info-row">';
            html += '<span class="info-label">姓名：</span>' + htmlEscape(member.name);
            html += '<span class="info-label" style="margin-left: 30px;">手机号：</span>' + htmlEscape(member.mobile || member.contact || '');

            // 修复性别显示逻辑：1=男，2=女
            var genderText = '未填写';
            if (member.gender == '1' || member.gender == 1) {
                genderText = '男';
            } else if (member.gender == '2' || member.gender == 2) {
                genderText = '女';
            }
            html += '<span class="info-label" style="margin-left: 30px;">性别：</span>' + genderText;
            html += '</div>';

            // 第二行：邮箱、身份证号
            html += '<div class="info-row">';
            html += '<span class="info-label">邮箱：</span>' + htmlEscape(member.email);
            html += '<span class="info-label" style="margin-left: 30px;">身份证号：</span>' + htmlEscape(member.idCard);
            html += '</div>';

            // 第三行：出生日期、户籍、职位
            html += '<div class="info-row">';
            html += '<span class="info-label">出生日期：</span>' + htmlEscape(member.birthday);
            html += '<span class="info-label" style="margin-left: 30px;">户籍：</span>' + htmlEscape(member.hukou);
            html += '</div>';

            html += '<div class="info-row">';
            html += '<span class="info-label">职位：</span>' + htmlEscape(member.position);
            html += '</div>';

            // 如果有身份证照片，显示
            if (member.idCardFront || member.idCardBack) {
                html += '<div class="id-card-images">';
                if (member.idCardFront) {
                    html += '<div style="display: inline-block; margin-right: 20px;">';
                    html += '<p><strong>身份证正面：</strong></p>';
                    html += '<img src="' + htmlEscape(member.idCardFront) + '" alt="身份证正面">';
                    html += '</div>';
                }
                if (member.idCardBack) {
                    html += '<div style="display: inline-block;">';
                    html += '<p><strong>身份证反面：</strong></p>';
                    html += '<img src="' + htmlEscape(member.idCardBack) + '" alt="身份证反面">';
                    html += '</div>';
                }
                html += '</div>';
            }
            html += '</div>';
        });
        $('#membersList').html(html);
    }

    function displayMentors(mentors) {
        var html = '';
        mentors.forEach(function(mentor, index) {
            console.log('显示指导老师', index + 1, ':', mentor);

            html += '<div class="member-card">';
            html += '<div class="member-title">指导老师' + (index + 1) + '</div>';

            html += '<div class="info-row">';
            html += '<span class="info-label">姓名：</span>' + htmlEscape(mentor.name);
            html += '<span class="info-label" style="margin-left: 30px;">手机号：</span>' + htmlEscape(mentor.mobile);
            html += '</div>';

            html += '<div class="info-row">';
            html += '<span class="info-label">邮箱：</span>' + htmlEscape(mentor.email);
            html += '<span class="info-label" style="margin-left: 30px;">工作单位：</span>' + htmlEscape(mentor.workplace);
            html += '</div>';

            html += '<div class="info-row">';
            html += '<span class="info-label">职位：</span>' + htmlEscape(mentor.position);
            html += '</div>';

            html += '</div>';
        });
        $('#mentorsList').html(html);
    }
</script>
</body>
</html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>

    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <!-- SUMMERNOTE -->
    <link href="${ctxStatic}/summernote/summernote.css" rel="stylesheet">
    <link href="${ctxStatic}/summernote/summernote-bs3.css" rel="stylesheet">

    <script src="${ctxStatic}/summernote/summernote.min.js"></script>
    <script src="${ctxStatic}/summernote/summernote-zh-CN.js"></script>

    <style>
    </style>
</head>

<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="row">

        <div class="col-sm-12 animated fadeInRight">
            <div class="mail-box">
                <div class="mail-body">
                    <form:form modelAttribute="capital" id="inputForm" action="" method="post" class="form-horizontal">
                        <div class="form-group">
                            <form:hidden path="id"/>
                            <label class="col-sm-2 control-label"><font color="red">*</font>标题：</label>
                            <div class="col-sm-8">
                                <input type="text" readonly="readonly" value="${capital.title}" id="title" name="title"  class="form-control required" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">发布者：</label>
                            <div class="col-sm-8">
                                <input type="text" readonly="readonly" value="${capital.announcer}" id="subtitle" name="announcer"  class="form-control required" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">模块选择：</label>
                            <div class="col-sm-8">
                                <form:select cssStyle="cursor: pointer" disabled="true" readonly="true" style="width:150px;" path="sub"  class="form-control m-b">
                                    <form:option value="" label=""/>
                                    <form:options items="${fns:getDictList('qqc_company_type')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
                                </form:select>
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">投资区间：</label>
                            <div class="col-sm-8">
                                <input type="text" readonly="readonly" value="${capital.intervalPre}" style="width:130px;margin:0;padding:0;text-align:center;"
                                       htmlEscape="false" maxlength="200" class="form-control required number" />
                                &nbsp;&nbsp;/&nbsp;&nbsp;
                                <input type="text" readonly="readonly" value="${capital.intervalSuf}" style="width:130px;margin:0;padding:0;text-align:center;"
                                       htmlEscape="false" maxlength="200" class="form-control required number" />
                                &nbsp;&nbsp;单位(万)
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">地址：</label>
                            <div class="col-sm-8">
                                <input type="text" readonly="readonly" value="${capital.indexAddress}" id="indexAddress" name="indexAddress"  class="form-control" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">标签：</label>
                            <div class="col-sm-8">
                                <input type="text" readonly="readonly" value="${capital.label}" id="label" name="label"  class="form-control required" />
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">图片：</label>
                            <div class="col-sm-8">
                                <img style="width: 110px;height: 110px" src="${capital.img}">
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">详情：</label>
                            <div class="col-sm-8">
                                <div class="mail-text">
                                    <div id="summernote" class="summernote">
                                            ${capital.content}"
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </form:form>
                </div>
            </div>
        </div>

    </div>
</div>
<script type="text/javascript">
    $(document).ready(function () {
        $("#summernote").summernote({
            height: 400,
            minHeight: 300,
            maxHeight: 500,
            lang:'zh-CN',
        });
    });
</script>
</body>

</html>
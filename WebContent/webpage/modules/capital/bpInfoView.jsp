<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>BP信息</title>
	<meta name="decorator" content="default"/>
</head>
<body>
	<form:form id="inputForm" modelAttribute="BPInfo" action="${ctx}/qqc/bp/save" method="post" class="form-horizontal">
		<form:hidden path="id"/>
		<sys:message content="${message}"/>
		<table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
		   <tbody>
		      <tr>
		         <td  class="width-15 active">	<label class="pull-right"><font color="red">*</font>BP名称：</label></td>
		         <td class="width-35" >
		         	<form:input path="bpName" readonly="true" htmlEscape="false" maxlength="200" class="form-control required"/>
		         </td>
				  <td  class="width-15 active">	<label class="pull-right"><font color="red">*</font>创始人名称：</label></td>
				  <td  class="width-35" >
					  <form:input path="userName" readonly="true" htmlEscape="false" maxlength="200" class="form-control required"/>
				  </td>
		      </tr>
			  <tr>
				  <td  class="width-15 active">	<label class="pull-right"><font color="red">*</font>性别：</label></td>
				  <td  class="width-35" >
					  <input readonly="readonly" value="${fns:getDictLabel(bpInfo.sex, 'sex', '')}" class="form-control required"/>
				  </td>
				  <td  class="width-15 active">	<label class="pull-right"><font color="red">*</font>联系方式：</label></td>
				  <td class="width-35" >
					  <form:input path="phone" readonly="true" htmlEscape="false" maxlength="200" class="form-control number required"/>
				  </td>
			  </tr>
			  <tr>
				  <td  class="width-15 active">	<label class="pull-right">融资需求：</label></td>
				  <td colspan="3" class="width-35" >
					  <form:textarea path="demand" readonly="true" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
				  </td>
			  </tr>
		      <tr>
		         <td  class="width-15 active">	<label class="pull-right">产品介绍：</label></td>
		         <td colspan="3" class="width-35" >
					 <form:textarea path="product" readonly="true" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
		         </td>
		      </tr>
		      <tr>
		         <td  class="width-15 active">	<label class="pull-right">市场需求：</label></td>
		         <td colspan="3" class="width-35" >
					 <form:textarea path="market" readonly="true" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
		         </td>
		      </tr>
		      <tr>
		         <td  class="width-15 active">	<label class="pull-right">团队介绍：</label></td>
		         <td colspan="3" class="width-35" >
					 <form:textarea path="team" readonly="true" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/></td>
		      </tr>
		</tbody>
		</table>
	</form:form>
</body>
</html>
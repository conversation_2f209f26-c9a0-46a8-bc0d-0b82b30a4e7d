<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>BP新增</title>
	<meta name="decorator" content="default"/>
	<script type="text/javascript">
		var validateForm;
		function doSubmit(){//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
		  if(validateForm.form()){
			  $("#inputForm").submit();
			  return true;
		  }
		  return false;
		}
		$(document).ready(function() {
			//$("#name").focus();
			validateForm = $("#inputForm").validate({
				submitHandler: function(form){
					loading('正在提交，请稍等...');
					form.submit();
				},
				errorContainer: "#messageBox",
				errorPlacement: function(error, element) {
					$("#messageBox").text("输入有误，请先更正。");
					if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
						error.appendTo(element.parent().parent());
					} else {
						error.insertAfter(element);
					}
				}
			});
		});
		
	</script>
</head>
<body>
	<form:form id="inputForm" modelAttribute="BPInfo" action="${ctx}/qqc/bp/save" method="post" class="form-horizontal">
		<form:hidden path="id"/>
		<sys:message content="${message}"/>
		<table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
		   <tbody>
		      <tr>
		         <td  class="width-15 active">	<label class="pull-right"><font color="red">*</font>BP名称：</label></td>
		         <td class="width-35" >
		         	<form:input autocomplete="off" path="bpName" htmlEscape="false" maxlength="200" class="form-control required"/>
		         </td>
				  <td  class="width-15 active">	<label class="pull-right"><font color="red">*</font>创始人名称：</label></td>
				  <td  class="width-35" >
					  <form:input autocomplete="off" path="userName" htmlEscape="false" maxlength="200" class="form-control required"/>
				  </td>
		      </tr>
			  <tr>
				  <td  class="width-15 active">	<label class="pull-right"><font color="red">*</font>性别：</label></td>
				  <td  class="width-35" >
					  <form:select path="sex"  class="form-control required m-b">
						  <form:option value="" label=""/>
						  <form:options items="${fns:getDictList('sex')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
					  </form:select>
				  </td>
				  <td  class="width-15 active">	<label class="pull-right"><font color="red">*</font>联系方式：</label></td>
				  <td class="width-35" >
					  <form:input autocomplete="off" path="phone" htmlEscape="false" maxlength="200" class="form-control number required"/>
				  </td>
			  </tr>
			  <tr>
				  <td  class="width-15 active">	<label class="pull-right">融资需求：</label></td>
				  <td colspan="3" class="width-35" >
					  <form:textarea path="demand" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
				  </td>
			  </tr>
		      <tr>
		         <td  class="width-15 active">	<label class="pull-right">产品介绍：</label></td>
		         <td colspan="3" class="width-35" >
					 <form:textarea path="product" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
		         </td>
		      </tr>
		      <tr>
		         <td  class="width-15 active">	<label class="pull-right">市场需求：</label></td>
		         <td colspan="3" class="width-35" >
					 <form:textarea path="market" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
		         </td>
		      </tr>
		      <tr>
		         <td  class="width-15 active">	<label class="pull-right">团队介绍：</label></td>
		         <td colspan="3" class="width-35" >
					 <form:textarea path="team" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/></td>
		      </tr>
		</tbody>
		</table>
	</form:form>
</body>
</html>
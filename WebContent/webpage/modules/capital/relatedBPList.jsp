<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>BP列表</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
	</style>
	<script type="text/javascript">
		$(document).ready(function() {
			check();
			$('#bpInfoTable thead tr th input.i-checks').on('ifChecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
				$('#bpInfoTable tbody tr td input.i-checks').iCheck('check');
				check();
			});

			$('#bpInfoTable thead tr th input.i-checks').on('ifUnchecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
				$('#bpInfoTable tbody tr td input.i-checks').iCheck('uncheck');
				check();
			});

			$('#bpInfoTable tbody tr input.i-checks').on('ifChecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
				check();
			});

			$('#bpInfoTable tbody tr input.i-checks').on('ifUnchecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
				check();
			});
		});

		function check(){
			// var url = $(this).attr('data-url');
			var str="";
			var ids="";
			$("#bpInfoTable tbody tr td input.i-checks:checkbox").each(function(){
				if(true == $(this).is(':checked')){
					str+=$(this).attr("id")+",";
				}
			});
			if(str.substr(str.length-1)== ','){
				ids = str.substr(0,str.length-1);
			}
			$("#bpIds").val(ids);
		}
	</script>
	<script type="text/javascript">
		var validateForm;
		function doSubmit(){//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
			/*var ids = $("#bpIds").val();
			if(ids == ""){
				top.layer.alert('请至少选择一条数据!', {icon: 0, title:'警告'});
				return false;
			}*/
			if(validateForm.form()){
				$("#inputForm").submit();
				return true;
			}
			return false;
		}
		$(document).ready(function () {
			validateForm = $("#inputForm").validate({
				submitHandler: function(form){
					loading('正在提交，请稍等...');
					form.submit();
				},
				errorContainer: "#messageBox",
				errorPlacement: function(error, element) {

				}
			});

		});

	</script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
<div class="ibox">
<div class="ibox-title">
		<h5>BP信息 </h5>
		<div class="ibox-tools">
			<a class="collapse-link">
				<i class="fa fa-chevron-up"></i>
			</a>
			<a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
				<i class="fa fa-wrench"></i>
			</a>
			<ul class="dropdown-menu dropdown-user">
				<li><a href="#">选项1</a>
				</li>
				<li><a href="#">选项2</a>
				</li>
			</ul>
			<a class="close-link">
				<i class="fa fa-times"></i>
			</a>
		</div>
	</div>
    
    <div class="ibox-content">
	<sys:message content="${message}"/>
	
	<!-- 查询条件 -->
	<div class="row">
	<div class="col-sm-12">
	<form:form id="searchForm" modelAttribute="BPInfo" action="" method="post" class="form-inline">
		<input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
		<div class="form-group">
			<span>项目名称：&nbsp;</span>
			<form:input autocomplete="off" path="bpName" cssStyle="cursor: pointer" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
			<span>创建人：&nbsp;</span>
			<form:input autocomplete="off" path="userName" cssStyle="cursor: pointer" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
		</div>
	</form:form>
	<br/>
	</div>
	</div>
	
	
	<!-- 工具栏 -->
	<div class="row">
	<div class="col-sm-12">
		<div class="pull-left">
			</div>
		<div class="pull-right">
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
		</div>
	</div>
	</div>

	<!-- 表单栏 -->
	<form:form modelAttribute="bpInfo" id="inputForm" action="${ctx}/qqc/bp/relatedBP" method="post" class="form-horizontal">
		<input type="hidden" id="bpIds" name="id" value=""/>
		<input type="hidden" id="delbpIds" name="id" value=""/>
		<form:hidden path="capitalId" />
	</form:form>

	<!-- 数据栏 -->
	<table id="bpInfoTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
		<thead>
			<tr>
				<th> <input type="checkbox"  class="i-checks selectAll"></th>
				<th>项目名称</th>
				<th>创始人姓名</th>
				<th>联系方式</th>
				<th>投递时间</th>
			</tr>
		</thead>
		<tbody>
		<c:forEach items="${page.list}" var="b">
			<tr>
				<td>
					<c:if test="${b.capitalId == bpInfo.capitalId}">
						<input type="checkbox" id="${b.id}" checked="checked" class="i-checks">
					</c:if>
					<c:if test="${b.capitalId != bpInfo.capitalId}">
						<input type="checkbox" id="${b.id}" class="i-checks">
					</c:if>

				</td>
				<td><a href="#" onclick="openDialogView('查看BP信息', '${ctx}/qqc/bp/form?type=View&id=${b.id}','800px', '700px')">
					${fns:abbr(b.bpName,50)}
				</a></td>
				<td>
					${fns:abbr(b.userName,50)}
				</td>
				<td>
					${fns:abbr(b.phone,50)}
				</td>
				<td>
					${fns:abbr(b.time,50)}
				</td>
			</tr>
		</c:forEach>
		</tbody>
	</table>
	<!-- 分页代码 -->
	<table:page page="${page}"></table:page>
	<br/>
	<br/>
	</div>
	</div>
</div>
</body>
</html>
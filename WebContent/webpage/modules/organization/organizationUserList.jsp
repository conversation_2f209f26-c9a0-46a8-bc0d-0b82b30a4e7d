<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>加入组织</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
		.btn-default{
			color: #333 !important;
			background-color: #fff !important;
			border-color: #ccc !important;
		}
	</style>
	<script type="text/javascript">
		
	</script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
<div class="ibox">
<div class="ibox-title">
		<h5>加入组织 </h5>
		<div class="ibox-tools">
			<a class="collapse-link">
				<i class="fa fa-chevron-up"></i>
			</a>
			<a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
				<i class="fa fa-wrench"></i>
			</a>
			<ul class="dropdown-menu dropdown-user">
				<li><a href="#">选项1</a>
				</li>
				<li><a href="#">选项2</a>
				</li>
			</ul>
			<a class="close-link">
				<i class="fa fa-times"></i>
			</a>
		</div>
	</div>
    
    <div class="ibox-content">
	<sys:message content="${message}"/>
	
	<!-- 查询条件 -->
	<div class="row">
	<div class="col-sm-12">
	<form:form id="searchForm" modelAttribute="organizationUser" action="" method="post" class="form-inline">
		<input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
		<div class="form-group">
			<span>组织名称：&nbsp;</span>
			<form:input autocomplete="off" path="organization.name" cssStyle="cursor: pointer" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
			<span>加入人姓名：&nbsp;</span>
			<form:input autocomplete="off" path="wechatUserinfo.nickname" cssStyle="cursor: pointer" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
			
		</div>
	</form:form>
	<br/>
	</div>
	</div>
	
	
	<!-- 工具栏 -->
	<div class="row">
	<div class="col-sm-12">
		<div class="pull-left">
			 <%-- <shiro:hasPermission name="organizationUser:add">
                 <table:addRow width="1000px" height="700px" url="${ctx}/organizationUser/Form" title="添加"></table:addRow><!-- 增加按钮 -->
             </shiro:hasPermission> --%>
             <shiro:hasPermission name="organizationUser:del">
                 <table:delRow url="${ctx}/organizationUser/deleteAll" id="capitalTable"></table:delRow><!-- 删除按钮 -->
             </shiro:hasPermission>
		</div>

		<div class="pull-right">
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
		</div>
	</div>
	</div>
	
	
	
	
	<table id="organizationUserTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
		<thead>
			<tr>
				<th> <input type="checkbox" class="i-checks"></th>
				<th>组织名称</th>
				<th>加入人姓名</th>
				<th>加入人电话</th>
				<th>加入时间</th>
				<th>操作</th>

			</tr>
		</thead>
		<tbody>
		<c:forEach items="${page.list}" var="organizationUser">
			<tr>
				<td> <input id="${organizationUser.id}" name="${organizationUser.id}"  type="checkbox" class="i-checks"></td>
				
				<td><a title="${organizationUser.id}" href="#" onclick="openDialogView('查看加入组织信息', '${ctx}/organizationUser/form?id=${organizationUser.id}','1000px', '700px')">
						${fns:abbr(organizationUser.organization.name,30)}
				</a></td>
				<td>
					${organizationUser.wechatUserinfo.nickname}
				</td>
				<td>
					${organizationUser.wechatUserinfo.tels}
				</td>
				<td>
					<fmt:formatDate value='${organizationUser.createDate}' pattern='yyyy-MM-dd'/>
				</td>
				<td>
				  <shiro:hasPermission name="organizationUser:view">
                      <a title="查看" href="#" onclick="openDialogView('查看组织', '${ctx}/organizationUser/form?id=${organizationUser.id}','1000px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
                  </shiro:hasPermission>
                  <shiro:hasPermission name="organizationUser:edit">
                      <a title="修改" href="#" onclick="openDialog('修改组织', '${ctx}/organizationUser/form?id=${organizationUser.id}','1000px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
                  </shiro:hasPermission>
                  <shiro:hasPermission name="organization:del">
                      <a title="删除" href="${ctx}/organizationUser/delete?id=${organizationUser.id}" onclick="return confirmx('确认要退出该组织吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
                  </shiro:hasPermission>
				</td>
			</tr>
		</c:forEach>
		</tbody>
	</table>
	<!-- 分页代码 -->
	<table:page page="${page}"></table:page>
	<br/>
	<br/>
	</div>
	</div>
</div>
</body>
<script>

</script>
</html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>

    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <!-- SUMMERNOTE -->
    <link href="${ctxStatic}/summernote/summernote.css" rel="stylesheet">
    <link href="${ctxStatic}/summernote/summernote-bs3.css" rel="stylesheet">

    <link rel="stylesheet" type="text/css" href="${ctxStatic}/webuploader-0.1.5/webuploader.css">
    <link rel="stylesheet" type="text/css" href="${ctxStatic}/webuploader-0.1.5/demo.css">

    <script src="${ctxStatic}/summernote/summernote.min.js"></script>
    <script src="${ctxStatic}/summernote/summernote-zh-CN.js"></script>

    <script type="text/javascript">
        // 添加全局站点信息
        var BASE_URL = '/webuploader';
    </script>
    <script type="text/javascript" src="${ctxStatic}/webuploader-0.1.5/webuploader.js"></script>
    <style>
        #uploader .queueList {
            margin: 0px;
            border: none;
        }
        #uploader .webuploader-pick{
            padding: 0px;
        }
        #uploader .queueList.webuploader-dnd-over {
            border: none;
        }
    </style>
</head>

<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="row">

        <div class="col-sm-12 animated fadeInRight">
            <div class="mail-box">
                <div class="mail-body">
                    <form:form enctype="multipart/form-data"  id="inputForm" modelAttribute="organization" action="${ctx}/organization/save" method="post" class="form-horizontal">
                        
                        <div class="form-group">
                            <form:hidden path="id"/>
                            <label class="col-sm-2 control-label"><font color="red">*</font>组织名称：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${organization.name}" id="name" name="name"  class="form-control required" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>组织类型：</label>
                            <div class="col-sm-8">
                                <form:select path="type" class="form-control required m-b">
                                    <form:option value="" label="请选择"/>
                                    <form:options items="${fns:getDictList('qqc_organization_type')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
                                </form:select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">图片：</label>
                            <div class="col-sm-8">
                                <form:hidden  path="img" htmlEscape="false"  maxlength="255" class="form-control "/>
						        <sys:ckfinder input="img" type="images" uploadPath="/qqchuang/organization"  selectMultiple="false" enableJcrop="true" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>地区：</label>
                            <div class="col-sm-8">
                                <sys:treeSelectMulti id="area" name="areaId" value="${organization.areaId}" labelName="areaName" labelValue="${organization.area.name}"
                                                     title="区域" url="/sys/area/treeData" extId="" cssClass="form-control required m-s" allowClear="true"/>
                            </div>
                        </div>
                          <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>具体地址：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${organization.address}" id="address" name="address"  class="form-control required">
                            </div>
                        </div>
                          <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>负责人姓名：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${organization.headName}" id="headName" name="headName"  class="form-control required">
                            </div>
                        </div>
                          <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>负责人电话：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${organization.headPhone}" id="headPhone" name="headPhone"  class="form-control required">
                            </div>
                        </div>
                          <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>联系人姓名：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${organization.contactName}" id="contactName" name="contactName"  class="form-control required">
                            </div>
                        </div>
                           <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>联系人电话：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${organization.contactPhone}" id="contactPhone" name="contactPhone"  class="form-control required">
                            </div>
                        </div>
                           <div class="form-group">
                            <label class="col-sm-2 control-label"><font color="red">*</font>固定电话：</label>
                            <div class="col-sm-8">
                                <input type="text" autocomplete="off" value="${organization.telephone}" id="telephone" name="telephone"  class="form-control required">
                            </div>
                        </div>
                       
                         <div class="form-group">
                            <label class="col-sm-2 control-label">介绍：</label>
                            <div class="col-sm-8">
                                <form:textarea cssStyle="display: none" path="detailed" htmlEscape="false" rows="6" maxlength="2000" class="form-control"/>
                                <div class="mail-text">
                                    <div id=summernote class="summernote">
                                        ${organization.detailed}
                                </div>
                            </div>
                        </div>
                       
                    </form:form>
                </div>
            </div>
        </div>

    </div>
</div>
<script type="text/javascript">
    var validateForm;
    var radioVal;
    function doSubmit(){//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
        if(validateForm.form()){
            $("#inputForm").submit();
            return true;
        }
        return false;
    }
    $(document).ready(function () {
    	  $("#summernote").summernote({
              height: 400,
              minHeight: 300,
              maxHeight: 500,
              lang:'zh-CN',
          });
          validateForm = $("#inputForm").validate({
              submitHandler: function(form){
                  var str= $('#summernote').code();
                  $("#detailed").val(str);
                  loading('正在提交，请稍等...');
                  form.submit();
              },
              errorContainer: "#messageBox",
              errorPlacement: function(error, element) {
                  $("#messageBox").text("输入有误，请先更正。");
                  if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
                      error.appendTo(element.parent().parent());
                  } else {
                      error.insertAfter(element);
                  }
              }
          });

        $("input[type=radio]").on("click",function(){
            if ($(this).val() === '1'){
                $("#linkDiv").show()
                $("#contentDiv").hide()
                $("#content").val("");
                $("#summernote").code('');
            }else if($(this).val() === '2'){
                $("#linkDiv").hide()
                $("#contentDiv").show();
                $("#link").val("");
            }
            radioVal = $(this).val();
        })

    });

</script>
<script type="text/javascript">
    jQuery(function() {
        var $ = jQuery,    // just in case. Make sure it's not an other libaray.

            $wrap = $('#uploader'),
            // 图片容器
            $queue = $('<ul class="filelist"></ul>')
                .appendTo( $wrap.find('.queueList') ),


            // 没选择文件之前的内容。
            $img = $wrap.find('.img'),

            // 优化retina, 在retina下这个值是2
            ratio = window.devicePixelRatio || 1,

            // 缩略图大小
            thumbnailWidth = 110 * ratio,
            thumbnailHeight = 110 * ratio,

            // 所有文件的进度信息，key为file id
            percentages = {},

            //图片选择初始化
            supportTransition = (function(){
                var s = document.createElement('p').style,
                    r = 'transition' in s ||
                        'WebkitTransition' in s ||
                        'MozTransition' in s ||
                        'msTransition' in s ||
                        'OTransition' in s;
                s = null;
                return r;
            })(),

            // WebUploader实例
            uploader;
        if ( !WebUploader.Uploader.support() ) {
            alert( 'Web Uploader 不支持您的浏览器！如果你使用的是IE浏览器，请尝试升级 flash 播放器');
            throw new Error( 'WebUploader does not support the browser you are using.' );
        }

        // 实例化
        uploader = WebUploader.create({
            pick: {
                id: '#filePicker',
            },
            dnd: '#uploader .queueList',
            paste: document.body,

            accept: {
                title: 'Images',
                extensions: 'gif,jpg,jpeg,bmp,png',
                mimeTypes: 'image/*'
            },
            // swf文件路径
            swf: BASE_URL + '/js/Uploader.swf',
            disableGlobalDnd: true,

            chunked: true,
            server: '${ctx}/qqc/news/imageUpload',
            fileNumLimit: 1,//一次最多上传多少张照片
        });

        // 当有文件添加进来时执行，负责view的创建
        function addFile( file ) {
            var $li = $( '<li id="' + file.id + '">' +
                '<p class="imgWrap"></p>'+
                '<p style="display:none;" class="title">' + file.name + '</p>' +
                '</li>' ),
                $btns = $('<div class="file-panel">' +
                    '<span title="删除" class="cancel">删除</span>' +
                    '<span title="向右旋转" class="rotateRight">向右旋转</span>' +
                    '<span title="向左旋转" class="rotateLeft">向左旋转</span></div>').appendTo( $li ),
                $wrap = $li.find( 'p.imgWrap' ),
                showError = function( code ) {
                    switch( code ) {
                        case 'exceed_size':
                            text = '文件大小超出';
                            break;
                        case 'interrupt':
                            text = '上传暂停';
                            break;
                        case 'Q_TYPE_DENIED':
                            text = '不支持该格式';
                            break;
                        default:
                            text = '上传失败，请重试';
                            break;
                    }
                };
            if ( file.getStatus() === 'invalid' ) {
                showError( file.statusText );
            } else {
                // @todo lazyload
                $wrap.text( '预览中' );
                uploader.makeThumb( file, function( error, src ) {
                    if ( error ) {
                        $wrap.text( '不能预览' );
                        return;
                    }
                    var width = file._info.width;
                    var height = file._info.height;
                    var img = $("<img style='width: "+width+";height: "+height+"' src="+src+">");
                    $wrap.empty().append( img );
                }, thumbnailWidth, thumbnailHeight );
                percentages[ file.id ] = [ file.size, 0 ];
                file.rotation = 0;
            }

            $li.on( 'mouseenter', function() {
                $btns.stop().animate({height: 30});
            });

            $li.on( 'mouseleave', function() {
                $btns.stop().animate({height: 0});
            });

            $btns.on( 'click', 'span', function() {
                var index = $(this).index(),
                    deg;
                switch ( index ) {
                    case 0:
                        uploader.removeFile( file );
                        return;
                    case 1:
                        file.rotation += 90;
                        break;
                    case 2:
                        file.rotation -= 90;
                        break;
                }
                if ( supportTransition ) {
                    deg = 'rotate(' + file.rotation + 'deg)';
                    $wrap.css({
                        '-webkit-transform': deg,
                        '-mos-transform': deg,
                        '-o-transform': deg,
                        'transform': deg
                    });
                } else {
                    $wrap.css( 'filter', 'progid:DXImageTransform.Microsoft.BasicImage(rotation='+ (~~((file.rotation/90)%4 + 4)%4) +')');
                }
            });
            $li.appendTo( $queue );
        }
        // 负责view的销毁
        function removeFile( file ) {
            var $li = $('#'+file.id);
            delete percentages[ file.id ];
            $li.off().find('.file-panel').off().end().remove();
        }

 
    });
</script>
</body>

</html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>组织列表</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
		.btn-default{
			color: #333 !important;
			background-color: #fff !important;
			border-color: #ccc !important;
		}
	</style>
	<script type="text/javascript">
		$(document).ready(function(){
			laydate({
				elem: '#createDate',
				format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
				festival: true, //显示节日
				istime: false, //是否开启时间选择<br/>
				isclear: true, //是否显示清空<br/>
				istoday: true, //是否显示今天<br/>
				choose: function(datas){ //选择日期完毕的回调
					$("#createDate").val(datas+" 00:00:00")
				}
			});
		})
		

		function addorganization(add){
			var url = '${ctx}/organization/form';
			openDialog("新增组织",url,"1000px", "700px");
		}

		function selectActity(){
			var id="";
			var name="";
			var checked = 0;
			$("#organizationTable tbody tr td input.i-checks:checkbox").each(function(){
				if(true == $(this).is(':checked')){
					checked ++;
					id = $(this).attr("id");
					name = $(this).attr("name");
				}
			})
			if(checked > 1){
				top.layer.alert('只能选择一个活动!', {icon: 0, title:'警告'});
				return;
			}
			if(id == ""){
				top.layer.alert('请至少选择一条数据!', {icon: 0, title:'警告'});
				return;
			}
			var myMap = {
				"id":id,
				"name":name
			};
			return myMap;
		}
	</script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
<div class="ibox">
<div class="ibox-title">
		<h5>组织信息 </h5>
		<div class="ibox-tools">
			<a class="collapse-link">
				<i class="fa fa-chevron-up"></i>
			</a>
			<a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
				<i class="fa fa-wrench"></i>
			</a>
			<ul class="dropdown-menu dropdown-user">
				<li><a href="#">选项1</a>
				</li>
				<li><a href="#">选项2</a>
				</li>
			</ul>
			<a class="close-link">
				<i class="fa fa-times"></i>
			</a>
		</div>
	</div>
    
    <div class="ibox-content">
	<sys:message content="${message}"/>
	
	<!-- 查询条件 -->
	<div class="row">
	<div class="col-sm-12">
	<form:form id="searchForm" modelAttribute="organization" action="" method="post" class="form-inline">
		<input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
		<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
		<div class="form-group">
			<span>组织名称：&nbsp;</span>
			<form:input autocomplete="off" path="name" cssStyle="cursor: pointer" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
            <span>组织类型：&nbsp;</span>
            <form:select path="type" class="form-control required m-b">
                <form:option value="" label="请选择"/>
                <form:options items="${fns:getDictList('qqc_organization_type')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
            </form:select>
		</div>
	</form:form>
	<br/>
	</div>
	</div>
	
	
	<!-- 工具栏 -->
	<div class="row">
	<div class="col-sm-12">
		<div class="pull-left">
			 <shiro:hasPermission name="organization:add">
                 <table:addRow width="1000px" height="700px" url="${ctx}/organization/form" title="添加"></table:addRow><!-- 增加按钮 -->
             </shiro:hasPermission>
             <shiro:hasPermission name="organization:del">
                 <table:delRow url="${ctx}/organization/deleteAll" id="capitalTable"></table:delRow><!-- 删除按钮 -->
             </shiro:hasPermission>
		</div>

		<div class="pull-right">
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
			<button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
		</div>
	</div>
	</div>
	
	
	
	
	<table id="organizationTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
		<thead>
			<tr>
				<th> <input type="checkbox" class="i-checks"></th>
				<th>组织名称</th>
                <th>组织类型</th>
				<th>所属地区</th>
				<th>具体地址</th>
				<th>创建时间</th>
				<th>负责人姓名</th>
				<th>负责人电话</th>
				<th>联系人姓名</th>
				<th>联系人电话</th>
				<th>固定电话</th>
				<th>操作</th>

			</tr>
		</thead>
		<tbody>
		<c:forEach items="${page.list}" var="organization">
			<tr>
				<td> <input id="${organization.id}" name="${organization.name}"  type="checkbox" class="i-checks"></td>
				
				<td><a title="${organization.name}" href="#" onclick="openDialogView('查看组织信息', '${ctx}/organization/form?id=${organization.id}','1000px', '700px')">
						${fns:abbr(organization.name,30)}
				</a></td>
                <td>
                        ${fns:getDictLabel(organization.type, 'qqc_organization_type', '')}
                </td>
				<td>
					${organization.area.name }
				</td>
				<td>
					${organization.address}
				</td>
				<td>
					<fmt:formatDate value='${organization.createDate}' pattern='yyyy-MM-dd'/>
				</td>
				<td>
					${organization.headName}
				</td>
				<td>
					${organization.headPhone}
				</td>
				<td>
					${organization.contactName}
				</td>
				<td>
					${organization.contactPhone}
				</td>
				<td>
					${organization.telephone}
				</td>
				<td>
				  <shiro:hasPermission name="organization:view">
                      <a title="查看组织" href="#" onclick="openDialogView('查看组织', '${ctx}/organization/form?id=${organization.id}','1000px', '700px')" class="btn btn-info btn-xs btn-circle" ><i class="fa fa-search-plus"></i></a>
                  </shiro:hasPermission>
                  <shiro:hasPermission name="organization:edit">
                      <a title="修改组织" href="#" onclick="openDialog('修改组织', '${ctx}/organization/form?id=${organization.id}','1000px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
                  </shiro:hasPermission>
                  <shiro:hasPermission name="organization:del">
                      <a title="删除组织" href="${ctx}/organization/delete?id=${organization.id}" onclick="return confirmx('确认要删除该组织吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
                  </shiro:hasPermission>
				</td>
			</tr>
		</c:forEach>
		</tbody>
	</table>
	<!-- 分页代码 -->
	<table:page page="${page}"></table:page>
	<br/>
	<br/>
	</div>
	</div>
</div>
</body>
<script>

</script>
</html>
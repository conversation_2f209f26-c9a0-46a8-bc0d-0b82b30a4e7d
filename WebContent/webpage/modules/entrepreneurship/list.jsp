<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
    <title>“浙江青年创业奖”申报表列表</title>
    <meta name="decorator" content="default"/>
    <style type="text/css">
        .btn-default{
            color: #333 !important;
            background-color: #fff !important;
            border-color: #ccc !important;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function(){
            $('#activeTable thead tr th input.i-checks').on('ifChecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
                $('#activeTable tbody tr td input.i-checks').iCheck('check');
                selectEnroll();
            });

            $('#activeTable thead tr th input.i-checks').on('ifUnchecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
                $('#activeTable tbody tr td input.i-checks').iCheck('uncheck');
                $("#ids", parent.document).val("");
                selectEnroll();
            });

            $('#activeTable tbody tr td input.i-checks').on('ifChecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
                selectEnroll();
            });

            $('#activeTable tbody tr td input.i-checks').on('ifUnchecked', function(event){ //ifCreated 事件应该在插件初始化之前绑定
                $("#ids", parent.document).val("");
                selectEnroll();
            });

            checked($("#ids", parent.document).val());
            /*laydate({
                elem: '#beginDate',
                format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
                festival: true, //显示节日
                istime: false, //是否开启时间选择<br/>
                isclear: true, //是否显示清空<br/>
                istoday: true, //是否显示今天<br/>
                choose: function(datas){ //选择日期完毕的回调
                    $("#beginDate").val(datas+" 00:00:00")
                }
            });
            laydate({
                elem: '#endDate',
                format: 'YYYY-MM-DD', // 分隔符可以任意定义，该例子表示只显示年月
                festival: true, //显示节日
                istime: false, //是否开启时间选择<br/>
                isclear: true, //是否显示清空<br/>
                istoday: true, //是否显示今天<br/>
                choose: function(datas){ //选择日期完毕的回调
                    $("#endDate").val(datas+" 23:59:59")
                }
            });*/

            function selectEnroll(){
                var id="";
                $("#activeTable tbody tr td input[type=checkbox]").each(function(){
                    if(true === $(this).is(':checked')){
                        if (id === ""){
                            id = $(this).attr("id");
                        }else{
                            id += ","+$(this).attr("id");
                        }
                    }
                })
                $("#ids", parent.document).val(id);
            }

            function checked(ids) {
                if (ids == undefined){
                    ids = "";
                }
                $("#activeTable tbody tr td input[type=checkbox]").each(function(){
                    if (ids.includes($(this).attr("id"))){
                        $(this).parent().addClass("checked");
                    }
                })
            }

            function selectAll() {
                $("#activeTable tbody tr td input[type=checkbox]").each(function(){
                    $(this).parent().addClass("checked");
                })

            }
        })
    </script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="ibox">
        <div class="ibox-title">
            <h5>活动信息 </h5>
            <div class="ibox-tools">
                <a class="collapse-link">
                    <i class="fa fa-chevron-up"></i>
                </a>
                <a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
                    <i class="fa fa-wrench"></i>
                </a>
                <ul class="dropdown-menu dropdown-user">
                    <li><a href="#">选项1</a>
                    </li>
                    <li><a href="#">选项2</a>
                    </li>
                </ul>
                <a class="close-link">
                    <i class="fa fa-times"></i>
                </a>
            </div>
        </div>

        <div class="ibox-content">
            <sys:message content="${message}"/>

            <!-- 查询条件 -->
            <div class="row">
                <div class="col-sm-12">
                    <form:form id="searchForm" modelAttribute="entrepreneurship" action="" method="post" class="form-inline">
                        <input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
                        <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
                        <div class="form-group">
                            <span>姓名：&nbsp;</span>
                            <form:input autocomplete="off" path="name" cssStyle="cursor: pointer" htmlEscape="false" maxlength="200"  class=" form-control input-sm"/>
                            <span>是否组织推荐：&nbsp;</span>
                            <form:select path="isRecommend"  class="form-control m-b">
                                <form:option value="" label=""/>
                                <form:options items="${fns:getDictList('yes_no')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
                            </form:select>
                        </div>
                    </form:form>
                    <br/>
                </div>
            </div>


            <!-- 工具栏 -->
            <div class="row">
                <div class="col-sm-12">
                    <div class="pull-left">
                        <c:if test="${mode == 'edit'}">
                            <table:delRow url="${ctx}/qqc/entrepreneurship/deleteAll" id="activeTable"></table:delRow><!-- 删除按钮 -->
                        </c:if>
                    </div>

                    <div class="pull-right">
                        <button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
                        <button  class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
                    </div>
                </div>
            </div>




            <table id="activeTable" class="table table-striped table-bordered  table-hover table-condensed  dataTables-example dataTable no-footer">
                <thead>
                <tr>
                    <th> <input type="checkbox" class="i-checks"></th>
                    <th>姓名</th>
                    <th>性别</th>
                    <th>民族</th>
                    <th>手机</th>
                    <th>身份证</th>
                    <th>微信号</th>
                    <th>是否为组织推荐</th>
                    <c:if test="${mode == 'edit'}">
                        <th>操作</th>
                    </c:if>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${page.list}" var="entrepreneurship">
                    <tr>
                        <td> <input id="${entrepreneurship.id}" type="checkbox" class="i-checks"></td>
                        <td><a title="${entrepreneurship.name}" href="#" onclick="openDialogView('查看活动信息', '${ctx}/qqc/entrepreneurship/form/view?id=${active.id}&addType=${active.addType}','1000px', '700px')">
                                ${fns:abbr(entrepreneurship.name,30)}
                        </a></td>
                        <td>
                                ${fns:getDictLabel(entrepreneurship.sex, 'sex', '')}
                        </td>
                        <td>
                                ${fns:abbr(entrepreneurship.nation,50)}
                        </td>
                        <td>
                                ${fns:abbr(entrepreneurship.phone,50)}
                        </td>
                        <td>
                                ${fns:abbr(entrepreneurship.identity,50)}
                        </td>
                        <td>
                                ${entrepreneurship.wechatNo}
                        </td>
                        <td>
                                ${fns:getDictLabel(entrepreneurship.isRecommend, 'yes_no', '')}
                        </td>
                        <c:if test="${mode == 'edit'}">
                            <td>
                                <a title="修改信息" href="#" onclick="openDialog('修改信息', '${ctx}/qqc/entrepreneurship/form/edit?id=${entrepreneurship.id}','1000px', '700px')" class="btn btn-success btn-xs btn-circle" ><i class="fa fa-edit"></i></a>
                                <a title="删除信息" href="${ctx}/qqc/entrepreneurship/delete?id=${entrepreneurship.id}" onclick="return confirmx('确认要删除该信息吗？', this.href)"   class="btn btn-danger btn-xs btn-circle"><i class="fa fa-trash"></i></a>
                            </td>
                        </c:if>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
            <!-- 分页代码 -->
            <table:page page="${page}"></table:page>
            <br/>
            <br/>
        </div>
    </div>
</div>
</body>
<script>

</script>
</html>
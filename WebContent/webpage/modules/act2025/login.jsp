<%@ taglib prefix="from" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>赛事报名系统 - 登录</title>
  <c:set var="ctxStatic" value="${pageContext.request.contextPath}/static"/>
  <c:set var="ctx" value="${pageContext.request.contextPath}"/>
  <link href="${ctxStatic}/act2025/css/all.css" rel="stylesheet">
  <link href="${ctxStatic}/act2025/css/local.google.fonts.css" rel="stylesheet">
  <script src="${ctxStatic}/act2025/js/taiwind3.4.16.js"></script>
  <script src="${ctxStatic}/act2025/js/jquery-3.7.1.min.js"></script>


  <!-- Tailwind 配置 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#FF7D00',
            neutral: {
              100: '#F5F7FA',
              200: '#E5E6EB',
              300: '#C9CDD4',
              400: '#86909C',
              500: '#4E5969',
              600: '#272E3B',
              700: '#1D2129',
            }
          },
          fontFamily: {
            inter: ['Inter', 'sans-serif'],
          },
        },
      }
    }
  </script>

  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .form-input-focus {
        @apply ring-2 ring-primary/30 border-primary transition-all duration-200;
      }
      .form-input-error {
        @apply ring-2 ring-red-300 border-red-300;
      }
      .form-label {
        @apply block text-sm font-medium text-neutral-600 mb-1;
      }
      .form-field {
        @apply w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:form-input-focus;
      }
      .btn-primary {
        @apply bg-primary hover:bg-primary/90 text-white font-medium py-2 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-primary/30;
      }
      .btn-secondary {
        @apply bg-white border border-neutral-200 hover:border-neutral-300 text-neutral-600 font-medium py-2 px-6 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-neutral-200;
      }
      .btn-outline {
        @apply border border-neutral-200 hover:border-primary text-neutral-600 hover:text-primary font-medium py-1.5 px-3 rounded-lg transition-all duration-200;
      }
      .btn-disabled {
        @apply bg-neutral-200 text-neutral-400 cursor-not-allowed;
      }
      .error-message {
        @apply text-red-500 text-xs mt-1 hidden;
      }
      .card-hover {
        @apply transition-all duration-300 hover:shadow-md hover:-translate-y-1;
      }
    }
  </style>
</head>
<body class="font-inter bg-neutral-100 min-h-screen flex items-center justify-center p-4">

  <div class="w-full max-w-md">
    <!-- 登录卡片 -->
    <div class="bg-white rounded-xl shadow-sm p-6 md:p-8">
      <div class="text-center mb-6">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 text-primary mb-4">
          <i class="fa fa-user-circle text-3xl"></i>
        </div>
        <h2 class="text-xl font-bold text-neutral-700 mb-2">赛事报名系统</h2>
        <p class="text-neutral-500 text-sm">请登录查看您的报名状态</p>
      </div>

      <!-- 登录表单 -->
      <form id="loginForm" action="${ctx}/apiAct2025/doLogin" method="post">
        <!-- 错误提示 -->
        <div id="errorAlert" class="hidden mb-4 p-3 bg-red-50 text-red-600 rounded-lg flex items-center">
          <i class="fa fa-exclamation-circle mr-2"></i>
          <span id="errorMessage">请输入有效的手机号</span>
        </div>

        <div class="mb-4">
          <label for="loginMobile"  class="form-label">手机号 <span class="text-red-500">*</span></label>
          <input type="tel" id="loginMobile" name="mobile" class="form-field" placeholder="请输入手机号" required>
          <p id="loginMobileError" class="error-message">请输入有效的手机号</p>
        </div>

        <div class="mb-4">
          <label for="captcha" class="form-label">图片验证码 <span class="text-red-500">*</span></label>
          <div class="flex gap-3">
            <input type="text" id="captcha"  name="captchaCode" class="form-field flex-1" placeholder="请输入验证码" required>
            <div id="captchaImage" class="w-32 h-10 bg-neutral-100 rounded-lg flex items-center justify-center text-neutral-400 cursor-pointer">
              <img src="${ctx}/apiAct2025/generateCaptcha" alt="验证码" width="120" height="40" onclick="refreshCaptcha()">
            </div>
          </div>
          <p id="captchaError" class="error-message">请输入正确的验证码</p>
        </div>

        <div class="mb-6">
          <label for="smsCode" class="form-label">短信验证码 <span class="text-red-500">*</span></label>
          <div class="flex gap-3">
            <input type="text" id="smsCode"  name="smsCode" class="form-field flex-1" placeholder="请输入短信验证码" required>
            <button type="button" id="sendSmsBtn" class="btn-outline whitespace-nowrap">
              获取验证码
            </button>
          </div>
          <p id="smsCodeError" class="error-message">请输入正确的短信验证码</p>
        </div>

        <button type="submit" class="btn-primary w-full flex justify-center">
          <i class="fa fa-sign-in mr-2"></i> 登录
        </button>
      </form>

    </div>

    <!-- 页脚 -->
    <div class="mt-6 text-center text-xs text-neutral-400">
      <p>© 2025 赛事报名系统 版权所有</p>
    </div>
  </div>

  <script>
    // 手机号正则表达式（中国大陆）
    const mobileRegex = /^1[3-9]\d{9}$/;
    let smsCountdown = 60;
    let smsTimer = null;

    function refreshCaptcha() {
      $('#captchaImage img').attr('src', '${ctx}/apiAct2025/generateCaptcha?timestamp=' + new Date().getTime());
    }

    // 手机号验证
    function validateLoginMobile() {
      const input = $('#loginMobile');
      const value = input.val().trim();
      const isValid = mobileRegex.test(value);
      const errorElement = $('#loginMobileError');

      if (!value) {
        input.addClass('form-input-error');
        errorElement.removeClass('hidden').text('请输入手机号');
        return false;
      }

      if (value && !isValid) {
        input.addClass('form-input-error');
        errorElement.removeClass('hidden').text('请输入有效的手机号');
        return false;
      } else {
        input.removeClass('form-input-error');
        errorElement.addClass('hidden');
        return true;
      }
    }

    let captchaVerifyTimer = null;
    let lastCaptchaValue = '';
    let cachedCaptchaResult = {};

    function validateCaptcha(callBack) {
      const input = $('#captcha');
      const value = input.val().trim();
      const errorElement = $('#captchaError');

      if (!value) {
        input.addClass('form-input-error');
        errorElement.removeClass('hidden').text('请输入验证码');
        return callBack(false);
      }

      // 防止重复请求相同值
      if (value === lastCaptchaValue && cachedCaptchaResult[value] !== undefined) {
        if (cachedCaptchaResult[value]) {
          input.removeClass('form-input-error');
          errorElement.addClass('hidden');
          return callBack(true);
        } else {
          input.addClass('form-input-error');
          errorElement.removeClass('hidden').text('验证码错误');
          return callBack(false);
        }
      }

      // 节流：延迟发送请求，减少频繁调用
      if (captchaVerifyTimer) clearTimeout(captchaVerifyTimer);
      captchaVerifyTimer = setTimeout(() => {
        $.ajax({
          url: '${ctx}/apiAct2025/verifyCaptcha',
          type: 'POST',
          data: {
            captchaCode: value
          },
          dataType: 'json',
          success: function(res) {
            lastCaptchaValue = value;
            cachedCaptchaResult[value] = res.success;
            captchaCache[value] = res.success;
            if (res.success) {
              input.removeClass('form-input-error');
              errorElement.addClass('hidden');
               callBack(true);
            } else {
              input.addClass('form-input-error');
              errorElement.removeClass('hidden').text(res.msg);
               callBack(false);
            }
          },
          error: function(xhr, status, error) {
            console.error('验证码验证请求失败:', error);
            showError('验证码验证请求失败');
            callBack(false);
          }
        });
      }, 300); // 延迟 300ms 发送请求

      return false; // 异步验证无法立即返回结果
    }

    const captchaCache = {};
    function verifyCaptchaLocally(code) {
      if (captchaCache[code]) {
        return captchaCache[code];
      }
      return null;
    }


    function sendSmsCode() {
      // 验证手机号和图片验证码是否填写正确
      if (!validateLoginMobile()) {
        $('#loginMobile').focus();
        return;
      }

      // 异步验证验证码，并传入回调函数
      validateCaptcha(function(captchaValid) {
        if (!captchaValid) {
          $('#captcha').focus();
          return;
        }
      });

      const btn = $('#sendSmsBtn');
      const mobile = $('#loginMobile').val();
      const captchaCode = $('#captcha').val();

      // 检查是否已禁用（防止重复点击）
      if (btn.hasClass('btn-disabled')) {
        return;
      }

      // 清除可能存在的旧定时器
      if (smsTimer) {
        clearInterval(smsTimer);
        smsTimer = null;
      }

      // 初始化倒计时
      smsCountdown = 60;
      btn.addClass('btn-disabled');
      btn.text("重新发送(" + smsCountdown + "s)");

      smsTimer = setInterval(() => {
        smsCountdown--;
        btn.text("重新发送(" + smsCountdown + "s)");

        if (smsCountdown <= 0) {
          clearInterval(smsTimer);
          smsTimer = null;
          btn.removeClass('btn-disabled');
          btn.text('获取验证码');
        }
      }, 1000);

      // 发送请求获取短信验证码
      $.ajax({
        url: '${ctx}/apiAct2025/sendSmsCode',
        type: 'POST',
        data: {
          mobile: mobile,
          captchaCode: captchaCode
        },
        dataType: 'json',
        success: function(res) {
          if (res.success) {
            // 已由上面处理倒计时，无需再改按钮文字
          } else {
            showError(res.msg); // 显示错误信息
            btn.removeClass('btn-disabled');
            refreshCaptcha(); // 刷新验证码
            if (smsTimer) {
              clearInterval(smsTimer);
              smsTimer = null;
              btn.text('获取验证码');
            }
          }
        },
        error: function(xhr, status, error) {
          console.error('发送短信验证码失败:', error);
          showError('发送短信验证码失败，请稍后重试');
          btn.removeClass('btn-disabled');
          refreshCaptcha();
          if (smsTimer) {
            clearInterval(smsTimer);
            smsTimer = null;
            btn.text('获取验证码');
          }
        }
      });
    }




    // 表单提交验证
    function validateLoginForm() {
      let isValid = true;

      if (!validateLoginMobile()) {
        isValid = false;
      }

      if (!validateCaptcha()) {
        isValid = false;
      }

      // 验证短信验证码
      const smsInput = $('#smsCode');
      const smsValue = smsInput.val().trim();
      const smsError = $('#smsCodeError');

      if (!smsValue) {
        smsInput.addClass('form-input-error');
        smsError.removeClass('hidden').text('请输入短信验证码');
        isValid = false;
      } else {
        smsInput.removeClass('form-input-error');
        smsError.addClass('hidden');
      }

      return isValid;
    }

    // 绑定事件
    $(document).ready(function() {
      // 初始化验证码
      refreshCaptcha();



      // 检查登录状态
      <%--$.ajax({--%>
      <%--  url: '${ctx}/apiAct2025/checkLogin',--%>
      <%--  type: 'GET',--%>
      <%--  dataType: 'json',--%>
      <%--  success: function(res) {--%>
      <%--    if (res.success) {--%>
      <%--      // 已登录，跳转到表单状态页面--%>
      <%--      window.location.href = '${ctx}/apiAct2025/formStatus';--%>
      <%--    }--%>
      <%--  }--%>
      <%--});--%>


      // 手机号验证
      $('#loginMobile').on('input blur', validateLoginMobile);

      $('#sendSmsBtn').on('click', function() {
        sendSmsCode();
      });

      // 验证码验证
      $('#captcha').on('blur', function () {
        const value = $(this).val().trim();
        if (!value) return;

        // 如果已有缓存结果，则跳过请求
        if (verifyCaptchaLocally(value)) {
          $('#captcha').removeClass('form-input-error');
          $('#captchaError').addClass('hidden');
          return;
        }

        // 延迟发送请求，防止频繁触发
        if (captchaVerifyTimer) clearTimeout(captchaVerifyTimer);
        captchaVerifyTimer = setTimeout(() => {
          validateCaptcha();
        }, 200);
      });

      // 点击刷新验证码图片时不立即验证，只刷新
      $('#captchaImage').on('click', function () {
        refreshCaptcha(); // 刷新验证码图片
        $('#captcha').val(''); // 清空验证码输入框
        $('#captcha').removeClass('form-input-error');
        $('#captchaError').addClass('hidden');
      });


      // 页面加载时检查是否有错误信息并显示
      const backendError = "${error}"; // 从模型中获取后端传递的错误信息
      if (backendError) {
        // const decodedError = decodeURIComponent(backendError);
        showError(backendError);
        // 清除 URL 中的 error 参数
        // const cleanUrl = window.location.origin + window.location.pathname;
        // window.history.replaceState({}, document.title, cleanUrl);
      }


      // 表单提交
      <%--$('#loginForm').on('submit', function(e) {--%>
      <%--  e.preventDefault();--%>
      <%--  --%>
      <%--  if (validateLoginForm()) {--%>
      <%--    const mobile = $('#loginMobile').val();--%>
      <%--    const smsCode = $('#smsCode').val();--%>
      <%--    --%>
      <%--    $.ajax({--%>
      <%--      url: '${ctx}/apiAct2025/doLogin',--%>
      <%--      type: 'POST',--%>
      <%--      data: {--%>
      <%--        mobile: mobile,--%>
      <%--        smsCode: smsCode--%>
      <%--      },--%>
      <%--      dataType: 'json',--%>
      <%--      success: function(res) {--%>
      <%--        if (res.success) {--%>
      <%--          // 登录成功，跳转到表单状态页面--%>
      <%--          window.location.href = '${ctx}/apiAct2025/formStatus';--%>
      <%--        } else {--%>
      <%--          alert(res.msg);--%>
      <%--          // 刷新验证码--%>
      <%--          refreshCaptcha();--%>
      <%--        }--%>
      <%--      },--%>
      <%--      error: function(xhr, status, error) {--%>
      <%--        console.error('登录失败:', error);--%>
      <%--        alert('登录失败，请稍后重试');--%>
      <%--      }--%>
      <%--    });--%>
      <%--  }--%>
      <%--});--%>
    });

    function showError(message) {
      const errorAlert = $('#errorAlert');
      const errorMessage = $('#errorMessage');

      if (message && message.trim()) {
        errorMessage.text(message);
        errorAlert.removeClass('hidden');
      } else {
        errorAlert.addClass('hidden');
      }
    }

  </script>
</body>
</html>
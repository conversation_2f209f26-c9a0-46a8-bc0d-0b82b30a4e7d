<%@ taglib prefix="from" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%
  response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate"); // HTTP 1.1
  response.setHeader("Pragma", "no-cache"); // HTTP 1.0
  response.setDateHeader("Expires", 0); // Proxies
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>赛事报名系统</title>
  <c:set var="ctxStatic" value="${pageContext.request.contextPath}/static"/>
  <c:set var="ctx" value="${pageContext.request.contextPath}"/>
  <link href="${ctxStatic}/act2025/css/all.css" rel="stylesheet">
  <link href="${ctxStatic}/act2025/css/local.google.fonts.css" rel="stylesheet">
  <script src="${ctxStatic}/act2025/js/taiwind3.4.16.js"></script>
  <script src="${ctxStatic}/act2025/js/jquery-3.7.1.min.js"></script>
  <script>
    // 直接在HTML中定义Tailwind配置对象
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#FF7D00',
            neutral: {
              100: '#F5F7FA',
              200: '#E5E6EB',
              300: '#C9CDD4',
              400: '#86909C',
              500: '#4E5969',
              600: '#272E3B',
              700: '#1D2129',
            },
            status: {
              pending: '#FF7D00',
              approved: '#00B42A',
              rejected: '#F53F3F',
              processing: '#165DFF'
            }
          },
          fontFamily: {
            inter: ['Inter', 'sans-serif'],
          },
        },
      }
    }
  </script>

  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .form-input-focus {
        @apply ring-2 ring-primary/30 border-primary transition-all duration-200;
      }
      .form-input-error {
        @apply ring-2 ring-red-300 border-red-300;
      }
      .form-label {
        @apply block text-sm font-medium text-neutral-600 mb-1;
      }
      .form-field {
        @apply w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:form-input-focus;
      }
      .btn-primary {
        @apply bg-primary hover:bg-primary/90 text-white font-medium py-2 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-primary/30;
      }
      .btn-secondary {
        @apply bg-white border border-neutral-200 hover:border-neutral-300 text-neutral-600 font-medium py-2 px-6 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-neutral-200;
      }
      .btn-outline {
        @apply border border-neutral-200 hover:border-primary text-neutral-600 hover:text-primary font-medium py-1.5 px-3 rounded-lg transition-all duration-200;
      }
      .btn-draft {
        @apply bg-neutral-100 border border-neutral-200 hover:bg-neutral-200 text-neutral-700 font-medium py-2 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-neutral-200;
      }
      .btn-secondary {
        @apply bg-white text-primary border border-primary hover:bg-gray-100;
      }
      .form-section {
        @apply bg-white rounded-xl shadow-sm p-5 mb-6 transition-all duration-300;
      }
      .form-section:hover {
        @apply shadow-md;
      }
      .form-grid {
        @apply grid grid-cols-1 md:grid-cols-2 gap-4;
      }
      .mobile-label {
        @apply text-sm text-neutral-400 block;
      }
      .error-message {
        @apply text-red-500 text-xs mt-1 hidden;
      }
      .card-hover {
        @apply transition-all duration-300 hover:shadow-md hover:-translate-y-1;
      }
      .status-pending {
        @apply bg-status-pending/10 text-status-pending;
      }
      .status-approved {
        @apply bg-status-approved/10 text-status-approved;
      }
      .status-rejected {
        @apply bg-status-rejected/10 text-status-rejected;
      }
      .status-processing {
        @apply bg-status-processing/10 text-status-processing;
      }
      .member-card {
        @apply border border-neutral-200 rounded-lg p-4 mb-4 bg-white shadow-sm transition-all duration-300 hover:shadow-md;
      }
      .member-header {
        @apply flex justify-between items-center mb-3 pb-2 border-b border-neutral-100;
      }
      .member-title {
        @apply font-medium text-neutral-700;
      }
      .remove-btn {
        @apply text-red-500 hover:text-red-600 transition-colors duration-200;
      }
      .add-btn {
        @apply bg-primary/10 text-primary hover:bg-primary/20 transition-colors duration-200 px-3 py-1.5 rounded-lg font-medium;
      }
      .form-grid-3 {
        @apply grid grid-cols-1 md:grid-cols-3 gap-3;
      }
      .form-grid-4 {
        @apply grid grid-cols-1 md:grid-cols-4 gap-3;
      }
      .upload-area {
        @apply relative border-2 border-dashed border-neutral-200 rounded-lg p-4 text-center hover:border-primary transition-colors duration-200;
      }
      .upload-input {
        @apply absolute inset-0 w-full h-full opacity-0 cursor-pointer;
      }
    }
  </style>

</head>
<body class="font-inter bg-neutral-100 min-h-screen">
<div class="container mx-auto px-4 py-8 max-w-5xl">
  <!-- 表单头部 -->
  <div class="text-center mb-8">
    <h1 class="text-[clamp(1.75rem,4vw,2.5rem)] font-bold text-neutral-700 mb-2">赛事报名系统</h1>
    <p class="text-neutral-500 max-w-2xl mx-auto">请填写以下信息完成报名，所有带 <span class="text-red-500">*</span> 的字段为必填项</p>
  </div>

  <!-- 主表单 -->
  <form id="competitionForm" class="bg-white rounded-xl shadow-md p-6 md:p-8 mb-8">
    <input type="hidden" id="hdId" name="hdId" value="${activityId}"/>
    <input type="hidden" id="userId" name="userId" value="${userId}"/>
    <input type="hidden" id="id" name="id" value="${id}"/>

    <!-- 基础信息 -->
    <div class="form-section">
      <h2 class="text-lg font-semibold text-neutral-700 mb-4 flex items-center">
        <i class="fa fa-user-circle text-primary mr-2"></i> 基础信息
      </h2>
      <div class="form-grid">
        <div>
          <label for="participantProject" class="form-label">参赛项目 <span class="text-red-500">*</span></label>
          <input type="text" id="participantProject" value="${reg.participantProject}" name="participantProject" class="form-field" required>
        </div>
        <div>
          <label for="registrationChannel" class="form-label">报名途径 <span class="text-red-500">*</span></label>
          <select id="registrationChannel" name="registrationChannel"  class="form-field" required>
            <option value="">请选择</option>
            <c:forEach items="${registrationChannel}" var="item">
              <option value="${item.value}">${item.label}</option>
            </c:forEach>
<%--            <option value="官网">地市推荐</option>--%>
<%--            <option value="公众号">大学通道</option>--%>
<%--            <option value="线下推荐">自主报名</option>--%>
          </select>
        </div>
        <div>
          <label for="competitionCity" class="form-label">参赛地区（市） <span class="text-red-500">*</span></label>
          <select id="competitionCity" name="competitionCity" class="form-field" required>
            <option value="">请选择市</option>
<%--              <c:forEach items="${cities}" var="item">--%>
<%--                <option value="${item.code}">${item.name}</option>--%>
<%--              </c:forEach>--%>
            <option value="330100">杭州市</option>
            <option value="330200">宁波市</option>
            <option value="330300">温州市</option>
            <option value="330400">嘉兴市</option>
            <option value="330500">湖州市</option>
            <option value="330600">绍兴市</option>
            <option value="330700">金华市</option>
            <option value="330800">衢州市</option>
            <option value="330900">舟山市</option>
            <option value="331000">台州市</option>
            <option value="331100">丽水市</option>
          </select>
        </div>
        <div>
          <label for="competitionDistrict" class="form-label">参赛地区（区县） <span class="text-red-500">*</span></label>
          <select id="competitionDistrict" name="competitionDistrict" class="form-field" required>
            <option value="">请先选择市</option>
          </select>
        </div>
        <div>
          <label for="competitionGroup" class="form-label">赛事分组 <span class="text-red-500">*</span></label>
          <select id="competitionGroup" name="competitionGroup" class="form-field" required>
            <option value="">请选择</option>
            <c:forEach items="${competitionGroup}" var="item">
              <option value="${item.value}">${item.label}</option>
            </c:forEach>
          </select>
        </div>
        <div>
          <label for="competitionSubgroup" class="form-label">项目分组 <span class="text-red-500">*</span></label>
          <select id="competitionSubgroup" name="competitionSubgroup" class="form-field" required>
            <option value="">请选择</option>
            <c:forEach items="${competitionSubgroup}" var="item">
              <option value="${item.value}">${item.label}</option>
            </c:forEach>
          </select>
        </div>

        <!-- 新增的项目分组子项下拉框，默认隐藏 -->
        <div id="ruralSubgroup" style="display: none;">
          <label for="ruralCompetitionSubgroup" class="form-label">乡村振兴项目分组 <span class="text-red-500">*</span></label>
          <select id="ruralCompetitionSubgroup" name="ruralCompetitionSubgroup" class="form-field" required>
            <option value="">请选择</option>
            <c:forEach items="${ruralSubgroup}" var="item">
              <option value="${item.value}">${item.label}</option>
            </c:forEach>
<%--            <option value="ruralIndustry">农村青年产业项目</option>--%>
<%--            <option value="ruralGovernance">农村青年治理项目</option>--%>
<%--            <option value="ruralService">农业农村服务项目</option>--%>
          </select>
        </div>
        <!-- 新增项目领域字段，横跨整列 -->
        <div class="md:col-span-2">
          <label for="projectField" class="form-label">项目领域 <span class="text-red-500">*</span></label>
          <select id="projectField" name="projectField" class="form-field" required>
            <option value="">请选择</option>
            <c:forEach items="${projectField}" var="item">
              <option value="${item.value}">${item.label}</option>
            </c:forEach>
<%--            <option value="信息传输、软件和信息技术服务业">信息传输、软件和信息技术服务业</option>--%>
<%--            <option value="批发和零售业">批发和零售业</option>--%>
<%--            <option value="卫生和社会工作">卫生和社会工作</option>--%>
<%--            <option value="制造业">制造业</option>--%>
<%--            <option value="农、林、牧、渔业">农、林、牧、渔业</option>--%>
<%--            <option value="文化、体育和娱乐业">文化、体育和娱乐业</option>--%>
<%--            <option value="科学研究和技术服务业">科学研究和技术服务业</option>--%>
<%--            <option value="电力、热力、燃气及水生产和供应业">电力、热力、燃气及水生产和供应业</option>--%>
<%--            <option value="居民服务、修理和其他服务业">居民服务、修理和其他服务业</option>--%>
<%--            <option value="教育">教育</option>--%>
<%--            <option value="住宿和餐饮业">住宿和餐饮业</option>--%>
<%--            <option value="采矿业">采矿业</option>--%>
          </select>
        </div>

      </div>
    </div>
    <!-- 申报人信息 -->
    <div class="form-section">
      <h2 class="text-lg font-semibold text-neutral-700 mb-4 flex items-center">
        <i class="fa fa-id-card text-primary mr-2"></i> 申报人信息
      </h2>
      <h3 class="text-md font-semibold text-neutral-700 mb-3">第一申报人</h3>
      <div class="form-grid">
        <div>
          <label for="firstApplicantName" class="form-label">姓名 <span class="text-red-500">*</span></label>
          <input type="text" id="firstApplicantName" name="firstApplicantName" class="form-field" required>
        </div>
        <div>
          <label for="firstApplicantMobile" class="form-label">手机号 <span class="text-red-500">*</span></label>
          <input type="tel" id="firstApplicantMobile" name="firstApplicantMobile" class="form-field" required>
          <p id="mobileError" class="error-message">请输入有效的手机号</p>
        </div>
        <div>
          <label for="firstApplicantEmail" class="form-label">邮箱 <span class="text-red-500">*</span></label>
          <input type="email" id="firstApplicantEmail" name="firstApplicantEmail" class="form-field" required>
        </div>
        <div>
          <label for="firstApplicantHukou" class="form-label">户籍 <span class="text-red-500">*</span></label>
          <input type="text" id="firstApplicantHukou" name="firstApplicantHukou" class="form-field" required>
        </div>

        <div>
          <label for="firstApplicantPosition" class="form-label">所任职位 <span class="text-red-500">*</span></label>
          <input type="text" id="firstApplicantPosition" name="firstApplicantPosition" class="form-field" required>
        </div>
        <div>
          <label for="firstApplicantGender" class="form-label">性别 <span class="text-red-500">*</span></label>
          <div class="flex space-x-4 pt-2" id="firstApplicantGender">
            <label class="flex items-center cursor-pointer">
              <input type="radio" name="firstApplicantGender" value="1" class="form-radio text-primary focus:ring-primary" required>
              <span class="ml-2">男</span>
            </label>
            <label class="flex items-center cursor-pointer">
              <input type="radio" name="firstApplicantGender" value="2" class="form-radio text-primary focus:ring-primary">
              <span class="ml-2">女</span>
            </label>
          </div>
        </div>
        <div>
          <label for="firstApplicantBirthday" class="form-label">出生年月日 <span class="text-red-500">*</span></label>
          <input type="date" id="firstApplicantBirthday" name="firstApplicantBirthday" class="form-field" required>
        </div>
        <div>
          <label for="firstApplicantGraduationTime" class="form-label">毕业时间</label>
          <input type="date" id="firstApplicantGraduationTime" name="firstApplicantGraduationTime" class="form-field">
        </div>

        <div class="md:col-span-2">
          <label for="firstApplicantIdCard" class="form-label">身份证号 <span class="text-red-500">*</span></label>
          <input type="text" id="firstApplicantIdCard" name="firstApplicantIdCard" class="form-field" required>

        </div>

      </div>

      <div class="mt-4">
        <label for="firstApplicantIdCardFrontFile" class="form-label">身份证正面（国徽面）上传 <span class="text-red-500">*</span></label>
        <div class="upload-area relative border-2 border-dashed border-neutral-200 rounded-lg p-4 text-center hover:border-primary transition-colors duration-200">
          <input type="file" id="firstApplicantIdCardFrontFile"  class="absolute inset-0 w-full h-full opacity-0 cursor-pointer" accept=".jpg,.jpeg,.png"  />
          <input type="hidden" id="firstApplicantIdCardFrontFilePath" name="firstApplicantIdCardFrontFile" value="" required/>
          <!-- 新增错误提示 -->
          <p id="firstApplicantIdCardFrontError" class="error-message text-red-500 text-xs mt-1 hidden">请上传身份证正面文件</p>
          <div class="py-4">
            <i class="fa fa-cloud-upload text-3xl text-neutral-300 mb-2"></i>
            <p class="text-neutral-400">点击或拖拽文件到此处上传身份证正面（国徽面）</p>
            <p class="text-xs text-neutral-400 mt-1">支持 JPG、PNG 格式，不超过20MB</p>
          </div>
        </div>
        <div id="frontFilePreview" class="hidden mt-3">
          <img id="frontPreviewImage" src="" alt="身份证正面预览" class="max-h-40 rounded-lg border border-neutral-200">
          <button type="button" onclick="removePreview(this)"  id="removeFrontFile" class="mt-2 text-red-500 text-sm flex items-center">
            <i class="fa fa-times-circle mr-1"></i> 移除文件
          </button>
        </div>
        <p id="idCardError" class="error-message">请输入有效的身份证号</p>
      </div>

      <div class="mt-4">
        <label for="firstApplicantIdCardBackFile" class="form-label">身份证反面（持证人照片、个人信息所在面）上传 <span class="text-red-500">*</span></label>
        <div class="relative border-2 border-dashed border-neutral-200 rounded-lg p-4 text-center hover:border-primary transition-colors duration-200">
          <input type="file" id="firstApplicantIdCardBackFile"  class="absolute inset-0 w-full h-full opacity-0 cursor-pointer" accept=".jpg,.jpeg,.png" >
          <input type="hidden" id="firstApplicantIdCardBackFilePath" name="firstApplicantIdCardBackFile" required>
          <!-- 新增错误提示 -->
          <p id="firstApplicantIdCardBackError" class="error-message text-red-500 text-xs mt-1 hidden">请上传身份证反面文件</p>
          <div class="py-4">
            <i class="fa fa-cloud-upload text-3xl text-neutral-300 mb-2"></i>
            <p class="text-neutral-400">点击或拖拽文件到此处上传身份证反面（持证人照片、个人信息所在面）</p>
            <p class="text-xs text-neutral-400 mt-1">支持 JPG、PNG 格式，不超过20MB</p>
          </div>
        </div>
        <div id="backFilePreview" class="hidden mt-3">
          <img id="backPreviewImage" src="" alt="身份证反面预览" class="max-h-40 rounded-lg border border-neutral-200">
          <button type="button" id="removeBackFile" onclick="removePreview(this)" class="mt-2 text-red-500 text-sm flex items-center">
            <i class="fa fa-times-circle mr-1"></i> 移除文件
          </button>
        </div>

      </div>

      <!-- 项目团队成员 -->
      <h3 class="text-md font-semibold text-neutral-700 mt-6 mb-3">项目团队成员</h3>
      <div class="mb-6">
        <div class="flex justify-between items-center mb-4">
          <button type="button" id="addMember" class="add-btn flex items-center">
            <i class="fa fa-plus-circle mr-1"></i> 添加成员
          </button>
        </div>
        <input type="hidden" id="projectMembersData" value='${reg.projectMembers}' />
        <div id="projectMembers" class="space-y-4">
          <!-- 成员模板，将被JS复制 -->
          <div class="member-row member-card template" style="display: none;">
            <div class="member-header">
              <div class="member-title">团队成员</div>
              <button type="button" class="remove-member remove-btn">
                <i class="fa fa-times-circle"></i>
              </button>
            </div>
            <div class="form-grid-4 mb-3">
              <div>
                <label class="form-label text-sm">成员姓名 <span class="text-red-500">*</span></label>
                <input type="text" class="form-field member-name" required>
              </div>
              <div>
                <label class="form-label text-sm">联系方式 <span class="text-red-500">*</span></label>
                <input type="tel" class="form-field member-contact" required>
                <p class="member-contact-error error-message">请输入有效的手机号</p>
              </div>
              <div>
                <label class="form-label text-sm">性别 <span class="text-red-500">*</span></label>
                <div class="flex space-x-4 pt-2">
                  <label class="flex items-center cursor-pointer">
                    <input type="radio" name="member-gender-0" value="1" class="form-radio text-primary focus:ring-primary" required>
                    <span class="ml-2">男</span>
                  </label>
                  <label class="flex items-center cursor-pointer">
                    <input type="radio" name="member-gender-0" value="2" class="form-radio text-primary focus:ring-primary">
                    <span class="ml-2">女</span>
                  </label>
                </div>
              </div>
              <div>
                <label class="form-label text-sm">所任职务 <span class="text-red-500">*</span></label>
                <input type="text" class="form-field member-position" required>
              </div>
            </div>
            <div class="form-grid-4 mb-3">
              <div>
                <label class="form-label text-sm">身份证号 <span class="text-red-500">*</span></label>
                <input type="text" class="form-field member-id-card" required>
                <p class="member-id-card-error error-message">请输入有效的身份证号</p>
              </div>
              <div>
                <label class="form-label text-sm">出生日期 <span class="text-red-500">*</span></label>
                <input type="date" class="form-field member-birthday" required>
              </div>
            </div>
            <div class="form-grid-2 mb-3">
              <div>
                <label class="form-label text-sm">身份证正面（国徽面）上传 <span class="text-red-500">*</span></label>
                <div class="upload-area">
                  <input type="file" class="upload-input member-id-card-front" accept=".jpg,.jpeg,.png" >
                  <input type="hidden" required/>
                  <p class="member-id-card-front-error error-message text-red-500 text-xs mt-1 hidden">请上传身份证正面文件</p>
                  <div class="py-4">
                    <i class="fa fa-cloud-upload text-2xl text-neutral-300 mb-1"></i>
                    <p class="text-neutral-400 text-sm">上传身份证正面（国徽面）</p>
                    <p class="text-xs text-neutral-400 mt-1">支持 JPG、PNG 格式</p>
                  </div>
                </div>
                <!-- 添加预览容器 -->
                <div class="member-front-file-preview hidden mt-3">
                  <img src="" alt="身份证正面预览" class=" member-front-preview-image max-h-40 rounded-lg border border-neutral-200">
                  <button type="button"  onclick="removePreview(this)"  class="member-remove-front-file mt-2 text-red-500 text-sm flex items-center">
                    <i class="fa fa-times-circle mr-1"></i> 移除文件
                  </button>
                </div>

              </div>
              <div>
                <label class="form-label text-sm">身份证反面（持证人照片、个人信息所在面）上传 <span class="text-red-500">*</span></label>
                <div class="upload-area">
                  <input type="file" class="upload-input member-id-card-back" accept=".jpg,.jpeg,.png" >
                  <input type="hidden" required/>
                  <p class="member-id-card-back-error error-message text-red-500 text-xs mt-1 hidden">请上传身份证反面文件</p>
                  <div class="py-4">
                    <i class="fa fa-cloud-upload text-2xl text-neutral-300 mb-1"></i>
                    <p class="text-neutral-400 text-sm">上传身份证反面（持证人照片、个人信息所在面）</p>
                    <p class="text-xs text-neutral-400 mt-1">支持 JPG、PNG 格式</p>
                  </div>
                </div>
                <!-- 添加预览容器 -->
                <div class="member-back-file-preview hidden mt-3">
                  <img  src="" alt="身份证反面预览" class="member-back-preview-image max-h-40 rounded-lg border border-neutral-200">
                  <button type="button" onclick="removePreview(this)" class="member-remove-back-file mt-2 text-red-500 text-sm flex items-center">
                    <i class="fa fa-times-circle mr-1"></i> 移除文件
                  </button>
                </div>

              </div>
            </div>
          </div>

          <!-- 默认添加一个成员行 -->
<%--          <div class="member-row member-card">--%>
<%--            <div class="member-header">--%>
<%--              <div class="member-title">团队成员</div>--%>
<%--              <button type="button" class="remove-member remove-btn">--%>
<%--                <i class="fa fa-times-circle"></i>--%>
<%--              </button>--%>
<%--            </div>--%>

<%--            <div class="form-grid-4 mb-3">--%>
<%--              <div>--%>
<%--                <label class="form-label text-sm">成员姓名 <span class="text-red-500">*</span></label>--%>
<%--                <input type="text" class="form-field member-name" required>--%>
<%--              </div>--%>
<%--              <div>--%>
<%--                <label class="form-label text-sm">联系方式 <span class="text-red-500">*</span></label>--%>
<%--                <input type="tel" class="form-field member-contact" required>--%>
<%--                <p class="member-contact-error error-message">请输入有效的手机号</p>--%>
<%--              </div>--%>
<%--              <div>--%>
<%--                <label class="form-label text-sm">性别 <span class="text-red-500">*</span></label>--%>
<%--                <div class="flex space-x-4 pt-2">--%>
<%--                  <label class="flex items-center cursor-pointer">--%>
<%--                    <input type="radio" name="member-gender" value="1" class="form-radio text-primary focus:ring-primary" required>--%>
<%--                    <span class="ml-2">男</span>--%>
<%--                  </label>--%>
<%--                  <label class="flex items-center cursor-pointer">--%>
<%--                    <input type="radio" name="member-gender" value="2" class="form-radio text-primary focus:ring-primary">--%>
<%--                    <span class="ml-2">女</span>--%>
<%--                  </label>--%>
<%--                </div>--%>
<%--              </div>--%>
<%--              <div>--%>
<%--                <label class="form-label text-sm">所任职务 <span class="text-red-500">*</span></label>--%>
<%--                <input type="text" class="form-field member-position" required>--%>
<%--              </div>--%>
<%--            </div>--%>

<%--            <div class="form-grid-4 mb-3">--%>
<%--              <div>--%>
<%--                <label class="form-label text-sm">身份证号 <span class="text-red-500">*</span></label>--%>
<%--                <input type="text" class="form-field member-id-card" required>--%>
<%--                <p class="member-id-card-error error-message">请输入有效的身份证号</p>--%>
<%--              </div>--%>
<%--              <div>--%>
<%--                <label class="form-label text-sm">出生日期 <span class="text-red-500">*</span></label>--%>
<%--                <input type="date" class="form-field member-birthday" required>--%>
<%--              </div>--%>
<%--            </div>--%>

<%--            <div class="form-grid-2 mb-3">--%>
<%--              <div>--%>
<%--                <label class="form-label text-sm">身份证正面（国徽面）上传 <span class="text-red-500">*</span></label>--%>
<%--                <div class="upload-area">--%>
<%--                  <input type="file" class="upload-input member-id-card-front" accept=".jpg,.jpeg,.png" required>--%>
<%--                  <div class="py-4">--%>
<%--                    <i class="fa fa-cloud-upload text-2xl text-neutral-300 mb-1"></i>--%>
<%--                    <p class="text-neutral-400 text-sm">上传身份证正面（国徽面）</p>--%>
<%--                    <p class="text-xs text-neutral-400 mt-1">支持 JPG、PNG 格式</p>--%>
<%--                  </div>--%>
<%--                </div>--%>
<%--              </div>--%>
<%--              <div>--%>
<%--                <label class="form-label text-sm">身份证反面（持证人照片、个人信息所在面）上传 <span class="text-red-500">*</span></label>--%>
<%--                <div class="upload-area">--%>
<%--                  <input type="file" class="upload-input member-id-card-back" accept=".jpg,.jpeg,.png" required>--%>
<%--                  <div class="py-4">--%>
<%--                    <i class="fa fa-cloud-upload text-2xl text-neutral-300 mb-1"></i>--%>
<%--                    <p class="text-neutral-400 text-sm">上传身份证反面（持证人照片、个人信息所在面）</p>--%>
<%--                    <p class="text-xs text-neutral-400 mt-1">支持 JPG、PNG 格式</p>--%>
<%--                  </div>--%>
<%--                </div>--%>
<%--              </div>--%>
<%--            </div>--%>
<%--          </div>--%>
        </div>
      </div>

      <!-- 指导老师 -->
      <h2 class="text-lg font-semibold text-neutral-700 mb-4 flex items-center">
        指导老师（选填）<span class="text-xs text-neutral-400">创新创业导师不受年龄限制，不得作为项目成员参加比赛</span>
      </h2>
      <button type="button" id="addMentor" class="add-btn flex items-center">
        <i class="fa fa-plus-circle mr-1"></i> 添加导师
      </button>
      <input type="hidden" id="mentorsData" value='${reg.mentors}' />
      <div id="mentors" class="space-y-4 mt-4">
        <!-- 导师模板，将被JS复制 -->
        <div class="mentor-row member-card" style="display: none;">
          <div class="flex justify-between items-center mb-3">
            <h3 class="text-base font-medium text-neutral-700">导师信息</h3>
            <button type="button" class="remove-mentor remove-btn">
              <i class="fa fa-times-circle"></i>
            </button>
          </div>
          <div class="form-grid-2 mb-3">
            <div>
              <label class="form-label text-sm">导师姓名</label>
              <input type="text" class="form-field mentor-name">
            </div>
            <div>
              <label class="form-label text-sm">所任职务</label>
              <input type="text" class="form-field mentor-position">
            </div>
          </div>
        </div>
      </div>

    </div>
    <!-- 公司信息 -->
    <div class="form-section" id="companyInfo" style="display: none;">
      <h2 class="text-lg font-semibold text-neutral-700 mb-4 flex items-center">
        <i class="fa fa-building text-primary mr-2"></i> 公司信息
      </h2>
      <div class="form-grid">
        <div>
          <label for="companyName" class="form-label">公司名称 <span class="text-red-500">*</span></label>
          <input type="text" id="companyName" name="companyName" class="form-field" required>
        </div>
        <div>
          <label for="companyEstablishTime" class="form-label">成立时间 <span class="text-red-500">*</span></label>
          <input type="date" id="companyEstablishTime" name="companyEstablishTime" class="form-field" required>
        </div>
        <div class="md:col-span-2">
          <label for="companyAddress" class="form-label">公司地址 <span class="text-red-500">*</span></label>
          <input type="text" id="companyAddress" name="companyAddress" class="form-field" required>
        </div>
      </div>

      <!-- 新增营业执照上传区域 -->
      <div class="mt-4">
        <label for="companyBusinessLicenseFile" class="form-label">营业执照上传 <span class="text-red-500">*</span><span class="text-xs text-red-500 ml-1">第一申报人须与营业执照中企业法人代表一致</span></label>
        <div class="relative border-2 border-dashed border-neutral-200 rounded-lg p-4 text-center hover:border-primary transition-colors duration-200">
          <input type="file" id="companyBusinessLicenseFile"  class="absolute inset-0 w-full h-full opacity-0 cursor-pointer" accept=".jpg,.jpeg,.png" >
          <input type="hidden" name="companyBusinessLicenseFile" required>
          <p id="companyBusinessLicenseError" class="error-message text-red-500 text-xs mt-1 hidden">请上传营业执照文件</p>
          <div class="py-4">
            <i class="fa fa-file text-3xl text-neutral-300 mb-2"></i>
            <p class="text-neutral-400">点击或拖拽文件到此处上传营业执照</p>
            <p class="text-xs text-neutral-400 mt-1">支持JPG、PNG格式，不超过20MB</p>
          </div>
        </div>

        <div id="businessLicensePreview" class="hidden mt-3">
          <img id="businessLicensePreviewImage" src="" alt="营业执照预览" class="max-h-40 rounded-lg border border-neutral-200">
          <button type="button" onclick="removePreview(this)" id="removeBusinessLicenseFile" class="mt-2 text-red-500 text-sm flex items-center">
            <i class="fa fa-times-circle mr-1"></i> 移除文件
          </button>
        </div>

      </div>

    </div>


    <!-- 项目信息 -->
   <div class="form-section">
      <h2 class="text-lg font-semibold text-neutral-700 mb-4 flex items-center">
        <i class="fa fa-briefcase text-primary mr-2"></i> 项目信息
      </h2>
        <div>
          <label for="projectBrief" class="form-label">项目简介 <span class="text-red-500">*</span></label>
          <textarea id="projectBrief" name="projectBrief"  rows="3" class="form-field" maxlength="200" required placeholder="请简要描述项目，不超过200字"></textarea>
          <div class="flex justify-end mt-1">
            <span id="briefCounter" class="text-xs text-neutral-400">0/200</span>
          </div>
        </div>

        <div class="mt-4">
          <label for="industryCompetitiveAdvantage" class="form-label">同行业竞争优势 <span class="text-red-500">*</span></label>
          <textarea id="industryCompetitiveAdvantage" name="industryCompetitiveAdvantage" rows="5" class="form-field" maxlength="1000" required placeholder="请描述项目的同行业竞争优势，不超过1000字"></textarea>
          <div class="flex justify-end mt-1">
            <span id="advantageCounter" class="text-xs text-neutral-400">0/1000</span>
          </div>
        </div>

        <div>
          <label for="socialBenefits" class="form-label">社会效益</label>
          <textarea id="socialBenefits" name="socialBenefits" rows="4" class="form-field" maxlength="1000" placeholder="请描述项目的社会效益"></textarea>
          <div class="flex justify-end mt-1">
            <span id="socialBenefitsCounter" class="text-xs text-neutral-400">0/1000</span>
          </div>
        </div>
        <div class="mt-4">
          <label for="teamQuality" class="form-label">团队素质</label>
          <textarea id="teamQuality" name="teamQuality" rows="4" class="form-field" maxlength="1000" placeholder="请描述团队成员背景及能力"></textarea>
          <div class="flex justify-end mt-1">
            <span id="teamQualityCounter" class="text-xs text-neutral-400">0/1000</span>
          </div>
        </div>
        <div class="mt-4">
          <label for="financialOperation" class="form-label">财务运营</label>
          <textarea id="financialOperation" name="financialOperation" rows="4" class="form-field" maxlength="1000" placeholder="请描述项目财务规划及运营模式"></textarea>
          <div class="flex justify-end mt-1">
            <span id="financialOperationCounter" class="text-xs text-neutral-400">0/1000</span>
          </div>
        </div>
        <div class="mt-4">
          <label for="marketProspect" class="form-label">市场前景</label>
          <textarea id="marketProspect" name="marketProspect" rows="4" class="form-field" maxlength="1000" placeholder="请分析项目市场潜力及发展趋势"></textarea>
          <div class="flex justify-end mt-1">
            <span id="marketProspectCounter" class="text-xs text-neutral-400">0/1000</span>
          </div>
        </div>
        <div class="mt-4">
          <label for="productService" class="form-label">产品服务</label>
          <textarea id="productService" name="productService" rows="4" class="form-field" maxlength="1000" placeholder="请详细描述产品功能及服务模式"></textarea>
          <div class="flex justify-end mt-1">
            <span id="productServiceCounter" class="text-xs text-neutral-400">0/1000</span>
          </div>
        </div>

      <div class="mt-4">
        <label for="projectPlanFile" class="form-label">项目计划书上传 <span class="text-red-500">*</span></label>
        <div class="relative border-2 border-dashed border-neutral-200 rounded-lg p-4 text-center hover:border-primary transition-colors duration-200">
          <input type="file" id="projectPlanFile"  class="absolute inset-0 w-full h-full opacity-0 cursor-pointer" accept=".pdf" >
          <input type="hidden" name="projectPlanFile" required>
          <p id="projectPlanFileError" class="error-message text-red-500 text-xs mt-1 hidden">请上传项目计划书文件</p>
          <div class="py-4">
            <i class="fa fa-file-pdf-o text-3xl text-neutral-300 mb-2"></i>
            <p class="text-neutral-400">点击或拖拽文件到此处上传项目计划书</p>
            <p class="text-xs text-neutral-400 mt-1">仅支持 PDF 格式，不超过50MB</p>
          </div>
        </div>
        <div id="planFilePreview" class="hidden mt-3">
          <div class="flex items-center bg-neutral-100 rounded-lg p-3">
            <i class="fa fa-file-pdf-o text-red-500 text-2xl mr-3"></i>
            <div class="flex-1">
              <p id="planFileName" class="font-medium text-neutral-700 truncate"></p>
              <p class="text-xs text-neutral-400">PDF文件</p>
            </div>
            <button type="button" onclick="removePreview(this)" id="removePlanFile" class="text-red-500">
              <i class="fa fa-times"></i>
            </button>
          </div>

        </div>
      </div>
<%--      // 文件预览功能初始化--%>
<%--      function setupFilePreview(inputElement, previewContainer, removeButton, hiddenInput) {--%>
<%--        inputElement.addEventListener('change', function(e) {--%>
<%--          const file = this.files[0];--%>
<%--          if (!file) return;--%>

<%--          // 验证文件类型和大小--%>
<%--          const isValidType = file.type === 'application/pdf';--%>
<%--          const isValidSize = file.size <= 50 * 1024 * 1024; // 50MB--%>

<%--          if (!isValidType) {--%>
<%--            showNotification('只能上传PDF格式的文件', false);--%>
<%--            this.value = '';--%>
<%--            return;--%>
<%--          }--%>

<%--          if (!isValidSize) {--%>
<%--            showNotification('文件大小不能超过50MB', false);--%>
<%--            this.value = '';--%>
<%--            return;--%>
<%--          }--%>

<%--          // 显示预览--%>
<%--          const fileName = file.name || '项目计划书.pdf';--%>
<%--          previewContainer.querySelector('#planFileName').textContent = fileName;--%>
<%--          previewContainer.classList.remove('hidden');--%>

<%--          // 将文件路径保存到隐藏输入框--%>
<%--          const filePath = URL.createObjectURL(file);--%>
<%--          hiddenInput.value = filePath;--%>

<%--          // 设置移除按钮点击事件--%>
<%--          if (removeButton) {--%>
<%--            removeButton.onclick = function() {--%>
<%--              inputElement.value = '';--%>
<%--              previewContainer.classList.add('hidden');--%>
<%--              hiddenInput.value = '';--%>
<%--            };--%>
<%--          }--%>
<%--        });--%>
<%--      }--%>

<%--      // 初始化项目计划书上传预览功能--%>
<%--      const projectPlanFileInput = document.getElementById('projectPlanFile');--%>
<%--      const planFilePreview = document.getElementById('planFilePreview');--%>
<%--      const removePlanFileBtn = document.getElementById('removePlanFile');--%>
<%--      const projectPlanHiddenInput = document.querySelector('#projectPlanFile + input[type="hidden"]');--%>

<%--      if (projectPlanFileInput && planFilePreview && projectPlanHiddenInput) {--%>
<%--        setupFilePreview(projectPlanFileInput, planFilePreview, removePlanFileBtn, projectPlanHiddenInput);--%>
<%--      }--%>

<%--      // 如果已有文件路径，显示预览--%>
<%--      if ('${reg.projectPlanFile}') {--%>
<%--        projectPlanHiddenInput.value = '${reg.projectPlanFile}';--%>
<%--        planFilePreview.classList.remove('hidden');--%>
<%--        document.getElementById('planFileName').textContent = '项目计划书.pdf'; // 可以从服务器获取实际文件名--%>
<%--      }--%>
   </div>
  <!-- 声明信息 -->
  <div class="form-section">
    <h2 class="text-lg font-semibold text-neutral-700 mb-4 flex items-center">
      <i class="fa fa-info-circle text-primary mr-2"></i> 声明信息
    </h2>
    <div class="mt-2">
      <label for="isAgreePublic" class="form-label">是否同意于线上平台公开展示本项目 <span class="text-red-500">*</span></label>
      <select id="isAgreePublic" name="isAgreePublic" class="form-field" required>
        <option value="">请选择</option>
        <c:forEach items="${yesNo}" var="item">
          <option value="${item.value}">${item.label}</option>
        </c:forEach>
<%--        <option value="1">是</option>--%>
<%--        <option value="0">否</option>--%>
      </select>
    </div>
  </div>
    <!-- 表单底部 -->
  <div class="flex flex-col sm:flex-row justify-between items-center gap-4 pt-4">
      <button type="reset" class="btn-secondary w-full sm:w-auto">
        <i class="fa fa-refresh mr-2"></i> 重置
      </button>
      <div class="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
        <button type="button" id="saveDraft" class="btn-draft w-full sm:w-auto flex justify-center">
          <i class="fa fa-save mr-2"></i> 保存草稿
        </button>
        <button type="button" id="submitApply" class="btn-primary w-full sm:w-auto flex justify-center">
          <i class="fa fa-paper-plane mr-2"></i> 提交报名
        </button>
        <button type="button" id="backToFormStatus" class="btn-secondary w-full sm:w-auto">
          <i class="fa fa-arrow-left mr-2"></i> 返回
        </button>
      </div>
    </div>
  </form>

  <div id="notificationToast" class="fixed bottom-4 right-4 text-white px-4 py-3 rounded-lg shadow-lg transform translate-y-20 opacity-0 hidden transition-all duration-500 flex items-center z-50">
    <i id="notificationIcon" class="mr-2"></i>
    <span id="notificationMessage">默认提示信息</span>
  </div>

</body>

<script>
  const MAX_ID_CARD_SIZE = 20 * 1024 * 1024; // 20MB
  const MAX_LICENSE_SIZE = 20 * 1024 * 1024; // 20MB
  const MAX_PLAN_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  // 初始化固定元素的预览
  setupFilePreview('#firstApplicantIdCardFrontFile', '#frontFilePreview', '#removeFrontFile');
  setupFilePreview('#firstApplicantIdCardBackFile', '#backFilePreview', '#removeBackFile');
  setupFilePreview('#companyBusinessLicenseFile', '#businessLicensePreview', '#removeBusinessLicenseFile');
  setupFilePreview('#projectPlanFile', '#planFilePreview', '#removePlanFile');
  initMemberFilePreviews()

  $(document).ready(function () {
    bindRemoveEvents();
    if('${reg.id }'){
      initMemberFilePreviews()
      // 初始化基础字段
      $('#registrationChannel').val('${reg.registrationChannel}');
      $('#competitionGroup').val('${reg.competitionGroup}');
      let competitionSubgroup='${reg.competitionSubgroup}';
      $('#competitionSubgroup').val(competitionSubgroup);
      if (competitionSubgroup === 'startup' || competitionSubgroup === 'growth') {
        $('#companyInfo').show();
      } else {
        $('#companyInfo').hide();
      }
      $('#ruralCompetitionSubgroup').val('${reg.ruralCompetitionSubgroup}');
      $('#projectField').val('${reg.projectField}');
      $('#competitionCity').val('${reg.competitionCity}');

      if ('${reg.companyName}') {
        $('#companyName').val('${reg.companyName}');
      }
      if ('${reg.companyEstablishTime}') {
        $('#companyEstablishTime').val('<fmt:formatDate value="${reg.companyEstablishTime}" pattern="yyyy-MM-dd"/>');
      }
      if ('${reg.companyAddress}') {
        $('#companyAddress').val('${reg.companyAddress}');
      }

      if ('${reg.companyBusinessLicenseFile}') {
        // 设置隐藏字段的值
        $('#companyBusinessLicenseFile').next('input[type="hidden"]').val('${reg.companyBusinessLicenseFile}');

        // 设置预览图片
        $('#businessLicensePreviewImage').attr('src', '${reg.companyBusinessLicenseFile}').show();

        // 显示预览容器
        $('#businessLicensePreview').removeClass('hidden');
      }

      // 初始化第一申报人信息
      $('#firstApplicantName').val('${reg.firstApplicantName}');
      $('#firstApplicantMobile').val('${reg.firstApplicantMobile}');
      $('#firstApplicantEmail').val('${reg.firstApplicantEmail}');
      $('#firstApplicantHukou').val('${reg.firstApplicantHukou}');
      $('#firstApplicantPosition').val('${reg.firstApplicantPosition}');
      $('#firstApplicantIdCard').val('${reg.firstApplicantIdCard}');
      $('#firstApplicantBirthday').val('<fmt:formatDate value="${reg.firstApplicantBirthday}" pattern="yyyy-MM-dd"/>');
      $('#firstApplicantGraduationTime').val('<fmt:formatDate value="${reg.firstApplicantGraduationTime}" pattern="yyyy-MM-dd"/>');

      if ('${reg.firstApplicantIdCardFrontFile}') {
        // 设置隐藏字段的值
        $('#firstApplicantIdCardFrontFilePath').val('${reg.firstApplicantIdCardFrontFile}');

        // 设置预览图片
        $('#frontPreviewImage').attr('src', '${reg.firstApplicantIdCardFrontFile}').show();

        // 显示预览容器
        $('#frontFilePreview').removeClass('hidden');
      }

      if ('${reg.firstApplicantIdCardBackFile}') {
        // 设置隐藏字段的值
        $('#firstApplicantIdCardBackFilePath').val('${reg.firstApplicantIdCardBackFile}');

        // 设置预览图片
        $('#backPreviewImage').attr('src', '${reg.firstApplicantIdCardBackFile}').show();

        // 显示预览容器
        $('#backFilePreview').removeClass('hidden');
      }
      // 设置性别
      $('input[name="firstApplicantGender"][value="${reg.firstApplicantGender}"]').prop('checked', true);

      // 初始化市-区联动
      const city = '${reg.competitionCity}';
      const district = '${reg.competitionDistrict}';
      if (city) {
        updateDistrictOptions(city, district);
      }

      // 控制乡村振兴分组显示
      if ($('#competitionGroup').val() === '3') {
        $('#ruralSubgroup').show();
        $('#ruralCompetitionSubgroup').attr('required', true);
      } else {
        $('#ruralSubgroup').hide();
        $('#ruralCompetitionSubgroup').attr('required', false);
      }
      //团队成员
      if($('#projectMembersData').val()){
        let members = JSON.parse($('#projectMembersData').val());
        if (members && members.length > 0) {
          // 克隆模板行
          const template = $('.member-row:first').clone().removeClass('hidden').show();
          // 清空现有成员
          $('#projectMembers .member-row:not(.template)').remove();
          members.forEach((member, index) => {
            let row = template.clone().removeClass('template').show(); // 去掉 .template
            row.find('input[type="radio"]').attr('name', 'member-gender-' + (index+1));
            row.find('.member-name').val(member.name);
            row.find('.member-contact').val(member.contact);
            row.find('input[name="member-gender-'+(index+1)+'"][value="'+member.gender+'"]').prop('checked', true);
            row.find('.member-position').val(member.position);
            row.find('.member-id-card').val(member.idCard);
            row.find('.member-birthday').val(member.birthday);
            // 设置文件预览
            if (member.idCardFront) {
              row.find('.member-front-preview-image').attr('src', member.idCardFront).show();
              row.find('.member-front-file-preview').removeClass('hidden');
              row.find('.member-id-card-front').next('input[type="hidden"]').val(member.idCardFront);
            }
            if (member.idCardBack) {
              row.find('.member-back-preview-image').attr('src', member.idCardBack).show();
              row.find('.member-back-file-preview').removeClass('hidden');
              row.find('.member-id-card-back').next('input[type="hidden"]').val(member.idCardBack);
            }
            $('#projectMembers').append(row);
          });
        }
      }

      if($('#mentorsData').val()){
        //指导老师
        let mentors = JSON.parse($('#mentorsData').val());
        if (mentors && mentors.length > 0) {
          // 克隆模板行
          const template = $('.mentor-row:first').clone().removeClass('hidden').show();
          // 清空现有成员
          $('#mentors').empty();
          mentors.forEach((mentor, index) => {
            let row = template.clone().show();
            row.find('.mentor-name').val(mentor.name);
            row.find('.mentor-position').val(mentor.position);

            $('#mentors').append(row);
          });

        }
      }

      const regData = JSON.parse('${regJson}');
      let projectBrief = regData.projectBrief;
      if(projectBrief){
        $('#projectBrief').val(projectBrief);
        $('#briefCounter').text(projectBrief.length+'/200');
      }


      let industryCompetitiveAdvantage = regData.industryCompetitiveAdvantage;
      if(industryCompetitiveAdvantage){
        $('#industryCompetitiveAdvantage').val(industryCompetitiveAdvantage);
        $('#advantageCounter').text((industryCompetitiveAdvantage.length+'/1000'));
      }

      let socialBenefits = regData.socialBenefits;
      if(socialBenefits){
        $('#socialBenefits').val(socialBenefits);
        $('#socialBenefitsCounter').text(socialBenefits.length+'/1000');
      }

      let teamQuality = regData.teamQuality;
      if(teamQuality){
        $('#teamQuality').val(teamQuality);
        $('#teamQualityCounter').text(teamQuality.length+'/1000');
      }

      let marketProspect = regData.marketProspect;
      if(marketProspect){
        $('#marketProspect').val(marketProspect);
        $('#marketProspectCounter').text(marketProspect.length+'/1000');
      }

      let financialOperation = regData.financialOperation;
      if(financialOperation){
        $('#financialOperation').val(financialOperation);
        $('#financialOperationCounter').text(financialOperation.length+'/1000');
      }

      let productService = regData.productService;
      if(productService){
        $('#productService').val(productService);
        $('#productServiceCounter').text(productService.length+'/1000');
      }

      $('#isAgreePublic').val('${reg.isAgreePublic}');

      if ('${reg.projectPlanFile}') {
        $('#projectPlanFile').next('input[type="hidden"]').val('${reg.projectPlanFile}');
        $('#planFileName').text('项目计划书.pdf'); // 假设后端返回的文件名信息不足，这里使用默认名称
        $('#planFilePreview').removeClass('hidden');
      }

      // 初始化字数统计
      setupCharacterCount('projectBrief', 'briefCounter', 200);
      setupCharacterCount('industryCompetitiveAdvantage', 'advantageCounter', 1000);
      setupCharacterCount('socialBenefits', 'socialBenefitsCounter', 1000);
      setupCharacterCount('teamQuality', 'teamQualityCounter', 1000);
      setupCharacterCount('financialOperation', 'financialOperationCounter', 1000);
      setupCharacterCount('marketProspect', 'marketProspectCounter', 1000);
      setupCharacterCount('productService', 'productServiceCounter', 1000);

    }

    $('#backToFormStatus').on('click', function () {
      customConfirm('确定要返回表单状态页吗？当前未保存的数据将会丢失。', function () {
        window.location.href = '${ctx}/apiAct2025/formStatus';
      });
    });
  });


  // 动态处理团队成员身份证预览
  function initMemberFilePreviews() {
    $('.member-row').each(function() {
      let $row = $(this);
      // 成员身份证正面预览
      setupFilePreview(
              $row.find('.member-id-card-front'),
              $row.find('.member-front-file-preview'),
              $row.find('.member-remove-front-file')
      );
      // 成员身份证反面预览
      setupFilePreview(
              $row.find('.member-id-card-back'),
              $row.find('.member-back-file-preview'),
              $row.find('.member-remove-back-file')
      );
    });
  }



  // 封装文件和图片预览函数
  function setupFilePreview(inputSelector, previewSelector, removeButtonSelector) {
    const $input = $(inputSelector);
    const $preview = $(previewSelector);
    $input.on('change', function() {
      const file = this.files[0];
      if (file) {
        // 获取文件大小和类型
        const fileSize = file.size;
        const fileType = file.type;

        // 根据文件类型设置最大限制
        let maxSize = MAX_ID_CARD_SIZE;
        if (fileType === 'application/pdf') {
          maxSize = MAX_PLAN_FILE_SIZE;
        } else if ($input.attr('id') === 'companyBusinessLicenseFile') {
          maxSize = MAX_LICENSE_SIZE;
        }

        // 文件大小校验
        if (fileSize > maxSize) {
          showNotification('文件大小超过限制，请上传小于'+formatBytes(maxSize)+'  的文件', "error");
          this.value = ''; // 清空文件选择
          return;
        }

        const reader = new FileReader();
        // 检查文件类型
        if (file.type.match('image.*')) {
          // 图片预览
          reader.onload = function(e) {
            $preview.find('img').attr('src', e.target.result).show();
            $preview.find('p').hide();
            $preview.removeClass('hidden');
          };
          reader.readAsDataURL(file);
        } else {
          // 文件预览（显示文件名）
          $preview.find('p:first').text(file.name).show();
          $preview.find('img').hide();
          $preview.removeClass('hidden');
        }
      }
    });
  }

  // 手机号正则表达式（中国大陆）
  const mobileRegex = /^1[3-9]\d{9}$/;
  // 身份证号正则表达式（支持15位和18位）
  const idCardRegex = /(^\d{15}$)|(^\d{17}([0-9]|X|x)$)/;

  // 浙江省市-区县映射关系

  const districtMap = {
    '330100': [
      { code: '330102', name: '上城区' },
      { code: '330103', name: '拱墅区' },
      { code: '330104', name: '西湖区' },
      { code: '330105', name: '滨江区' },
      { code: '330109', name: '萧山区' },
      { code: '330110', name: '余杭区' },
      { code: '330111', name: '富阳区' },
      { code: '330112', name: '临安区' },
      { code: '330113', name: '临平区' },
      { code: '330114', name: '钱塘区' },
      { code: '330122', name: '桐庐县' },
      { code: '330127', name: '淳安县' },
      { code: '330182', name: '建德市' }
    ],
    '330200': [
      { code: '330203', name: '海曙区' },
      { code: '330205', name: '江北区' },
      { code: '330206', name: '北仑区' },
      { code: '330211', name: '镇海区' },
      { code: '330212', name: '鄞州区' },
      { code: '330225', name: '象山县' },
      { code: '330226', name: '宁海县' },
      { code: '330281', name: '余姚市' },
      { code: '330282', name: '慈溪市' },
      { code: '330283', name: '奉化区' }
    ],
    '330300': [
      { code: '330302', name: '鹿城区' },
      { code: '330303', name: '龙湾区' },
      { code: '330304', name: '瓯海区' },
      { code: '330305', name: '洞头区' },
      { code: '330324', name: '永嘉县' },
      { code: '330326', name: '平阳县' },
      { code: '330327', name: '苍南县' },
      { code: '330328', name: '文成县' },
      { code: '330329', name: '泰顺县' },
      { code: '330381', name: '瑞安市' },
      { code: '330382', name: '乐清市' },
      { code: '330383', name: '龙港市' }
    ],
    '330400': [
      { code: '330402', name: '南湖区' },
      { code: '330411', name: '秀洲区' },
      { code: '330421', name: '嘉善县' },
      { code: '330424', name: '海盐县' },
      { code: '330481', name: '海宁市' },
      { code: '330482', name: '平湖市' },
      { code: '330483', name: '桐乡市' }
    ],
    '330500': [
      { code: '330502', name: '吴兴区' },
      { code: '330503', name: '南浔区' },
      { code: '330521', name: '德清县' },
      { code: '330522', name: '长兴县' },
      { code: '330523', name: '安吉县' }
    ],
    '330600': [
      { code: '330602', name: '越城区' },
      { code: '330603', name: '柯桥区' },
      { code: '330604', name: '上虞区' },
      { code: '330624', name: '新昌县' },
      { code: '330681', name: '诸暨市' },
      { code: '330683', name: '嵊州市' }
    ],
    '330700': [
      { code: '330702', name: '婺城区' },
      { code: '330703', name: '金东区' },
      { code: '330723', name: '武义县' },
      { code: '330726', name: '浦江县' },
      { code: '330727', name: '磐安县' },
      { code: '330781', name: '兰溪市' },
      { code: '330782', name: '义乌市' },
      { code: '330783', name: '东阳市' },
      { code: '330784', name: '永康市' }
    ],
    '330800': [
      { code: '330802', name: '柯城区' },
      { code: '330803', name: '衢江区' },
      { code: '330822', name: '常山县' },
      { code: '330824', name: '开化县' },
      { code: '330825', name: '龙游县' },
      { code: '330881', name: '江山市' }
    ],
    '330900': [
      { code: '330902', name: '定海区' },
      { code: '330903', name: '普陀区' },
      { code: '330921', name: '岱山县' },
      { code: '330922', name: '嵊泗县' }
    ],
    '331000': [
      { code: '331002', name: '椒江区' },
      { code: '331003', name: '黄岩区' },
      { code: '331004', name: '路桥区' },
      { code: '331022', name: '三门县' },
      { code: '331023', name: '天台县' },
      { code: '331024', name: '仙居县' },
      { code: '331081', name: '温岭市' },
      { code: '331082', name: '临海市' },
      { code: '331083', name: '玉环市' }
    ],
    '331100': [
      { code: '331102', name: '莲都区' },
      { code: '331121', name: '青田县' },
      { code: '331122', name: '缙云县' },
      { code: '331123', name: '遂昌县' },
      { code: '331124', name: '松阳县' },
      { code: '331125', name: '云和县' },
      { code: '331126', name: '庆元县' },
      { code: '331127', name: '景宁畲族自治县' },
      { code: '331181', name: '龙泉市' }
    ]
  };

  // 身份证号验证（增强版，包含校验位计算）
  function validateIdCardNumber(idCard) {
    idCard = idCard.toUpperCase();
    // 长度验证
    if (!idCardRegex.test(idCard)) {
      return false;
    }
    // 18位身份证需要验证最后一位校验位
    if (idCard.length === 18) {
      const idCardArray = idCard.split('');
      // 前17位权重因子
      const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      // 校验位对应值
      const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
      let sum = 0;

      for (let i = 0; i < 17; i++) {
        sum += parseInt(idCardArray[i]) * factor[i];
      }

      const lastChar = idCardArray[17];
      if (lastChar !== parity[sum % 11]) {
        return false;
      }
    }

    return true;
  }

  // 监听市选择变化
  $('#competitionCity').on('change', function() {
    let city = $(this).val();
    let districtSelect = $('#competitionDistrict');
    districtSelect.empty();

    if (city) {
      districtSelect.append('<option value="">请选择区县</option>');
      districtMap[city].forEach(district => {
        districtSelect.append('<option value="'+district.code+'">'+district.name+'</option>');
      });
    } else {
      districtSelect.append('<option value="">请先选择市</option>');
    }
  });

  // 根据赛事分组子项的选择，显示或隐藏公司信息
  $('#competitionSubgroup').on('change', function() {
    let selectedValue = $(this).val();
    if (selectedValue === 'startup' || selectedValue === 'growth') {
      $('#companyInfo').show();
    } else {
      $('#companyInfo').hide();
    }
  });

  // 监听分组变化
  $('#competitionGroup').on('change', function() {
    let group = $(this).val();
    if(group==='3'){
      $('#ruralCompetitionSubgroup').attr('required',true);
      $('#ruralSubgroup').show();
    }else{
      $('#ruralCompetitionSubgroup').attr('required',false);
      $('#ruralSubgroup').hide();
    }
  });

  // // 移除项目计划书文件
  // $('#removePlanFile').on('click', function() {
  //   $('#projectPlanFile').val('');
  //   $('#planFilePreview').addClass('hidden');
  // });

  let maxMembers = 5;
  // 添加团队成员
  $('#addMember').on('click', function() {
    let memberIndex = $('#projectMembers .member-row').length;
    if ($('.member-row').length >= maxMembers) {
      showNotification("最多只能添加4名团队成员", "error");
      return;
    }

    let memberRow = $('.member-row.template').first().clone()
            .removeClass('template')
            .removeAttr('style') // 显示出来
            .show();
    // 清空所有输入字段
    memberRow.find('input[type="text"],input[type="tel"] ,input[type="hidden"],input[type="date"], input[type="number"], textarea').val('');
    // 清空下拉框
    memberRow.find('select').val('');
    // 清除身份证预览图片
    memberRow.find('.member-front-preview-image').attr('src', '').hide();
    memberRow.find('.member-back-preview-image').attr('src', '').hide();
    memberRow.find('.member-front-file-preview').addClass('hidden');
    memberRow.find('.member-back-file-preview').addClass('hidden');
    // 更新 radio 的 name 属性
    memberRow.find('input[type="radio"]').attr('name', 'member-gender-' + memberIndex);
    // 取消单选按钮和复选框的选中状态
    memberRow.find('input[type="radio"], input[type="checkbox"]').prop('checked', false);
    // 添加到容器
    $('#projectMembers').append(memberRow);
    // 添加移除动画
    memberRow.hide().fadeIn(300);
    // 绑定移除事件
    bindRemoveEvents();
    // 绑定手机号验证
    bindMobileValidation();
    //初始化文件预览
    initMemberFilePreviews();
  });

  // 绑定移除成员事件

  function bindRemoveEvents() {
    $('.remove-member').off('click').on('click', function() {
      let $row = $(this).closest('.member-row');
      // 如果只剩一个成员行，直接删除（模板行始终存在）
      if ($('#projectMembers .member-row').length === 1) {
        $row.hide();
      } else {
        $row.fadeOut(300, function() {
          $(this).remove();
        });
      }
    });
  }

  // function bindRemoveEvents() {
  //   $('.remove-member').off('click').on('click', function() {
  //     let memberRow = $(this).closest('.member-row');
  //     // 如果只有一个成员行，则清空输入值而不是删除
  //     if ($('.member-row').length === 1) {
  //       memberRow.find('input').val('');
  //     } else {
  //       // 添加移除动画
  //       memberRow.fadeOut(300, function() {
  //         $(this).remove();
  //       });
  //     }
  //   });
  // }

  // 手机号验证
  function validateMobile(input) {
    const value = input.val().trim();
    const isValid = mobileRegex.test(value);
    const errorElement = input.next('.error-message');

    if (value && !isValid) {
      input.addClass('form-input-error');
      errorElement.removeClass('hidden');
      return false;
    } else {
      input.removeClass('form-input-error');
      errorElement.addClass('hidden');
      return true;
    }
  }

  function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  // 身份证号验证
  function validateIdCard(input) {
    const value = input.val().trim();
    const isValid = validateIdCardNumber(value);
    const errorElement = input.next('.error-message');

    if (value && !isValid) {
      input.addClass('form-input-error');
      errorElement.removeClass('hidden');
      return false;
    } else {
      input.removeClass('form-input-error');
      errorElement.addClass('hidden');
      return true;
    }
  }

  // 表单提交前验证
  function validateForm() {
    let isValid = true;

    // 验证申报人手机号
    if (!validateMobile($('#firstApplicantMobile'))) {
      isValid = false;
    }

    // 验证申报人身份证号
    if (!validateIdCard($('#firstApplicantIdCard'))) {
      isValid = false;
    }

    // 验证文件上传字段
    const requiredFiles = [
      { input: '#firstApplicantIdCardFrontFile', hidden: '#firstApplicantIdCardFrontFilePath', error: '#firstApplicantIdCardFrontError', message: '请上传身份证正面文件' },
      { input: '#firstApplicantIdCardBackFile', hidden: '#firstApplicantIdCardBackFilePath', error: '#firstApplicantIdCardBackError', message: '请上传身份证反面文件' },
      { input: '#companyBusinessLicenseFile', hidden: '#companyBusinessLicenseFile + input[type="hidden"]', error: '#companyBusinessLicenseError', message: '请上传营业执照文件' },
      { input: '#projectPlanFile', hidden: '#projectPlanFile + input[type="hidden"]', error: '#projectPlanFileError', message: '请上传项目计划书文件' }
    ];

    requiredFiles.forEach(file => {
      const hiddenInput = $(file.hidden);
      const errorElement = $(file.error);
      if (!hiddenInput.val()) {
        errorElement.removeClass('hidden').text(file.message);
        isValid = false;
      } else {
        errorElement.addClass('hidden');
      }
    });

    const hasMembers = $('#projectMembers .member-row').length > 0;
    if (hasMembers) {
      // 验证团队成员身份证上传
      $('.member-row').each(function(index) {
        const $row = $(this);
        const frontHidden = $row.find('.member-id-card-front').next('input[type="hidden"]');
        const backHidden = $row.find('.member-id-card-back').next('input[type="hidden"]');
        if (!frontHidden.val()) {
          $row.find('.member-id-card-front-error').removeClass('hidden').text('请上传身份证正面文件');
          isValid = false;
        } else {
          $row.find('.member-id-card-front-error').addClass('hidden');
        }

        if (!backHidden.val()) {
          $row.find('.member-id-card-back-error').removeClass('hidden').text('请上传身份证反面文件');
          isValid = false;
        } else {
          $row.find('.member-id-card-back-error').addClass('hidden');
        }
      });

      // 验证团队成员手机号
      $('.member-contact').each(function() {
        if (!validateMobile($(this))) {
          isValid = false;
        }
      });
    }
    return isValid;
  }

  // 绑定手机号和身份证验证
  function bindValidation() {
    // 申报人手机号验证
    $('#firstApplicantMobile').on('input blur', function() {
      validateMobile($(this));
    });

    // 申报人身份证验证
    $('#firstApplicantIdCard').on('input blur', function() {
      validateIdCard($(this));
    });

    // 团队成员手机号验证
    bindMobileValidation();

  }

  $('#projectMembers').on('change', 'input[type="file"]', function() {
    var _this = this;
    var file = this.files[0];
    if (file) {
      // 获取对应的 userId 和 activityId
      let userId = $('#userId').val();
      let activityId = $('#hdId').val();
      var fileType = getFileType($(this).attr('id'));

      // 判断是否是团队成员身份证上传
      var memberIndex = null;
      if ($(this).hasClass('member-id-card-front') || $(this).hasClass('member-id-card-back')) {
        if ($(this).hasClass('member-id-card-front')) {
          fileType = 'TEAM_MEMBER_IDCARD_FRONT';
        } else if ($(this).hasClass('member-id-card-back')) {
          fileType = 'TEAM_MEMBER_IDCARD_BACK';
        }
        memberIndex = $(this).closest('.member-row').index() + 1;
      }

      // 构造 FormData
      var formData = new FormData();
      formData.append('file', file);
      formData.append('userId', userId);
      formData.append('activityId', activityId);
      formData.append('fileType', fileType);
      if (memberIndex) {
        formData.append('memberIndex', memberIndex);
      }

      // 发起 AJAX 请求
      $.ajax({
        url: '${ctx}/apiAct2025/uploadFile',
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        success: function(response) {
          if (response.success) {
            // ✅ 正确赋值隐藏域
            $(_this).next('input[type="hidden"]').val(response.body.ossUrl);
          }
        },
        error: function(xhr, status, error) {
          alert('文件上传失败，请重试！');
          console.error(error);
        }
      });
    }
  });

  $('#submitApply').click(function() {
    customConfirm('确认要提交报名信息吗？一经确认提交，无法修改。', function () {
      // 查找所有带有 required 属性的表单元素
      const requiredFields = $('#competitionForm').find('[required]').filter(function () {
        const $field = $(this);
        // 如果字段在隐藏的 .form-section 中，排除
        if ($field.closest('.form-section').is(':hidden')) {
          return false;
        }
        // 如果字段在隐藏的 .member-row 中（包括模板行），排除
        if ($field.closest('.member-row').is(':hidden')) {
          return false;
        }
        // 其他情况保留
        return true;

      });

      let hasError = false;
      // 遍历所有必填字段
      requiredFields.each(function() {
        const field = $(this);

        // 检查字段是否为空
        if (field.val() === '') {
          // 如果字段是隐藏的，先显示它以便聚焦
          if (field.is(':hidden')) {

            field.parent().addClass('form-input-error');
          }
          // 添加错误样式
          field.addClass('form-input-error');
          // 显示错误消息
          field.next('.error-message').show();
          hasError = true;
        } else {
          if (field.is(':hidden')) {
            field.parent().removeClass('form-input-error');
          }
          // 移除错误样式
          field.removeClass('form-input-error');
          // 隐藏错误消息
          field.next('.error-message').hide();
        }
      });

      // 如果有错误，阻止表单提交
      if (hasError) {
        var visibleErrorFields = requiredFields.filter(function() {
          return $(this).val() === '' && $(this).is(':visible');
        });

        var hiddenErrorFields = requiredFields.filter(function() {
          return $(this).val() === '' && !$(this).is(':visible');
        });

        if (visibleErrorFields.length > 0) {
            visibleErrorFields.first().focus();
        } else if (hiddenErrorFields.length > 0) {
          // 找到隐藏字段的可见父元素并聚焦
          var parent = hiddenErrorFields.first().closest(':visible');
          if (parent.length > 0) {
            parent[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            parent.addClass('focus-ring'); // 可选：添加高亮样式
          }
        }
        return;
      }


      // 收集表单数据
      var formData = new FormData($('#competitionForm')[0]);
      var draftData = {};
      // 处理基础表单数据
      formData.forEach(function(value, key) {
        if(key.includes('member-gender-') ) return;
        draftData[key] = value;
      });

      // 处理团队成员数据
      let teamMembersData = [];
      const hasMembers = $('#projectMembers .member-row:not(.template):visible').length > 0;
      if(hasMembers){
        $('#projectMembers .member-row:not(.template):visible').each(function(index) {
          const $row = $(this);
          const genderInput = $row.find('input[name="member-gender-'+(index+1)+'"]:checked');
          const gender = genderInput.length ? genderInput.val() : null;
          if (!gender) {
            showNotification("请为所有团队成员选择性别", "error");
            throw new Error("存在未填写性别的团队成员");
          }

          var member = {
            name: $(this).find('.member-name').val() || '',
            contact: $(this).find('.member-contact').val() || '',
            gender: gender,
            position: $(this).find('.member-position').val() || '',
            idCard: $(this).find('.member-id-card').val() || '',
            birthday: $(this).find('.member-birthday').val() || '',
            idCardFront: $(this).find('.member-id-card-front').next('input[type="hidden"]').val() || '',
            idCardBack: $(this).find('.member-id-card-back').next('input[type="hidden"]').val() || ''
          };
          // 检查成员的所有字段是否都为空
          var isMemberEmpty = Object.values(member).every(val => val === '');
          if (!isMemberEmpty) {
            teamMembersData.push(member);
          }
        });
      }
      draftData.projectMembers = JSON.stringify(teamMembersData); // 序列化为字符串


      // 处理指导老师数据
      let mentorsData = [];
      $('#mentors .mentor-row').each(function(index) {
        var mentor = {
          name: $(this).find('.mentor-name').val() || '',
          position: $(this).find('.mentor-position').val() || ''
        };
        // 检查导师的所有字段是否都为空
        var isMentorEmpty = Object.values(mentor).every(val => val === '');
        if (!isMentorEmpty) {
          mentorsData.push(mentor);
        }
      });
      draftData.mentors = JSON.stringify(mentorsData); // 序列化为字符串
      disableSubmitButtons();
      $.ajax({
        url: '${ctx}/apiAct2025/submit',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(draftData),
        success: function(response) {
          showNotification("报名提交成功！", "success");
          // resetForm(); // 提交成功后刷新页面或重置表单
          window.location.href = '${ctx}/apiAct2025/formStatus?refresh=1';
        },
        error: function(xhr, status, error) {
          // 发生错误时
          showNotification("提交失败，请重试", "error");
          enableSubmitButtons();
        }
      });
    });
  });

  // 绑定团队成员手机号验证
  function bindMobileValidation() {
    $('.member-contact').on('input blur', function() {
      validateMobile($(this));
    });
  }
  // 初始化绑定验证
  bindValidation();
  // 初始化绑定移除事件
  bindRemoveEvents();


  // 字数统计函数
  function setupCharacterCount(textareaId, counterId, maxLength) {
    const textarea = document.getElementById(textareaId);
    const counter = document.getElementById(counterId);
    textarea.addEventListener('input', function() {
      let currentLength = this.value.length;
      counter.textContent = currentLength+'/'+maxLength;
    });
  }



  // 初始化现有成员行
  // initMemberFilePreviews();

  $(document).ready(function() {
    bindRemoveEvents();
    initMemberFilePreviews();
    let maxMentors = 2;
    // 添加导师
    $('#addMentor').on('click', function() {
      if ($('.mentor-row').length > maxMentors) {
        showNotification("最多只能添加2位指导老师", "error");
        return;
      }
      var mentorTemplate = $('.mentor-row:first').clone();

      mentorTemplate.show();
      mentorTemplate.find('.remove-mentor').on('click', function() {
        $(this).closest('.mentor-row').remove();
      });
      $('#mentors').append(mentorTemplate);
    });

    // 删除导师
    $(document).on('click', '.remove-mentor', function() {
      $(this).closest('.mentor-row').remove();
    });



    // 保存草稿功能
    $('#saveDraft').on('click', function() {
      disableSubmitButtons();
      var formData = new FormData($('#competitionForm')[0]);
      var draftData = {};
      // 处理基础表单数据
      formData.forEach(function(value, key) {
        if(key.includes('member-gender-') ) return;
        draftData[key] = value;
      });


      // 处理团队成员数据
      let teamMembersData = [];
      $('#projectMembers .member-row:not(.template):visible').each(function(index) {
        var member = {
          name: $(this).find('.member-name').val() || '',
          contact: $(this).find('.member-contact').val() || '',
          gender: $(this).find('input[name="member-gender-' + (index+1) + '"]:checked').val() || '',
          position: $(this).find('.member-position').val() || '',
          idCard: $(this).find('.member-id-card').val() || '',
          birthday: $(this).find('.member-birthday').val() || '',
          idCardFront: $(this).find('.member-id-card-front').next('input[type="hidden"]').val() || '',
          idCardBack: $(this).find('.member-id-card-back').next('input[type="hidden"]').val() || ''
        };
        // 检查成员的所有字段是否都为空
        var isMemberEmpty = Object.values(member).every(val => val === '');
        if (!isMemberEmpty) {
          teamMembersData.push(member);
        }
      });
      draftData.projectMembers = teamMembersData;
      draftData.projectMembers = JSON.stringify(teamMembersData); // 序列化为字符串

      // 处理指导老师数据
      let mentorsData = [];
      $('#mentors .mentor-row').each(function(index) {
        var mentor = {
          name: $(this).find('.mentor-name').val() || '',
          position: $(this).find('.mentor-position').val() || ''
        };
        // 检查导师的所有字段是否都为空
        var isMentorEmpty = Object.values(mentor).every(val => val === '');
        if (!isMentorEmpty) {
          mentorsData.push(mentor);
        }
      });
      draftData.mentors = mentorsData;
      draftData.mentors = JSON.stringify(mentorsData); // 序列化为字符串
      $.ajax({
        url: '${ctx}/apiAct2025/saveDraft',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(draftData),
        success: function(response) {
          console.log("草稿已保存");
          enableSubmitButtons();
          showNotification("草稿保存成功！", "success");
        },
        error: function(xhr, status, error) {
          enableSubmitButtons();
          showNotification("保存草稿失败，请重试！","error");
          console.error(error);
        }
      });
    });

    // 选中表单中所有的 input[type="file"] 元素
    $('input[type="file"]').on('change', function() {
      var _this=this;
      // 获取当前选中的文件
      var file = this.files[0];
      if (file) {
        // 获取对应的 userId 和 activityId，这里假设这两个值可以从页面元素中获取
        let userId = $('#userId').val(); // 请根据实际情况修改选择器
        let activityId = $('#hdId').val(); // 请根据实际情况修改选择器
        // 获取文件类型
        var fileType = getFileType($(this).attr('id'));
        // 获取团队成员编号，如果不是团队成员文件则为 null
        var memberIndex = null;
        if ($(this).hasClass('member-id-card-front') || $(this).hasClass('member-id-card-back')) {
          if($(this).hasClass('member-id-card-front')){
            fileType = 'TEAM_MEMBER_IDCARD_FRONT';
          }else if($(this).hasClass('member-id-card-back')){
            fileType = 'TEAM_MEMBER_IDCARD_BACK';
          }
          memberIndex = $(this).closest('.member-row').index() + 1;
        }
        // 创建 FormData 对象
        var formData = new FormData();
        formData.append('file', file);
        formData.append('userId', userId);
        formData.append('activityId', activityId);
        formData.append('fileType', fileType);
        if (memberIndex) {
          formData.append('memberIndex', memberIndex);
        }
        // 发送 AJAX 请求上传文件
        $.ajax({
          url: '${ctx}/apiAct2025/uploadFile', // 接口地址
          type: 'POST',
          data: formData,
          contentType: false,
          processData: false,
          success: function(response) {
            if(response.success){
              // 可以在这里更新隐藏输入框的值，用于表单提交
              // alert('文件上传成功，OSS URL: '+response.body.ossUrl);
              $(_this).next('input[type="hidden"]').val(response.body.ossUrl);
            }
          },
          error: function(xhr, status, error) {
            alert('文件上传失败，请重试！');
            console.error(error);
          }
        });
      }
    });
  });

  // 根据输入框的 ID 获取文件类型
  function getFileType(inputId) {
    switch (inputId) {
      case 'firstApplicantIdCardFrontFile':
        return 'APPLICANT_IDCARD_FRONT';
      case 'firstApplicantIdCardBackFile':
        return 'APPLICANT_IDCARD_BACK';
      case 'companyBusinessLicenseFile':
        return 'BUSINESS_LICENSE';
      case 'projectPlanFile':
        return 'PROJECT_PLAN';
      case 'member-id-card-front':
        return 'TEAM_MEMBER_IDCARD_FRONT';
      case 'member-id-card-back':
        return 'TEAM_MEMBER_IDCARD_BACK';
      default:
        return null;
    }
  }

  function  showNotification(message, type) {
    var toast = $('#notificationToast');
    var icon = $('#notificationIcon');
    var msg = $('#notificationMessage');

    // 设置样式和图标
    if (type === 'success') {
      toast.removeClass('bg-red-500').addClass('bg-green-500');
      icon.removeClass('fa-exclamation-circle').addClass('fa-check-circle');
    } else {
      toast.removeClass('bg-green-500').addClass('bg-red-500');
      icon.removeClass('fa-check-circle').addClass('fa-exclamation-circle');
    }

    msg.text(message);
    toast.removeClass('hidden opacity-0 translate-y-20');

    setTimeout(function () {
      toast.addClass('opacity-0 translate-y-20');
      setTimeout(function () {
        toast.addClass('hidden');
      }, 5000);
    }, 5000);
  }

  function disableSubmitButtons() {
    $('#saveDraft, #submitApply').prop('disabled', true).addClass('opacity-50 cursor-not-allowed');
  }

  function enableSubmitButtons() {
    $('#saveDraft, #submitApply').prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
  }

  function resetForm() {
    // 方法一：直接刷新页面（简单有效）
    // location.reload();

    // 方法二：手动重置表单 + 动态区域
    $('#competitionForm')[0].reset();
    $('#projectMembers .member-row:not(:first)').remove(); // 删除新增的成员行
    $('#mentors .mentor-row').remove();     // 删除新增的导师行
    $('.form-input-error').removeClass('form-input-error');
    $('.error-message').addClass('hidden');

    // 清除第一申报人身份证预览
    $('#frontPreviewImage').attr('src', '').hide();
    $('#backPreviewImage').attr('src', '').hide();
    $('#frontFilePreview').addClass('hidden');
    $('#backFilePreview').addClass('hidden');

    // 清除团队成员身份证预览
    $('.member-front-preview-image').attr('src', '').hide();
    $('.member-back-preview-image').attr('src', '').hide();
    $('.member-front-file-preview').addClass('hidden');
    $('.member-back-file-preview').addClass('hidden');

    memberIndex = 1;
    mentorIndex = 1;
  }

  // 在页面加载时通过监听市选择变化动态更新区县
  function updateDistrictOptions(cityCode, selectedDistrict) {
    const districtSelect = $('#competitionDistrict');
    districtSelect.empty();

    if (cityCode && districtMap[cityCode]) {
      districtSelect.append('<option value="">请选择区县</option>');
      districtMap[cityCode].forEach(district => {
        var option = '<option value="' + district.code + '" ' +
                (district.code == selectedDistrict ? 'selected' : '') + '>' +
                district.name + '</option>';
        districtSelect.append(option);
      });
    } else {
      districtSelect.append('<option value="">请先选择市</option>');
    }
  }

  function removePreview(button) {
    // 获取被点击的按钮元素
    const $button = $(button);
    // 找到对应的文件输入框（隐藏的 input[type="file"]）
    const $fileInput = $button.closest('.upload-area').find('input[type="file"]');
    // 找到对应的预览容器（例如图片预览区域）
    const $previewContainer = $button.closest('.member-front-file-preview, .member-back-file-preview, #frontFilePreview, #backFilePreview, #businessLicensePreview, #planFilePreview');
    // 隐藏预览内容（如 img 或文件名）
    $previewContainer.find('img, p').hide();

    // 如果是团队成员的身份证预览，还需要清空隐藏的 input[type="hidden"]
    if ($button.hasClass('member-remove-front-file') || $button.hasClass('member-remove-back-file')) {
      $button.parent().prev().find('input[type="hidden"]').val('');
    }else{
      $previewContainer.prev().find('input[type="hidden"]').val('');
    }
    // 隐藏整个预览容器
    $previewContainer.addClass('hidden');
  }

  // 自定义 confirm 函数
  function customConfirm(message, onConfirm, onCancel = () => {}) {
    const modal = $('#customConfirmModal');
    const messageEl = $('#customConfirmMessage');
    const confirmBtn = $('#confirmOk');
    const cancelBtn = $('#confirmCancel');

    messageEl.text(message);

    // 显示模态框
    modal.removeClass('hidden');

    // 确认按钮点击
    confirmBtn.off('click').on('click', function () {
      modal.addClass('hidden');
      onConfirm();
    });

    // 取消按钮点击
    cancelBtn.off('click').on('click', function () {
      modal.addClass('hidden');
      onCancel();
    });

    // 点击遮罩关闭
    modal.off('click').on('click', function (e) {
      if (e.target === modal[0]) {
        modal.addClass('hidden');
        onCancel();
      }
    });
  }
</script>

<%--<script>--%>
<%--  // 常量定义--%>
<%--  const Constants = {--%>
<%--    MAX_ID_CARD_SIZE: 20 * 1024 * 1024, // 20MB--%>
<%--    MAX_LICENSE_SIZE: 20 * 1024 * 1024, // 20MB--%>
<%--    MAX_PLAN_FILE_SIZE: 50 * 1024 * 1024, // 50MB--%>
<%--    MAX_MEMBERS: 4,--%>
<%--    MAX_MENTORS: 2,--%>
<%--    MOBILE_REGEX: /^1[3-9]\d{9}$/,--%>
<%--    ID_CARD_REGEX: /(^\d{15}$)|(^\d{17}([0-9]|X|x)$)/,--%>
<%--    DISTRICT_MAP: {--%>
<%--          '330100': [--%>
<%--            { code: '330102', name: '上城区' },--%>
<%--            { code: '330103', name: '拱墅区' },--%>
<%--            { code: '330104', name: '西湖区' },--%>
<%--            { code: '330105', name: '滨江区' },--%>
<%--            { code: '330109', name: '萧山区' },--%>
<%--            { code: '330110', name: '余杭区' },--%>
<%--            { code: '330111', name: '富阳区' },--%>
<%--            { code: '330112', name: '临安区' },--%>
<%--            { code: '330113', name: '临平区' },--%>
<%--            { code: '330114', name: '钱塘区' },--%>
<%--            { code: '330122', name: '桐庐县' },--%>
<%--            { code: '330127', name: '淳安县' },--%>
<%--            { code: '330182', name: '建德市' }--%>
<%--          ],--%>
<%--          '330200': [--%>
<%--            { code: '330203', name: '海曙区' },--%>
<%--            { code: '330205', name: '江北区' },--%>
<%--            { code: '330206', name: '北仑区' },--%>
<%--            { code: '330211', name: '镇海区' },--%>
<%--            { code: '330212', name: '鄞州区' },--%>
<%--            { code: '330225', name: '象山县' },--%>
<%--            { code: '330226', name: '宁海县' },--%>
<%--            { code: '330281', name: '余姚市' },--%>
<%--            { code: '330282', name: '慈溪市' },--%>
<%--            { code: '330283', name: '奉化区' }--%>
<%--          ],--%>
<%--          '330300': [--%>
<%--            { code: '330302', name: '鹿城区' },--%>
<%--            { code: '330303', name: '龙湾区' },--%>
<%--            { code: '330304', name: '瓯海区' },--%>
<%--            { code: '330305', name: '洞头区' },--%>
<%--            { code: '330324', name: '永嘉县' },--%>
<%--            { code: '330326', name: '平阳县' },--%>
<%--            { code: '330327', name: '苍南县' },--%>
<%--            { code: '330328', name: '文成县' },--%>
<%--            { code: '330329', name: '泰顺县' },--%>
<%--            { code: '330381', name: '瑞安市' },--%>
<%--            { code: '330382', name: '乐清市' },--%>
<%--            { code: '330383', name: '龙港市' }--%>
<%--          ],--%>
<%--          '330400': [--%>
<%--            { code: '330402', name: '南湖区' },--%>
<%--            { code: '330411', name: '秀洲区' },--%>
<%--            { code: '330421', name: '嘉善县' },--%>
<%--            { code: '330424', name: '海盐县' },--%>
<%--            { code: '330481', name: '海宁市' },--%>
<%--            { code: '330482', name: '平湖市' },--%>
<%--            { code: '330483', name: '桐乡市' }--%>
<%--          ],--%>
<%--          '330500': [--%>
<%--            { code: '330502', name: '吴兴区' },--%>
<%--            { code: '330503', name: '南浔区' },--%>
<%--            { code: '330521', name: '德清县' },--%>
<%--            { code: '330522', name: '长兴县' },--%>
<%--            { code: '330523', name: '安吉县' }--%>
<%--          ],--%>
<%--          '330600': [--%>
<%--            { code: '330602', name: '越城区' },--%>
<%--            { code: '330603', name: '柯桥区' },--%>
<%--            { code: '330604', name: '上虞区' },--%>
<%--            { code: '330624', name: '新昌县' },--%>
<%--            { code: '330681', name: '诸暨市' },--%>
<%--            { code: '330683', name: '嵊州市' }--%>
<%--          ],--%>
<%--          '330700': [--%>
<%--            { code: '330702', name: '婺城区' },--%>
<%--            { code: '330703', name: '金东区' },--%>
<%--            { code: '330723', name: '武义县' },--%>
<%--            { code: '330726', name: '浦江县' },--%>
<%--            { code: '330727', name: '磐安县' },--%>
<%--            { code: '330781', name: '兰溪市' },--%>
<%--            { code: '330782', name: '义乌市' },--%>
<%--            { code: '330783', name: '东阳市' },--%>
<%--            { code: '330784', name: '永康市' }--%>
<%--          ],--%>
<%--          '330800': [--%>
<%--            { code: '330802', name: '柯城区' },--%>
<%--            { code: '330803', name: '衢江区' },--%>
<%--            { code: '330822', name: '常山县' },--%>
<%--            { code: '330824', name: '开化县' },--%>
<%--            { code: '330825', name: '龙游县' },--%>
<%--            { code: '330881', name: '江山市' }--%>
<%--          ],--%>
<%--          '330900': [--%>
<%--            { code: '330902', name: '定海区' },--%>
<%--            { code: '330903', name: '普陀区' },--%>
<%--            { code: '330921', name: '岱山县' },--%>
<%--            { code: '330922', name: '嵊泗县' }--%>
<%--          ],--%>
<%--          '331000': [--%>
<%--            { code: '331002', name: '椒江区' },--%>
<%--            { code: '331003', name: '黄岩区' },--%>
<%--            { code: '331004', name: '路桥区' },--%>
<%--            { code: '331022', name: '三门县' },--%>
<%--            { code: '331023', name: '天台县' },--%>
<%--            { code: '331024', name: '仙居县' },--%>
<%--            { code: '331081', name: '温岭市' },--%>
<%--            { code: '331082', name: '临海市' },--%>
<%--            { code: '331083', name: '玉环市' }--%>
<%--          ],--%>
<%--          '331100': [--%>
<%--            { code: '331102', name: '莲都区' },--%>
<%--            { code: '331121', name: '青田县' },--%>
<%--            { code: '331122', name: '缙云县' },--%>
<%--            { code: '331123', name: '遂昌县' },--%>
<%--            { code: '331124', name: '松阳县' },--%>
<%--            { code: '331125', name: '云和县' },--%>
<%--            { code: '331126', name: '庆元县' },--%>
<%--            { code: '331127', name: '景宁畲族自治县' },--%>
<%--            { code: '331181', name: '龙泉市' }--%>
<%--          ]--%>
<%--    }--%>
<%--  };--%>

<%--  // 工具函数--%>
<%--  const Utils = {--%>
<%--    // 格式化文件大小--%>
<%--    formatBytes(bytes, decimals = 2) {--%>
<%--      if (bytes === 0) return '0 Bytes';--%>
<%--      const k = 1024;--%>
<%--      const dm = decimals < 0 ? 0 : decimals;--%>
<%--      const sizes = ['Bytes', 'KB', 'MB', 'GB'];--%>
<%--      const i = Math.floor(Math.log(bytes) / Math.log(k));--%>
<%--      return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];--%>
<%--    },--%>

<%--    // 身份证号验证(含校验位)--%>
<%--    validateIdCard(idCard) {--%>
<%--      idCard = idCard.toUpperCase();--%>
<%--      if (!Constants.ID_CARD_REGEX.test(idCard)) return false;--%>

<%--      if (idCard.length === 18) {--%>
<%--        const factor = [7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2];--%>
<%--        const parity = ['1','0','X','9','8','7','6','5','4','3','2'];--%>
<%--        let sum = 0;--%>

<%--        for (let i = 0; i < 17; i++) {--%>
<%--          sum += parseInt(idCard[i]) * factor[i];--%>
<%--        }--%>

<%--        return idCard[17] === parity[sum % 11];--%>
<%--      }--%>
<%--      return true;--%>
<%--    },--%>

<%--    // 显示通知提示--%>
<%--    showNotification(message, type = 'error') {--%>
<%--      const toast = $('#notificationToast');--%>
<%--      const icon = $('#notificationIcon');--%>
<%--      const msg = $('#notificationMessage');--%>

<%--      toast.removeClass('bg-red-500 bg-green-500')--%>
<%--              .addClass(type === 'success' ? 'bg-green-500' : 'bg-red-500');--%>

<%--      icon.removeClass('fa-check-circle fa-exclamation-circle')--%>
<%--              .addClass(type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle');--%>

<%--      msg.text(message);--%>
<%--      toast.removeClass('hidden opacity-0 translate-y-20');--%>

<%--      setTimeout(() => {--%>
<%--        toast.addClass('opacity-0 translate-y-20');--%>
<%--        setTimeout(() => toast.addClass('hidden'), 500);--%>
<%--      }, 3000);--%>
<%--    },--%>

<%--    // 自定义确认弹窗--%>
<%--    customConfirm(message, onConfirm, onCancel = () => {}) {--%>
<%--      const modal = $('#customConfirmModal');--%>
<%--      const messageEl = $('#customConfirmMessage');--%>
<%--      const confirmBtn = $('#confirmOk');--%>
<%--      const cancelBtn = $('#confirmCancel');--%>

<%--      messageEl.text(message);--%>
<%--      modal.removeClass('hidden');--%>

<%--      const closeModal = () => modal.addClass('hidden');--%>

<%--      confirmBtn.off('click').on('click', () => { closeModal(); onConfirm(); });--%>
<%--      cancelBtn.off('click').on('click', () => { closeModal(); onCancel(); });--%>
<%--      modal.off('click').on('click', (e) => {--%>
<%--        if (e.target === modal[0]) { closeModal(); onCancel(); }--%>
<%--      });--%>
<%--    }--%>
<%--  };--%>

<%--  // 表单核心功能--%>
<%--  const CompetitionForm = {--%>
<%--    init() {--%>
<%--      this.bindEvents();--%>
<%--      this.initFormData();--%>
<%--      this.initFilePreviews();--%>
<%--      this.setupCharacterCount();--%>
<%--    },--%>

<%--    // 绑定所有事件--%>
<%--    bindEvents() {--%>
<%--      // 地区联动--%>
<%--      $('#competitionCity').on('change', () => this.handleCityChange());--%>

<%--      // 分组显示控制--%>
<%--      $('#competitionGroup').on('change', () => this.handleGroupChange());--%>
<%--      $('#competitionSubgroup').on('change', () => this.handleSubgroupChange());--%>

<%--      // 动态添加成员/导师--%>
<%--      $('#addMember').on('click', () => this.addDynamicRow('member'));--%>
<%--      $('#addMentor').on('click', () => this.addDynamicRow('mentor'));--%>

<%--      // 表单提交--%>
<%--      $('#submitApply').on('click', () => this.submitForm('submit'));--%>
<%--      $('#saveDraft').on('click', () => this.submitForm('draft'));--%>

<%--      // 返回按钮--%>
<%--      $('#backToFormStatus').on('click', () => this.handleBack());--%>

<%--      // 事件委托 - 移除成员/导师--%>
<%--      $('#projectMembers').on('click', '.remove-member', (e) => this.removeDynamicRow(e, 'member'));--%>
<%--      $('#mentors').on('click', '.remove-mentor', (e) => this.removeDynamicRow(e, 'mentor'));--%>

<%--      // 输入验证--%>
<%--      this.bindValidationEvents();--%>
<%--    },--%>

<%--    // 初始化表单数据--%>
<%--    initFormData() {--%>
<%--      debugger--%>
<%--      const regData = JSON.parse('${regJson}');--%>
<%--      if (!regData) return;--%>

<%--      // 基础信息填充--%>
<%--      Object.keys(regData).forEach(key => {--%>
<%--        const $el = $('#' + key + ', [name="' + key + '"]');--%>
<%--        if ($el.is('select, input[type="radio"]')) {--%>
<%--          $el.val(regData[key]);--%>
<%--        } else if ($el.length) {--%>
<%--          $el.val(regData[key]);--%>
<%--        }--%>
<%--      });--%>

<%--      // 日期格式化--%>
<%--      const dateFields = ['firstApplicantBirthday', 'firstApplicantGraduationTime', 'companyEstablishTime'];--%>
<%--      dateFields.forEach(field => {--%>
<%--        var $el = $('#' + field);--%>
<%--        if ($el.val()) $el.val(this.formatDate($el.val()));--%>
<%--      });--%>

<%--      // 团队成员初始化--%>
<%--      this.initDynamicData('member', regData.projectMembers);--%>

<%--      // 导师初始化--%>
<%--      this.initDynamicData('mentor', regData.mentors);--%>
<%--    },--%>

<%--    // 初始化文件预览--%>
<%--    initFilePreviews() {--%>
<%--      // 固定文件预览--%>
<%--      const fixedFiles = [--%>
<%--        {input: '#firstApplicantIdCardFrontFile', preview: '#frontFilePreview'},--%>
<%--        {input: '#firstApplicantIdCardBackFile', preview: '#backFilePreview'},--%>
<%--        {input: '#companyBusinessLicenseFile', preview: '#businessLicensePreview'},--%>
<%--        {input: '#projectPlanFile', preview: '#planFilePreview'}--%>
<%--      ];--%>

<%--      fixedFiles.forEach(item => this.setupFilePreview($(item.input), $(item.preview)));--%>

<%--      // 动态成员文件预览--%>
<%--      this.initMemberFilePreviews();--%>
<%--    },--%>

<%--    // 初始化成员文件预览--%>
<%--    initMemberFilePreviews() {--%>
<%--      $('.member-row').each(function() {--%>
<%--        const $row = $(this);--%>
<%--        CompetitionForm.setupFilePreview(--%>
<%--                $row.find('.member-id-card-front'),--%>
<%--                $row.find('.member-front-file-preview')--%>
<%--        );--%>
<%--        CompetitionForm.setupFilePreview(--%>
<%--                $row.find('.member-id-card-back'),--%>
<%--                $row.find('.member-back-file-preview')--%>
<%--        );--%>
<%--      });--%>
<%--    },--%>

<%--    // 文件预览通用设置--%>
<%--    setupFilePreview($input, $preview) {--%>
<%--      $input.on('change', function() {--%>
<%--        const file = this.files[0];--%>
<%--        if (!file) return;--%>

<%--        // 文件大小验证--%>
<%--        const maxSize = $input.attr('id') === 'projectPlanFile'--%>
<%--                ? Constants.MAX_PLAN_FILE_SIZE--%>
<%--                : ($input.attr('id') === 'companyBusinessLicenseFile'--%>
<%--                        ? Constants.MAX_LICENSE_SIZE--%>
<%--                        : Constants.MAX_ID_CARD_SIZE);--%>

<%--        if (file.size > maxSize) {--%>
<%--          Utils.showNotification('文件大小超过限制，最大支持' + Utils.formatBytes(maxSize), 'error');--%>
<%--          this.value = '';--%>
<%--          return;--%>
<%--        }--%>

<%--        // 预览处理--%>
<%--        const reader = new FileReader();--%>
<%--        reader.onload = (e) => {--%>
<%--          if (file.type.match('image.*')) {--%>
<%--            $preview.find('img').attr('src', e.target.result).show();--%>
<%--          } else {--%>
<%--            $preview.find('#planFileName').text(file.name);--%>
<%--          }--%>
<%--          $preview.removeClass('hidden');--%>
<%--        };--%>
<%--        reader.readAsDataURL(file);--%>
<%--      });--%>
<%--    },--%>

<%--    // 处理城市选择变化--%>
<%--    handleCityChange() {--%>
<%--      const city = $('#competitionCity').val();--%>
<%--      const $district = $('#competitionDistrict');--%>
<%--      $district.empty().append('<option value="">请选择区县</option>');--%>

<%--      if (city && Constants.DISTRICT_MAP[city]) {--%>
<%--        Constants.DISTRICT_MAP[city].forEach(district => {--%>
<%--          $district.append('<option value="'+district.code+'">'+district.name+'</option>');--%>
<%--        });--%>
<%--      }--%>
<%--    },--%>

<%--    // 处理分组变化--%>
<%--    handleGroupChange() {--%>
<%--      const isRural = $('#competitionGroup').val() === '3';--%>
<%--      $('#ruralSubgroup').toggle(isRural).find('select').attr('required', isRural);--%>
<%--    },--%>

<%--    // 处理子分组变化--%>
<%--    handleSubgroupChange() {--%>
<%--      const isCompanyNeeded = ['startup', 'growth'].includes($('#competitionSubgroup').val());--%>
<%--      $('#companyInfo').toggle(isCompanyNeeded);--%>
<%--    },--%>

<%--    // 动态添加行(成员/导师)--%>
<%--    addDynamicRow(type) {--%>
<%--      const config = {--%>
<%--        member: {--%>
<%--          container: '#projectMembers',--%>
<%--          template: '.member-row:first',--%>
<%--          max: Constants.MAX_MEMBERS,--%>
<%--          error: '最多只能添加4名团队成员'--%>
<%--        },--%>
<%--        mentor: {--%>
<%--          container: '#mentors',--%>
<%--          template: '.mentor-row:first',--%>
<%--          max: Constants.MAX_MENTORS,--%>
<%--          error: '最多只能添加2位指导老师'--%>
<%--        }--%>
<%--      };--%>

<%--      const {container, template, max, error} = config[type];--%>
<%--      const $container = $(container);--%>

<%--      if ($container.find('.'+type+'-row').length >= max) {--%>
<%--        Utils.showNotification(error, 'error');--%>
<%--        return;--%>
<%--      }--%>

<%--      const $newRow = $(template).clone().removeClass('hidden').show();--%>
<%--      $newRow.find('input, select, textarea').val('');--%>
<%--      $newRow.find('input[type="radio"]').prop('checked', false);--%>

<%--      $container.append($newRow);--%>
<%--      if (type === 'member') this.initFilePreviews();--%>
<%--    },--%>

<%--    // 移除动态行--%>
<%--    removeDynamicRow(e, type) {--%>
<%--      e.preventDefault();--%>
<%--      const $row = $(e.target).closest('.'+type+'-row');--%>
<%--      const $container = $row.parent();--%>

<%--      if ($container.find('.'+type+'-row').length <= 1) {--%>
<%--        $row.find('input, select, textarea').val('');--%>
<%--        $row.find('input[type="radio"]').prop('checked', false);--%>
<%--      } else {--%>
<%--        $row.remove();--%>
<%--      }--%>
<%--    },--%>

<%--    // 绑定验证事件--%>
<%--    bindValidationEvents() {--%>
<%--      // 手机号验证--%>
<%--      $('#firstApplicantMobile, .member-contact').on('input blur', (e) => {--%>
<%--        this.validateMobile($(e.target));--%>
<%--      });--%>

<%--      // 身份证验证--%>
<%--      $('#firstApplicantIdCard, .member-id-card').on('input blur', (e) => {--%>
<%--        this.validateIdCard($(e.target));--%>
<%--      });--%>
<%--    },--%>

<%--    // 手机号验证--%>
<%--    validateMobile($el) {--%>
<%--      const value = $el.val().trim();--%>
<%--      const isValid = Constants.MOBILE_REGEX.test(value);--%>
<%--      const $error = $el.next('.error-message');--%>

<%--      $el.toggleClass('form-input-error', !isValid && value);--%>
<%--      $error.toggle(!isValid && value).text('请输入有效的手机号');--%>
<%--      return isValid;--%>
<%--    },--%>

<%--    // 身份证验证--%>
<%--    validateIdCard($el) {--%>
<%--      const value = $el.val().trim();--%>
<%--      const isValid = Utils.validateIdCard(value);--%>
<%--      const $error = $el.next('.error-message');--%>

<%--      $el.toggleClass('form-input-error', !isValid && value);--%>
<%--      $error.toggle(!isValid && value).text('请输入有效的身份证号');--%>
<%--      return isValid;--%>
<%--    },--%>

<%--    // 表单整体验证--%>
<%--    validateForm() {--%>
<%--      let isValid = true;--%>

<%--      // 验证必填项--%>
<%--      $('[required]').each((i, el) => {--%>
<%--        const $el = $(el);--%>
<%--        if (!$el.val() && $el.is(':visible')) {--%>
<%--          $el.addClass('form-input-error');--%>
<%--          isValid = false;--%>
<%--        } else {--%>
<%--          $el.removeClass('form-input-error');--%>
<%--        }--%>
<%--      });--%>

<%--      // 验证手机号和身份证--%>
<%--      if (!this.validateMobile($('#firstApplicantMobile'))) isValid = false;--%>
<%--      if (!this.validateIdCard($('#firstApplicantIdCard'))) isValid = false;--%>

<%--      // 验证文件上传--%>
<%--      const requiredFiles = [--%>
<%--        {input: '#firstApplicantIdCardFrontFile', error: '#firstApplicantIdCardFrontError'},--%>
<%--        {input: '#firstApplicantIdCardBackFile', error: '#firstApplicantIdCardBackError'},--%>
<%--        {input: '#projectPlanFile', error: '#projectPlanFileError'}--%>
<%--      ];--%>

<%--      // 企业信息文件验证--%>
<%--      if ($('#companyInfo').is(':visible')) {--%>
<%--        requiredFiles.push({--%>
<%--          input: '#companyBusinessLicenseFile',--%>
<%--          error: '#companyBusinessLicenseError'--%>
<%--        });--%>
<%--      }--%>

<%--      requiredFiles.forEach(({input, error}) => {--%>
<%--        const hasFile = $(input).next('input[type="hidden"]').val();--%>
<%--        $(error).toggle(!hasFile).text('请上传必要文件');--%>
<%--        isValid = isValid && hasFile;--%>
<%--      });--%>

<%--      return isValid;--%>
<%--    },--%>

<%--    // 提交表单--%>
<%--    submitForm(type) {--%>
<%--      if (!this.validateForm()) return;--%>

<%--      const formData = this.collectFormData();--%>
<%--      const url = type === 'submit'--%>
<%--              ? '${ctx}/apiAct2025/submit'--%>
<%--              : '${ctx}/apiAct2025/saveDraft';--%>

<%--      this.disableSubmitButtons();--%>

<%--      $.ajax({--%>
<%--        url,--%>
<%--        type: 'POST',--%>
<%--        contentType: 'application/json',--%>
<%--        data: JSON.stringify(formData),--%>
<%--        success: (response) => {--%>
<%--          Utils.showNotification(type === 'submit' ? '报名提交成功！' : '草稿保存成功！', 'success');--%>
<%--          if (type === 'submit') {--%>
<%--            setTimeout(() => window.location.href = '${ctx}/apiAct2025/formStatus', 1500);--%>
<%--          }--%>
<%--          this.enableSubmitButtons();--%>
<%--        },--%>
<%--        error: () => {--%>
<%--          Utils.showNotification('操作失败，请重试', 'error');--%>
<%--          this.enableSubmitButtons();--%>
<%--        }--%>
<%--      });--%>
<%--    },--%>

<%--    // 收集表单数据--%>
<%--    collectFormData() {--%>
<%--      const data = {};--%>

<%--      // 基础字段--%>
<%--      $('#competitionForm').serializeArray().forEach(item => {--%>
<%--        if (!item.name.includes('member-gender-')) {--%>
<%--          data[item.name] = item.value;--%>
<%--        }--%>
<%--      });--%>

<%--      // 团队成员--%>
<%--      data.projectMembers = this.collectDynamicData('member');--%>

<%--      // 导师--%>
<%--      data.mentors = this.collectDynamicData('mentor');--%>

<%--      return data;--%>
<%--    },--%>

<%--    // 收集动态数据(成员/导师)--%>
<%--    collectDynamicData(type) {--%>
<%--      const $rows = $(type === 'member' ? '#projectMembers .member-row' : '#mentors .mentor-row');--%>
<%--      const result = [];--%>

<%--      $rows.each((i, row) => {--%>
<%--        const $row = $(row);--%>
<%--        const item = type === 'member'--%>
<%--                ? {--%>
<%--                  name: $row.find('.member-name').val(),--%>
<%--                  contact: $row.find('.member-contact').val(),--%>
<%--                  gender: $row.find('input[name="member-gender-' + i + '"]:checked').val() || '',--%>
<%--                  position: $row.find('.member-position').val(),--%>
<%--                  idCard: $row.find('.member-id-card').val(),--%>
<%--                  birthday: $row.find('.member-birthday').val(),--%>
<%--                  idCardFront: $row.find('.member-id-card-front').next('input').val() || '',--%>
<%--                  idCardBack: $row.find('.member-id-card-back').next('input').val() || ''--%>
<%--                }--%>
<%--                : {--%>
<%--                  name: $row.find('.mentor-name').val(),--%>
<%--                  position: $row.find('.mentor-position').val()--%>
<%--                };--%>

<%--        if (Object.values(item).some(v => v)) result.push(item);--%>
<%--      });--%>

<%--      return JSON.stringify(result);--%>
<%--    },--%>

<%--    // 初始化动态数据(成员/导师)--%>
<%--    initDynamicData(type, data) {--%>
<%--      if (!data || !data.length) return;--%>

<%--      const $container = $(type === 'member' ? '#projectMembers' : '#mentors');--%>
<%--      $container.empty();--%>

<%--      data.forEach((item, i) => {--%>
<%--        this.addDynamicRow(type);--%>
<%--        const $row = $container.find('.'+type+'-row').eq(i);--%>

<%--        if (type === 'member') {--%>
<%--          $row.find('.member-name').val(item.name);--%>
<%--          $row.find('.member-contact').val(item.contact);--%>
<%--          $row.find('input[name="member-gender-' + i + '"][value="' + item.gender + '"]').prop('checked', true);--%>
<%--          $row.find('.member-position').val(item.position);--%>
<%--          $row.find('.member-id-card').val(item.idCard);--%>
<%--          $row.find('.member-birthday').val(item.birthday);--%>

<%--          // 身份证预览--%>
<%--          if (item.idCardFront) {--%>
<%--            $row.find('.member-front-preview-image').attr('src', item.idCardFront);--%>
<%--            $row.find('.member-front-file-preview').removeClass('hidden');--%>
<%--          }--%>
<%--          if (item.idCardBack) {--%>
<%--            $row.find('.member-back-preview-image').attr('src', item.idCardBack);--%>
<%--            $row.find('.member-back-file-preview').removeClass('hidden');--%>
<%--          }--%>
<%--        } else {--%>
<%--          $row.find('.mentor-name').val(item.name);--%>
<%--          $row.find('.mentor-position').val(item.position);--%>
<%--        }--%>
<%--      });--%>
<%--    },--%>

<%--    // 字数统计--%>
<%--    setupCharacterCount() {--%>
<%--      const fields = [--%>
<%--        {id: 'projectBrief', counter: 'briefCounter', max: 200},--%>
<%--        {id: 'industryCompetitiveAdvantage', counter: 'advantageCounter', max: 1000},--%>
<%--        {id: 'socialBenefits', counter: 'socialBenefitsCounter', max: 1000},--%>
<%--        {id: 'teamQuality', counter: 'teamQualityCounter', max: 1000},--%>
<%--        {id: 'financialOperation', counter: 'financialOperationCounter', max: 1000},--%>
<%--        {id: 'marketProspect', counter: 'marketProspectCounter', max: 1000},--%>
<%--        {id: 'productService', counter: 'productServiceCounter', max: 1000}--%>
<%--      ];--%>

<%--      fields.forEach(function(field) {--%>
<%--        var id = field.id;--%>
<%--        var counter = field.counter;--%>
<%--        var max = field.max;--%>
<%--        $('#' + id).on('input', function() {--%>
<%--          var length = this.value.length;--%>
<%--          $('#' + counter).text(length + '/' + max);--%>
<%--        });--%>
<%--      });--%>
<%--    },--%>

<%--    // 处理返回--%>
<%--    handleBack() {--%>
<%--      Utils.customConfirm(--%>
<%--              '确定要返回吗？未保存的数据将会丢失',--%>
<%--              () => window.location.href = '${ctx}/apiAct2025/formStatus'--%>
<%--      );--%>
<%--    },--%>

<%--    // 禁用提交按钮--%>
<%--    disableSubmitButtons() {--%>
<%--      $('#saveDraft, #submitApply').prop('disabled', true).addClass('opacity-50 cursor-not-allowed');--%>
<%--    },--%>

<%--    // 启用提交按钮--%>
<%--    enableSubmitButtons() {--%>
<%--      $('#saveDraft, #submitApply').prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');--%>
<%--    },--%>

<%--    // 日期格式化--%>
<%--    formatDate(dateStr) {--%>
<%--      if (!dateStr) return '';--%>
<%--      const date = new Date(dateStr);--%>
<%--      return date.toISOString().split('T')[0];--%>
<%--    }--%>
<%--  };--%>

<%--  // 页面加载完成后初始化--%>
<%--  $(document).ready(() => {--%>
<%--    CompetitionForm.init();--%>
<%--  });--%>
<%--</script>--%>

<!-- 自定义确认弹窗 -->
<div id="customConfirmModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-xl p-6 max-w-md w-full mx-4 shadow-lg text-center">
    <h3 class="text-lg font-bold text-neutral-700 mb-2">提示</h3>
    <p id="customConfirmMessage" class="text-neutral-600 mb-6">你确定要执行此操作吗？</p>
    <div class="flex justify-end gap-3">
      <button id="confirmCancel" class="btn-secondary px-4 py-2">取消</button>
      <button id="confirmOk" class="btn-primary px-4 py-2">确认</button>
    </div>
  </div>
</div>

</html>


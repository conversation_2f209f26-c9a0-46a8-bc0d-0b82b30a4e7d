<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no">
    <c:set var="ctxStatic" value="${pageContext.request.contextPath}/static"/>
    <c:set var="ctx" value="${pageContext.request.contextPath}"/>
    <title>创青春大赛报名</title>
    <style>
        *{
            margin: 0;
            padding: 0;
            font-family: "novecento_widelight","微软雅黑",Arial,Helvetica;
        }
        img{
            border: none;
        }
        ul li,ol li{
            list-style: none;
        }
        h1,h2,h3,strong,b{
            font-weight: normal;
        }
        em{
            font-style: normal;
        }
        a{
            border: none;
            -webkit-tap-highlight-color: transparent;
            -webkit-tap-highlight-color: transparent;
            text-decoration: none;
            color: #333333;
        }
        a:focus{
            outline: none;
        }
        input,select,textarea{
            border: none;
            outline: none;
        }
        textarea{
            resize: none;
        }
        i{
            display: block;
        }
        .fl{
            float: left;
        }
        .fr{
            float: right;
        }
        .clear:after{
            clear: both;
            content: ".";
            display: block;
            font-size: 0;
            height: 0;
            visibility: hidden;
            _zoom: 1;
        }
        input[type=button],input[type=submit],input[type=file],input[type=reset],button{
            cursor: pointer;
            -webkit-appearance: none;
        }
        .index-bg{
            display: block;
            width: 100%;
        }
    </style>
</head>
<body>
<a href="javascript:location.href='${ctx}/apiAct2025/formStatus';">
    <img class="index-bg" src="${ctxStatic}/act2025/img/bg.jpg" alt="">
</a>
</body>
</html>
    
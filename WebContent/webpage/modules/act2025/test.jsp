<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.jeeplus.modules.api.util.DES" %>
<%@ page import="java.net.URLEncoder" %>
<%@ page import="com.jeeplus.common.config.Global" %>
<%--
    JSP版“浙里办”登录测试页
    工作原理:
    1. 页面包含一个HTML表单，用户填写原始数据后提交。
    2. 表单提交到本页面自身(POST请求)。
    3. JSP的Java代码块接收到POST请求后，执行加密逻辑：
       - 直接调用项目中的 com.jeeplus.modules.api.util.DES 类。
       - 对数据进行加密。
       - 拼接成最终的登录URL。
    4. 将生成的URL在页面下方显示为一个可点击的链接。
--%>

<%
    request.setCharacterEncoding("UTF-8");
    // 初始化变量，用于存储生成的登录链接和回填表单
    String loginUrl = null;
    String zlbId_param = "zlb_test_user_67890";
    String identity_param = "330102199503075432";
    String mobile_param = "13900139000";
    String name_param = "李四";

    // 判断是否为表单提交的POST请求
    if ("POST".equalsIgnoreCase(request.getMethod())) {
        // 从提交的表单中获取数据
        zlbId_param = request.getParameter("zlbId");
        identity_param = request.getParameter("identity");
        mobile_param = request.getParameter("mobile");
        name_param = request.getParameter("name");

        // --- 开始执行与后端完全一致的加密逻辑 ---

        // 1. 实例化DES工具类
        DES des = new DES();
        // 2. 设置与后端相同的密钥
        des.setKey("qczj15203!@1%d");

        // 3. 对各字段进行加密
        String encryptedIdentity = des.encrypt(identity_param);
        String encryptedMobile = des.encrypt(mobile_param);
        String encryptedName = des.encrypt(name_param);

        // 4. 构建目标URL
        // 获取应用的根路径，使URL更灵活
        String contextPath = request.getContextPath();
        String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + contextPath;
        String targetEndpoint = baseUrl + "/apiAct2025/zlbLogin";

        // 5. 拼接最终的URL
        loginUrl = String.format("%s?zlbId=%s&identity=%s&mobile=%s&name=%s",
                targetEndpoint,
                zlbId_param, // zlbId不加密
                encryptedIdentity,
                encryptedMobile,
                encryptedName
        );
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>浙里办登录接口测试页面 (JSP版)</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 20px auto; padding: 0 20px; }
        h1, h2 { color: #0056b3; }
        .container { background-color: #f9f9f9; padding: 25px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 15px; }
        label { display: block; font-weight: bold; margin-bottom: 5px; }
        input[type="text"] { width: calc(100% - 20px); padding: 10px; border: 1px solid #ccc; border-radius: 4px; }
        input[type="submit"] { background-color: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        input[type="submit"]:hover { background-color: #0056b3; }
        #result { margin-top: 20px; padding: 15px; background-color: #e9ecef; border: 1px solid #ced4da; border-radius: 4px; }
        #result a { color: #007bff; text-decoration: none; word-break: break-all; }
        #result a:hover { text-decoration: underline; }
        .info { background-color: #e2f0ff; border-left: 5px solid #007bff; padding: 10px; margin-top: 20px;}
    </style>
</head>
<body>

<div class="container">
    <h1>浙里办登录接口测试页面 (JSP版)</h1>
    <p>通过在服务器端调用Java加密类生成请求，确保与接口的解密逻辑完全匹配。</p>

    <form method="POST" action="">
        <h2>输入测试数据</h2>
        <div class="form-group">
            <label for="zlbId">浙里办ID (zlbId) - (此项不加密)</label>
            <input type="text" id="zlbId" name="zlbId" value="<%= zlbId_param %>">
        </div>
        <div class="form-group">
            <label for="identity">身份证号 (identity) - (将被加密)</label>
            <input type="text" id="identity" name="identity" value="<%= identity_param %>">
        </div>
        <div class="form-group">
            <label for="mobile">手机号 (mobile) - (将被加密)</label>
            <input type="text" id="mobile" name="mobile" value="<%= mobile_param %>">
        </div>
        <div class="form-group">
            <label for="name">姓名 (name) - (将被加密)</label>
            <input type="text" id="name" name="name" value="<%= name_param %>">
        </div>
        <input type="submit" value="生成登录链接">
    </form>

    <%-- 只在生成了URL之后才显示这个结果区域 --%>
    <% if (loginUrl != null) { %>
    <div id="result">
        <h3>已生成登录链接:</h3>
        <p>
            <a id="loginLink" href="<%= loginUrl %>" target="_blank"><%= loginUrl %></a>
        </p>
    </div>
    <% } %>
</div>

<div class="info">
    <strong>使用说明:</strong>
    <ol>
        <li>将此文件放入您项目的 `webapp/WEB-INF/views/` 下的某个目录。</li>
        <li>确保您有一个 Controller 方法可以访问到这个JSP页面。</li>
        <li>访问该页面，填写数据，点击“生成登录链接”按钮。</li>
        <li>页面刷新后，下方会显示出最终的URL，点击即可测试您的 `zlbLogin` 接口。</li>
    </ol>
</div>

</body>
</html>
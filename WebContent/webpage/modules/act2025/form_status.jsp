<%@ taglib prefix="from" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>赛事报名系统 - 表单状态</title>
  <c:set var="ctxStatic" value="${pageContext.request.contextPath}/static"/>
  <c:set var="ctx" value="${pageContext.request.contextPath}"/>
  <link href="${ctxStatic}/act2025/css/all.css" rel="stylesheet">
  <link href="${ctxStatic}/act2025/css/local.google.fonts.css" rel="stylesheet">
  <script src="${ctxStatic}/act2025/js/taiwind3.4.16.js"></script>
  <script src="${ctxStatic}/act2025/js/jquery-3.7.1.min.js"></script>

  <!-- Tailwind 配置 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#FF7D00',
            neutral: {
              100: '#F5F7FA',
              200: '#E5E6EB',
              300: '#C9CDD4',
              400: '#86909C',
              500: '#4E5969',
              600: '#272E3B',
              700: '#1D2129',
            },
            status: {
              pending: '#FF7D00',
              approved: '#00B42A',
              rejected: '#F53F3F',
              processing: '#165DFF'
            }
          },
          fontFamily: {
            inter: ['Inter', 'sans-serif'],
          },
        },
      }
    }
  </script>

  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .form-input-focus {
        @apply ring-2 ring-primary/30 border-primary transition-all duration-200;
      }
      .form-input-error {
        @apply ring-2 ring-red-300 border-red-300;
      }
      .form-label {
        @apply block text-sm font-medium text-neutral-600 mb-1;
      }
      .form-field {
        @apply w-full px-3 py-2 border border-neutral-200 rounded-lg focus:outline-none focus:form-input-focus;
      }
      .btn-primary {
        @apply bg-primary hover:bg-primary/90 text-white font-medium py-2 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-primary/30;
      }
      .btn-secondary {
        @apply bg-white border border-neutral-200 hover:border-neutral-300 text-neutral-600 font-medium py-2 px-6 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-neutral-200;
      }
      .btn-outline {
        @apply bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-red-500/30;
      }
      .card-hover {
        @apply transition-all duration-300 hover:shadow-md hover:-translate-y-1;
      }
      .status-preliminary {
        @apply bg-status-approved/10 text-status-approved;
      }
      .status-final {
        @apply bg-status-approved/10 text-status-approved;
      }
      .btn-gray-outline {
        @apply bg-neutral-200 hover:bg-neutral-300 text-neutral-600 font-medium py-2 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-neutral-200;
      }
    }
  </style>
</head>
<body class="font-inter bg-neutral-100 min-h-screen">
<div class="container mx-auto px-4 py-8 max-w-5xl">
  <!-- 顶部导航 -->
  <div class="flex justify-between items-center mb-8">
    <div class="flex items-center">
      <div class="text-primary text-2xl mr-2">
        <i class="fa fa-trophy"></i>
      </div>
      <h1 class="text-xl font-bold text-neutral-700">赛事报名系统</h1>
    </div>
    <div>
      <button id="logoutBtn" class="btn-outline flex items-center">
        <i class="fa fa-sign-out mr-1"></i> 退出登录
      </button>
    </div>
  </div>

  <!-- 用户信息卡片 -->
  <div class="bg-white rounded-xl shadow-sm p-5 mb-6">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
      <div class="flex items-center mb-4 md:mb-0">
        <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-4">
          <i class="fa fa-user-circle text-xl"></i>
        </div>
        <div>
          <h3 class="font-semibold text-neutral-700">${user.name}</h3>
          <p class="text-sm text-neutral-500">${user.phone}</p>
        </div>
      </div>
      <div class="flex gap-3">
        <%-- 新建报名按钮，根据是否已有报名记录来决定是否显示 --%>
        <c:if test="${!hasAnyRegistration}">
          <button id="newFormBtn"
                  onclick="window.location.href='${ctx}/apiAct2025/competitionForm?userId=${user.id}&activityId=1'"
                  class="btn-primary flex items-center">
            <i class="fa fa-plus mr-1"></i> 新建报名
          </button>
        </c:if>

          <button id="refreshListBtn" class="btn-secondary flex items-center">
            <i class="fa fa-sync mr-1"></i> 刷新
          </button>

<%--        <button id="historyFormBtn" class="btn-secondary flex items-center">--%>
<%--          <i class="fa fa-history mr-1"></i> 历史记录--%>
<%--        </button>--%>
      </div>
    </div>
  </div>

  <!-- 表单状态列表 -->
  <div class="bg-white rounded-xl shadow-sm p-5 mb-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-lg font-semibold text-neutral-700">我的报名</h2>
      <div class="relative">
        <input type="text" id="searchForm" name="keyword" value="${keyword}" class="form-field pr-10" placeholder="搜索报名项目">
        <i class="fa fa-search absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400"></i>
      </div>
    </div>

    <!-- 状态筛选 -->
    <div class="flex flex-wrap gap-2 mb-6">
      <button class="filter-btn px-3 py-1 rounded-full ${empty status ? 'bg-primary text-white' : 'bg-neutral-100 text-neutral-600'} text-sm hover:bg-neutral-200 transition-colors" data-status="">全部</button>
      <button class="filter-btn px-3 py-1 rounded-full ${status == 0 ? 'bg-primary text-white' : 'bg-neutral-100 text-neutral-600'} text-sm hover:bg-neutral-200 transition-colors" data-status="0">草稿</button>
      <button class="filter-btn px-3 py-1 rounded-full ${status == 1 ? 'bg-primary text-white' : 'bg-neutral-100 text-neutral-600'} text-sm hover:bg-neutral-200 transition-colors" data-status="1">待审核</button>
      <button class="filter-btn px-3 py-1 rounded-full ${status == 3 ? 'bg-primary text-white' : 'bg-neutral-100 text-neutral-600'} text-sm hover:bg-neutral-200 transition-colors" data-status="3">审核通过</button>
      <button class="filter-btn px-3 py-1 rounded-full ${status == -1 ? 'bg-primary text-white' : 'bg-neutral-100 text-neutral-600'} text-sm hover:bg-neutral-200 transition-colors" data-status="-1">已退回</button>
      <button class="filter-btn px-3 py-1 rounded-full ${status == 5 ? 'bg-primary text-white' : 'bg-neutral-100 text-neutral-600'} text-sm hover:bg-neutral-200 transition-colors" data-status="5">初审晋级</button>
      <button class="filter-btn px-3 py-1 rounded-full ${status == 6 ? 'bg-primary text-white' : 'bg-neutral-100 text-neutral-600'} text-sm hover:bg-neutral-200 transition-colors" data-status="6">复审晋级</button>
      <button class="filter-btn px-3 py-1 rounded-full ${status == 2 ? 'bg-primary text-white' : 'bg-neutral-100 text-neutral-600'} text-sm hover:bg-neutral-200 transition-colors" data-status="2">退赛</button>
      <button class="filter-btn px-3 py-1 rounded-full ${status == 4 ? 'bg-primary text-white' : 'bg-neutral-100 text-neutral-600'} text-sm hover:bg-neutral-200 transition-colors" data-status="4">已淘汰</button>
    </div>

    <!-- 表单列表 -->
    <div class="space-y-4">

      <!-- 动态渲染报名项 -->
      <c:choose>
        <c:when test="${not empty registrationList}">
          <c:forEach items="${registrationList}" var="registration">
            <%-- 单个报名项 --%>
            <div class="border border-neutral-200 rounded-lg p-4 card-hover">
              <!-- 项目标题与标签 -->
              <div class="flex flex-col md:flex-row md:items-center justify-between">
                <div class="mb-3 md:mb-0">
                  <h3 class="font-medium text-neutral-700 mb-1">${registration.participantProject}</h3>
                  <div class="flex flex-wrap gap-2 text-sm">
                    <span class="bg-neutral-100 text-neutral-600 px-2 py-0.5 rounded">${fns:getDictLabel(registration.competitionGroup, 'competition_group', '')}</span>
                    <span class="bg-neutral-100 text-neutral-600 px-2 py-0.5 rounded">${fns:getDictLabel(registration.projectField, 'project_field', '')}</span>
                    <span class="px-2 py-0.5 rounded
  ${registration.status == 0 ? 'bg-status-pending/10 text-status-pending' :
     registration.status == 1 ? 'bg-status-processing/10 text-status-processing' :
     registration.status == 3 ? 'bg-status-approved/10 text-status-approved' :
     registration.status == -1 ? 'bg-status-rejected/10 text-status-rejected' :
     registration.status == 5 ? 'status-preliminary' :
     registration.status == 6 ? 'status-final' :
     registration.status == 2 || registration.status == 4 ? 'bg-neutral-100 text-neutral-600' : ''}">
                        ${registration.status == 0 ? '草稿' :
                                registration.status == 1 ? '待审核' :
                                        registration.status == 3 ? '审核通过' :
                                                registration.status == -1 ? '已退回' :
                                                        registration.status == 5 ? '初审晋级' :
                                                                registration.status == 6 ? '复审晋级' :
                                                                        registration.status == 2 ? '退赛' :
                                                                                registration.status == 4 ? '已淘汰' : '未知'}
                    </span>


                  <%--                    <span class="status-badge ${registration.status == 0 ? 'status-pending' : registration.status == 1 ? 'status-processing' : registration.status == 2 ? 'status-approved' : registration.status == 3 ? 'status-rejected' : ''}">--%>
<%--                        ${registration.status == 0 ? '草稿' : registration.status == 1 ? '审核中' : registration.status == 2 ? '已通过' : registration.status == 3 ? '已拒绝' : ''}--%>
<%--                    </span>--%>
                  </div>
                </div>
                <div class="flex gap-2">
                  <c:if test="${registration.status == 0 || registration.status == -1}">
                    <a href="${pageContext.request.contextPath}/apiAct2025/competitionForm?userId=${user.id}&activityId=${registration.hdId}&id=${registration.id}" class="btn-gray-outline">
                      <i class="fa fa-edit mr-1"></i> 编辑
                    </a>
                  </c:if>

                  <c:if test="${registration.status == 0}">
                    <button class="btn-outline" onclick="deleteRegistration('${registration.id}')">
                      <i class="fa fa-trash mr-1"></i> 删除
                    </button>
                  </c:if>

                  <button class="btn-primary text-sm" onclick="viewFormDetails('${registration.id}')">
                    <i class="fa fa-eye mr-1"></i> 查看详情
                  </button>
                </div>
              </div>
              <!-- 提交信息 -->
              <div class="mt-3 pt-3 border-t border-neutral-100 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p class="text-neutral-400">提交时间</p>
                  <p class="text-neutral-600"><fmt:formatDate value="${registration.updateDate}" pattern="yyyy-MM-dd HH:mm" /></p>
                </div>
                <div>
                  <p class="text-neutral-400">申报人</p>
                  <p class="text-neutral-600">${registration.firstApplicantName}</p>
                </div>
                <div>
                  <p class="text-neutral-400">联系电话</p>
                  <p class="text-neutral-600">${registration.firstApplicantMobile}</p>
                </div>
                <div>
                  <p class="text-neutral-400">审核进度</p>
                  <!-- 更新审核进度部分 -->
                  <div class="flex items-center">
                    <div class="w-20 bg-neutral-200 rounded-full h-2 mr-2">
                      <div class="h-2 rounded-full
      ${registration.status == 0 ? 'bg-neutral-200' :
        registration.status == 1 ? 'bg-primary' :
        registration.status == 5 ? 'bg-status-approved' :
        registration.status >= 3 ? 'bg-status-approved' :
        'bg-neutral-200'}"
                           style="width: ${registration.status == 0 ? '0%' :
                                   registration.status == 1 ? '60%' :
                                           registration.status == 5 ? '80%' :
                                                   '100%'};">
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 审核意见（仅当状态为已拒绝时显示） -->
              <c:if test="${registration.status == -1}">
                <div class="mt-4 pt-3 border-t border-neutral-100 bg-status-rejected/5 p-4 rounded-lg">
                  <h5 class="font-medium text-status-rejected mb-2 flex items-center">
                    <i class="fa fa-exclamation-circle mr-2"></i> 审核意见
                  </h5>
                  <p class="text-status-rejected">${registration.rejectReason}</p>
                </div>
              </c:if>
            </div>
          </c:forEach>
        </c:when>
        <c:otherwise>
          <p class="text-center text-neutral-500">暂无报名记录</p>
        </c:otherwise>
      </c:choose>
    </div>

    <!-- 分页 -->
    <c:if test="${not empty pageInfo}">
      <div class="mt-6 flex justify-between items-center">
        <div class="text-sm text-neutral-500">
          显示 ${pageInfo.firstResult + 1}-${pageInfo.firstResult + fn:length(registrationList)} 条，共 ${pageInfo.count} 条
        </div>
        <div class="flex gap-1">
          <c:if test="${pageInfo.pageNo > 1}">
            <a href="?pageNo=1" class="w-8 h-8 flex items-center justify-center rounded border border-neutral-200 text-neutral-400 hover:border-primary hover:text-primary">
              <i class="fa fa-angle-double-left"></i>
            </a>
            <a href="?pageNo=${pageInfo.prev}" class="w-8 h-8 flex items-center justify-center rounded border border-neutral-200 text-neutral-400 hover:border-primary hover:text-primary">
              <i class="fa fa-angle-left"></i>
            </a>
          </c:if>

          <c:forEach begin="${pageInfo.first}" end="${pageInfo.last}" var="i">
            <c:choose>
              <c:when test="${pageInfo.pageNo == i}">
                <span class="w-8 h-8 flex items-center justify-center rounded bg-primary text-white">${i}</span>
              </c:when>
              <c:otherwise>
                <a href="?pageNo=${i}" class="w-8 h-8 flex items-center justify-center rounded border border-neutral-200 text-neutral-400 hover:border-primary hover:text-primary">${i}</a>
              </c:otherwise>
            </c:choose>
          </c:forEach>

          <c:if test="${pageInfo.pageNo < pageInfo.last}">
            <a href="?pageNo=${pageInfo.next}" class="w-8 h-8 flex items-center justify-center rounded border border-neutral-200 text-neutral-400 hover:border-primary hover:text-primary">
              <i class="fa fa-angle-right"></i>
            </a>
            <a href="?pageNo=${pageInfo.last}" class="w-8 h-8 flex items-center justify-center rounded border border-neutral-200 text-neutral-400 hover:border-primary hover:text-primary">
              <i class="fa fa-angle-double-right"></i>
            </a>
          </c:if>
        </div>
      </div>
    </c:if>
  </div>

  <!-- 页脚 -->
  <div class="text-center text-xs text-neutral-400">
    <p>© 2025 赛事报名系统 版权所有</p>
  </div>
</div>

<!-- 表单详情弹窗 -->
<div id="formDetailsModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl font-bold text-neutral-700" id="modalTitle">表单详情</h3>
      <button id="closeDetailsModal" class="text-neutral-400 hover:text-neutral-600">
        <i class="fa fa-times text-xl"></i>
      </button>
    </div>

    <!-- 详情内容将通过JS动态加载 -->
    <div id="formDetailsContent" class="space-y-6">
      <!-- 加载中状态 -->
      <div class="flex flex-col items-center justify-center py-12">
        <div class="w-12 h-12 border-4 border-neutral-200 border-t-primary rounded-full animate-spin mb-4"></div>
        <p class="text-neutral-500">加载中...</p>
      </div>
    </div>
  </div>
</div>

<script>



  const regionMap = {
    '330100': [
      { code: '330102', name: '上城区' },
      { code: '330103', name: '拱墅区' },
      { code: '330104', name: '西湖区' },
      { code: '330105', name: '滨江区' },
      { code: '330109', name: '萧山区' },
      { code: '330110', name: '余杭区' },
      { code: '330111', name: '富阳区' },
      { code: '330112', name: '临安区' },
      { code: '330113', name: '临平区' },
      { code: '330114', name: '钱塘区' },
      { code: '330122', name: '桐庐县' },
      { code: '330127', name: '淳安县' },
      { code: '330182', name: '建德市' }
    ],
    '330200': [
      { code: '330203', name: '海曙区' },
      { code: '330205', name: '江北区' },
      { code: '330206', name: '北仑区' },
      { code: '330211', name: '镇海区' },
      { code: '330212', name: '鄞州区' },
      { code: '330225', name: '象山县' },
      { code: '330226', name: '宁海县' },
      { code: '330281', name: '余姚市' },
      { code: '330282', name: '慈溪市' },
      { code: '330283', name: '奉化区' }
    ],
    '330300': [
      { code: '330302', name: '鹿城区' },
      { code: '330303', name: '龙湾区' },
      { code: '330304', name: '瓯海区' },
      { code: '330305', name: '洞头区' },
      { code: '330324', name: '永嘉县' },
      { code: '330326', name: '平阳县' },
      { code: '330327', name: '苍南县' },
      { code: '330328', name: '文成县' },
      { code: '330329', name: '泰顺县' },
      { code: '330381', name: '瑞安市' },
      { code: '330382', name: '乐清市' },
      { code: '330383', name: '龙港市' }
    ],
    '330400': [
      { code: '330402', name: '南湖区' },
      { code: '330411', name: '秀洲区' },
      { code: '330421', name: '嘉善县' },
      { code: '330424', name: '海盐县' },
      { code: '330481', name: '海宁市' },
      { code: '330482', name: '平湖市' },
      { code: '330483', name: '桐乡市' }
    ],
    '330500': [
      { code: '330502', name: '吴兴区' },
      { code: '330503', name: '南浔区' },
      { code: '330521', name: '德清县' },
      { code: '330522', name: '长兴县' },
      { code: '330523', name: '安吉县' }
    ],
    '330600': [
      { code: '330602', name: '越城区' },
      { code: '330603', name: '柯桥区' },
      { code: '330604', name: '上虞区' },
      { code: '330624', name: '新昌县' },
      { code: '330681', name: '诸暨市' },
      { code: '330683', name: '嵊州市' }
    ],
    '330700': [
      { code: '330702', name: '婺城区' },
      { code: '330703', name: '金东区' },
      { code: '330723', name: '武义县' },
      { code: '330726', name: '浦江县' },
      { code: '330727', name: '磐安县' },
      { code: '330781', name: '兰溪市' },
      { code: '330782', name: '义乌市' },
      { code: '330783', name: '东阳市' },
      { code: '330784', name: '永康市' }
    ],
    '330800': [
      { code: '330802', name: '柯城区' },
      { code: '330803', name: '衢江区' },
      { code: '330822', name: '常山县' },
      { code: '330824', name: '开化县' },
      { code: '330825', name: '龙游县' },
      { code: '330881', name: '江山市' }
    ],
    '330900': [
      { code: '330902', name: '定海区' },
      { code: '330903', name: '普陀区' },
      { code: '330921', name: '岱山县' },
      { code: '330922', name: '嵊泗县' }
    ],
    '331000': [
      { code: '331002', name: '椒江区' },
      { code: '331003', name: '黄岩区' },
      { code: '331004', name: '路桥区' },
      { code: '331022', name: '三门县' },
      { code: '331023', name: '天台县' },
      { code: '331024', name: '仙居县' },
      { code: '331081', name: '温岭市' },
      { code: '331082', name: '临海市' },
      { code: '331083', name: '玉环市' }
    ],
    '331100': [
      { code: '331102', name: '莲都区' },
      { code: '331121', name: '青田县' },
      { code: '331122', name: '缙云县' },
      { code: '331123', name: '遂昌县' },
      { code: '331124', name: '松阳县' },
      { code: '331125', name: '云和县' },
      { code: '331126', name: '庆元县' },
      { code: '331127', name: '景宁畲族自治县' },
      { code: '331181', name: '龙泉市' }
    ]
  };

  const $searchInput = $('#searchForm');
  const $filterBtns = $('.filter-btn');
  $(document).ready(function () {

    const confirmModal = $('#confirmModal');
    const confirmModalTitle = $('#confirmModalTitle');
    const confirmModalContent = $('#confirmModalContent');
    const confirmModalConfirmBtn = $('#confirmModalConfirmBtn');
    const confirmModalCancelBtn = $('#confirmModalCancelBtn');

    let confirmCallback = null;
    let closeOnConfirm = true;

    // 显示模态框
    window.showConfirmModal = function  (title, content, onConfirm, options = {}) {
      confirmModalTitle.text(title);
      confirmModalContent.text(content);
      confirmCallback = onConfirm;
      closeOnConfirm = options.closeOnConfirm ?? true;

      // 设置按钮文字
      confirmModalConfirmBtn.text(options.confirmText || '确认');
      confirmModalCancelBtn.text(options.cancelText || '取消');

      // 显示模态框
      confirmModal.removeClass('hidden');

      // 绑定点击事件（避免重复绑定）
      confirmModalConfirmBtn.off('click').on('click', function () {
        if (confirmCallback && confirmCallback() === false) {
          return; // 如果回调返回 false，不关闭弹窗
        }
        if (closeOnConfirm) {
          confirmModal.addClass('hidden');
        }
      });

      confirmModalCancelBtn.off('click').on('click', function () {
        confirmModal.addClass('hidden');
      });
    };

    // 隐藏模态框函数（可选）
    window.hideConfirmModal=function () {
      confirmModal.addClass('hidden');
    };

    $('#logoutBtn').on('click', function (e) {
      e.preventDefault(); // 阻止默认跳转
      showConfirmModal('确认退出登录', '您确定要退出当前账号吗？', function () {
        window.location.href = '${pageContext.request.contextPath}/apiAct2025/logout';
      });
    });

// 关闭成功提示
    $('#closeSuccessModal').on('click', function () {
      $('#logoutSuccessModal').addClass('hidden');
    });


    $searchInput.on('input', performSearch);
    $filterBtns.on('click', function () {
      $filterBtns.removeClass('bg-primary text-white').addClass('bg-neutral-100 text-neutral-600');
      $(this).removeClass('bg-neutral-100 text-neutral-600').addClass('bg-primary text-white');
      performSearch();
    });
  });
  function performSearch() {
    const keyword = $searchInput.val().trim();
    const status = $filterBtns.filter('.bg-primary').data('status');
    window.location.href = '?keyword=' + encodeURIComponent(keyword) + '&status=' + status;
  }

  function getRegionName(code) {
    if (!code) return '未知地区';

    // 优先查找精确匹配的区县
    for (const cityCode in regionMap) {
      const districts = regionMap[cityCode];
      const found = districts.find(d => d.code === code);
      if (found) {
        return found.name;
      }
    }

    // 若未找到区县，尝试返回市级名称（即 code 前6位匹配）
    // 若未找到区县，尝试返回市级名称
    const cityCodePrefix = code.substring(0, 6); // 如 330300
    const cityMap = {
      '330100': '杭州市',
      '330200': '宁波市',
      '330300': '温州市',
      '330400': '嘉兴市',
      '330500': '湖州市',
      '330600': '绍兴市',
      '330700': '金华市',
      '330800': '衢州市',
      '330900': '舟山市',
      '331000': '台州市',
      '331100': '丽水市'
    };

    if (cityMap[cityCodePrefix]) {
      return cityMap[cityCodePrefix];
    }

    return '未知地区';
  }
  // 退出登录

  // 刷新按钮点击事件
  $('#refreshListBtn').on('click', function() {
    location.reload(); // 重新加载页面
  });
  $('#closeDetailsModal').on('click', function() {
    $('#formDetailsModal').addClass('hidden');
  });
  // 查看表单详情
  // function viewFormDetails(formId) {
  //   let details = formDetails[formId];
  //   if (!details) return;
  //
  //   // 设置弹窗标题
  //   document.getElementById('modalTitle').textContent = details.title;
  //
  //   // 构建详情内容
  //   let content = '<div class="border border-neutral-200 rounded-lg p-4">';
  //   content += '<div class="flex justify-between items-start">';
  //   content += '<div>';
  //   content += '<h4 class="font-semibold text-lg text-neutral-700">' + details.title + '</h4>';
  //   content += '<div class="flex flex-wrap gap-2 mt-2 text-sm">';
  //   content += '<span class="bg-neutral-100 text-neutral-600 px-2 py-0.5 rounded">' + details.basicInfo.group + '</span>';
  //   content += '<span class="bg-neutral-100 text-neutral-600 px-2 py-0.5 rounded">' + details.basicInfo.subgroup + '</span>';
  //   content += '<span class="status-badge status-' + details.status + '">' + details.statusText + '</span>';
  //   content += '</div>';
  //   content += '</div>';
  //   content += '<div class="text-right">';
  //   content += '<p class="text-sm text-neutral-500">提交时间</p>';
  //   content += '<p class="font-medium">' + details.submitTime + '</p>';
  //   content += '</div>';
  //   content += '</div>';
  //
  //   content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
  //   content += '<h5 class="font-medium text-neutral-700 mb-3">基本信息</h5>';
  //   content += '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';
  //   content += '<div>';
  //   content += '<p class="text-sm text-neutral-500">项目名称</p>';
  //   content += '<p class="font-medium">' + details.basicInfo.projectName + '</p>';
  //   content += '</div>';
  //   content += '<div>';
  //   content += '<p class="text-sm text-neutral-500">报名渠道</p>';
  //   content += '<p class="font-medium">' + details.basicInfo.channel + '</p>';
  //   content += '</div>';
  //   content += '<div>';
  //   content += '<p class="text-sm text-neutral-500">省市地区</p>';
  //   content += '<p class="font-medium">' + details.basicInfo.city + ' ' + details.basicInfo.district + '</p>';
  //   content += '</div>';
  //   content += '<div>';
  //   content += '<p class="text-sm text-neutral-500">申报人</p>';
  //   content += '<p class="font-medium">' + details.applicant + ' (' + details.contact + ')</p>';
  //   content += '</div>';
  //   content += '</div>';
  //   content += '</div>';
  //
  //   content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
  //   content += '<h5 class="font-medium text-neutral-700 mb-3">申报人信息</h5>';
  //   content += '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';
  //   content += '<div>';
  //   content += '<p class="text-sm text-neutral-500">姓名</p>';
  //   content += '<p class="font-medium">' + details.applicantInfo.name + '</p>';
  //   content += '</div>';
  //   content += '<div>';
  //   content += '<p class="text-sm text-neutral-500">性别</p>';
  //   content += '<p class="font-medium">' + details.applicantInfo.gender + '</p>';
  //   content += '</div>';
  //   content += '<div>';
  //   content += '<p class="text-sm text-neutral-500">出生日期</p>';
  //   content += '<p class="font-medium">' + details.applicantInfo.birthday + '</p>';
  //   content += '</div>';
  //   content += '<div>';
  //   content += '<p class="text-sm text-neutral-500">身份证号</p>';
  //   content += '<p class="font-medium">' + details.applicantInfo.idCard + '</p>';
  //   content += '</div>';
  //   content += '<div>';
  //   content += '<p class="text-sm text-neutral-500">联系电话</p>';
  //   content += '<p class="font-medium">' + details.applicantInfo.mobile + '</p>';
  //   content += '</div>';
  //   content += '<div>';
  //   content += '<p class="text-sm text-neutral-500">电子邮箱</p>';
  //   content += '<p class="font-medium">' + details.applicantInfo.email + '</p>';
  //   content += '</div>';
  //   content += '<div>';
  //   content += '<p class="text-sm text-neutral-500">户籍所在地</p>';
  //   content += '<p class="font-medium">' + details.applicantInfo.hukou + '</p>';
  //   content += '</div>';
  //   content += '<div>';
  //   content += '<p class="text-sm text-neutral-500">职位</p>';
  //   content += '<p class="font-medium">' + details.applicantInfo.position + '</p>';
  //   content += '</div>';
  //   content += '</div>';
  //   content += '</div>';
  //
  //   content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
  //   content += '<h5 class="font-medium text-neutral-700 mb-3">团队成员</h5>';
  //   if (details.teamMembers.length > 0) {
  //     content += '<div class="overflow-x-auto">';
  //     content += '<table class="min-w-full divide-y divide-neutral-200">';
  //     content += '<thead>';
  //     content += '<tr>';
  //     content += '<th class="px-4 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">姓名</th>';
  //     content += '<th class="px-4 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">角色</th>';
  //     content += '<th class="px-4 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">联系方式</th>';
  //     content += '</tr>';
  //     content += '</thead>';
  //     content += '<tbody class="bg-white divide-y divide-neutral-200">';
  //     for (let i = 0; i < details.teamMembers.length; i++) {
  //       let member = details.teamMembers[i];
  //       content += '<tr>';
  //       content += '<td class="px-4 py-3 text-sm text-neutral-600">' + member.name + '</td>';
  //       content += '<td class="px-4 py-3 text-sm text-neutral-600">' + member.role + '</td>';
  //       content += '<td class="px-4 py-3 text-sm text-neutral-600">' + member.contact + '</td>';
  //       content += '</tr>';
  //     }
  //     content += '</tbody>';
  //     content += '</table>';
  //     content += '</div>';
  //   } else {
  //     content += '<p class="text-neutral-500 italic">未添加团队成员</p>';
  //   }
  //   content += '</div>';
  //
  //   content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
  //   content += '<h5 class="font-medium text-neutral-700 mb-3">项目简介</h5>';
  //   content += '<p class="text-neutral-600">' + details.projectBrief + '</p>';
  //   content += '</div>';
  //
  //   content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
  //   content += '<h5 class="font-medium text-neutral-700 mb-3">项目优势</h5>';
  //   content += '<p class="text-neutral-600">' + details.advantage + '</p>';
  //   content += '</div>';
  //
  //   if (details.rejectionReason) {
  //     content += '<div class="mt-4 pt-4 border-t border-neutral-100 bg-status-rejected/5 p-4 rounded-lg">';
  //     content += '<h5 class="font-medium text-status-rejected mb-2 flex items-center">';
  //     content += '<i class="fa fa-exclamation-circle mr-2"></i> 审核意见';
  //     content += '</h5>';
  //     content += '<p class="text-status-rejected">' + details.rejectionReason + '</p>';
  //     content += '</div>';
  //   }
  //   content += '</div>';
  //
  //   // 设置详情内容
  //   document.getElementById('formDetailsContent').innerHTML = content;
  //
  //   // 显示弹窗
  //   document.getElementById('formDetailsModal').classList.remove('hidden');
  // }

  function viewFormDetails(registrationId) {
    fetch('${pageContext.request.contextPath}/apiAct2025/get?id=' + registrationId)
            .then(response => response.json())
            .then(result => {
              if(!result.success){
                alert('获取数据失败：'+result.msg);
                return
              }
              const data =result.body.data;
              console.log(data)
              let content = '';
              // 基础信息
              content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
              content += '    <h5 class="font-medium text-neutral-700 mb-3">基础信息</h5>';
              content += '    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">';
              content += '        <div><p class="text-sm text-neutral-500">参赛项目</p><p class="font-medium">' + (data.participantProject || '') + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">报名途径</p><p class="font-medium">' + (data.registrationChannel) + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">参赛地区（市）</p><p class="font-medium">' + getRegionName(data.competitionCity) + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">参赛地区（区县）</p><p class="font-medium">' + getRegionName(data.competitionDistrict) + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">赛事分组</p><p class="font-medium">' + (data.competitionGroup || '') + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">项目分组</p><p class="font-medium">' + (data.competitionSubgroup || '') + '</p></div>';

              // 如果赛事分组为 "乡村振兴"
              if (data.competitionGroup === '乡村振兴') {
                content += '        <div><p class="text-sm text-neutral-500">乡村振兴项目分组</p><p class="font-medium">' + (data.ruralCompetitionSubgroup || '') + '</p></div>';
              }

              content += '        <div><p class="text-sm text-neutral-500">项目领域</p><p class="font-medium">' + (data.projectField || '') + '</p></div>';
              content += '    </div>';
              content += '</div>';

              // 申报人信息
              content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
              content += '    <h5 class="font-medium text-neutral-700 mb-3">第一申报人信息</h5>';
              content += '    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">';
              content += '        <div><p class="text-sm text-neutral-500">姓名</p><p class="font-medium">' + (data.firstApplicantName || '') + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">手机号</p><p class="font-medium">' + (data.firstApplicantMobile || '') + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">邮箱</p><p class="font-medium">' + (data.firstApplicantEmail || '') + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">户籍所在地</p><p class="font-medium">' + (data.firstApplicantHukou || '') + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">职位</p><p class="font-medium">' + (data.firstApplicantPosition || '') + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">性别</p><p class="font-medium">' + (data.firstApplicantGender === 1 ? '男' : '女') + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">出生年月日</p><p class="font-medium">' + formatDate(data.firstApplicantBirthday) + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">毕业时间</p><p class="font-medium">' + formatDate(data.firstApplicantGraduationTime) + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">身份证号</p><p class="font-medium">' + (data.firstApplicantIdCard || '') + '</p></div>';
              content += '        <div><p class="text-sm text-neutral-500">身份证正面（国徽面）</p>' + renderFileLink(data.firstApplicantIdCardFrontFile, '查看文件') + '</div>';
              content += '        <div><p class="text-sm text-neutral-500">身份证反面（个人信息面）</p>' + renderFileLink(data.firstApplicantIdCardBackFile, '查看文件') + '</div>';
              content += '    </div>';
              content += '</div>';

              // 项目团队成员
              try {
                const projectMembers = JSON.parse(data.projectMembers || '[]');
                if (projectMembers.length > 0) {
                  content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
                  content += '    <h5 class="font-medium text-neutral-700 mb-3">项目团队成员</h5>';
                  content += '    <div class="overflow-x-auto">';
                  content += '        <table class="min-w-full divide-y divide-neutral-200">';
                  content += '            <thead>';
                  content += '                <tr>';
                  content += '                    <th class="px-4 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">姓名</th>';
                  content += '                    <th class="px-4 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">联系方式</th>';
                  content += '                    <th class="px-4 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">性别</th>';
                  content += '                    <th class="px-4 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">所任职务</th>';
                  content += '                    <th class="px-4 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">身份证号</th>';
                  content += '                    <th class="px-4 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">出生日期</th>';
                  content += '                    <th class="px-4 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">身份证正反面</th>';
                  content += '                </tr>';
                  content += '            </thead>';
                  content += '            <tbody class="bg-white divide-y divide-neutral-200">';
                  projectMembers.forEach(member => {
                    content += '            <tr>';
                    content += '                <td class="px-4 py-3 text-sm text-neutral-600">' + (member.name || '') + '</td>';
                    content += '                <td class="px-4 py-3 text-sm text-neutral-600">' + (member.contact || '') + '</td>';
                    content += '                <td class="px-4 py-3 text-sm text-neutral-600">' + (member.gender == '1' ? '男' : member.gender == '2' ? '女' : '') + '</td>';
                    content += '                <td class="px-4 py-3 text-sm text-neutral-600">' + (member.position || '') + '</td>';
                    content += '                <td class="px-4 py-3 text-sm text-neutral-600">' + (member.idCard || '') + '</td>';
                    content += '                <td class="px-4 py-3 text-sm text-neutral-600">' + formatDate(member.birthday) + '</td>';
                    content += '                <td class="px-4 py-3 text-sm text-neutral-600">';
                    content += '                    正面：' + renderFileLink(member.idCardFront, '查看') + '<br>';
                    content += '                    反面：' + renderFileLink(member.idCardBack, '查看');
                    content += '                </td>';
                    content += '            </tr>';
                  });
                  content += '            </tbody>';
                  content += '        </table>';
                  content += '    </div>';
                  content += '</div>';
                } else {
                  content += '<div class="mt-4 pt-4 border-t border-neutral-100"><p class="text-neutral-500 italic">未添加团队成员</p></div>';
                }
              } catch (e) {
                console.error("解析团队成员信息失败", e);
              }

              // 指导老师信息
              try {
                const mentors = JSON.parse(data.mentors || '[]');
                if (mentors.length > 0) {
                  content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
                  content += '    <h5 class="font-medium text-neutral-700 mb-3">指导老师信息</h5>';
                  content += '    <div class="overflow-x-auto">';
                  content += '        <table class="min-w-full divide-y divide-neutral-200">';
                  content += '            <thead>';
                  content += '                <tr>';
                  content += '                    <th class="px-4 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">姓名</th>';
                  content += '                    <th class="px-4 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">职务</th>';
                  content += '                </tr>';
                  content += '            </thead>';
                  content += '            <tbody class="bg-white divide-y divide-neutral-200">';
                  mentors.forEach(mentor => {
                    content += '            <tr>';
                    content += '                <td class="px-4 py-3 text-sm text-neutral-600">' + (mentor.name || '') + '</td>';
                    content += '                <td class="px-4 py-3 text-sm text-neutral-600">' + (mentor.position || '') + '</td>';
                    content += '            </tr>';
                  });
                  content += '            </tbody>';
                  content += '        </table>';
                  content += '    </div>';
                  content += '</div>';
                }
              } catch (e) {
                console.error("解析指导老师信息失败", e);
              }

              // 公司信息（仅当 competitionSubgroup 为 初创组 或 成长组 时显示）
              if (data.competitionSubgroup === '初创组' || data.competitionSubgroup === '成长组') {
                content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
                content += '    <h5 class="font-medium text-neutral-700 mb-3">公司信息</h5>';
                content += '    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">';
                content += '        <div><p class="text-sm text-neutral-500">公司名称</p><p class="font-medium">' + (data.companyName || '') + '</p></div>';
                content += '        <div><p class="text-sm text-neutral-500">公司地址</p><p class="font-medium">' + (data.companyAddress || '') + '</p></div>';
                content += '        <div><p class="text-sm text-neutral-500">成立时间</p><p class="font-medium">' + formatDate(data.companyEstablishTime) + '</p></div>';
                content += '        <div><p class="text-sm text-neutral-500">营业执照</p>' + renderFileLink(data.companyBusinessLicenseFile, '查看营业执照') + '</div>';
                content += '    </div>';
                content += '</div>';
              }


              // 项目信息
              content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
              content += '    <h5 class="font-medium text-neutral-700 mb-3">项目简介</h5>';
              content += '    <p class="text-neutral-600">' + (data.projectBrief || '') + '</p>';
              content += '</div>';

              content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
              content += '    <h5 class="font-medium text-neutral-700 mb-3">同行业竞争优势</h5>';
              content += '    <p class="text-neutral-600">' + (data.industryCompetitiveAdvantage || '') + '</p>';
              content += '</div>';

              content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
              content += '    <h5 class="font-medium text-neutral-700 mb-3">社会效益</h5>';
              content += '    <p class="text-neutral-600">' + (data.socialBenefits || '') + '</p>';
              content += '</div>';

              content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
              content += '    <h5 class="font-medium text-neutral-700 mb-3">团队素质</h5>';
              content += '    <p class="text-neutral-600">' + (data.teamQuality || '') + '</p>';
              content += '</div>';

              content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
              content += '    <h5 class="font-medium text-neutral-700 mb-3">财务运营</h5>';
              content += '    <p class="text-neutral-600">' + (data.financialOperation || '') + '</p>';
              content += '</div>';

              content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
              content += '    <h5 class="font-medium text-neutral-700 mb-3">市场前景</h5>';
              content += '    <p class="text-neutral-600">' + (data.marketProspect || '') + '</p>';
              content += '</div>';

              content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
              content += '    <h5 class="font-medium text-neutral-700 mb-3">产品服务</h5>';
              content += '    <p class="text-neutral-600">' + (data.productService || '') + '</p>';
              content += '</div>';

              content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
              content += '    <h5 class="font-medium text-neutral-700 mb-3">项目计划书</h5>';
              content += '    <p class="text-neutral-600">' + renderFileLink(data.projectPlanFile, '下载项目计划书') + '</p>';
              content += '</div>';

              // 声明信息
              content += '<div class="mt-4 pt-4 border-t border-neutral-100">';
              content += '    <h5 class="font-medium text-neutral-700 mb-3">声明信息</h5>';
              content += '    <div>';
              content += '        <p class="text-neutral-600">是否同意于线上平台公开展示本项目：<strong>' + (data.isAgreePublic === 1 ? '是' : '否') + '</strong></p>';
              content += '    </div>';
              content += '</div>';


              $('#formDetailsContent').html(content);
              $('#formDetailsModal').removeClass('hidden');
            })
            .catch(error => {
              console.error('获取报名详情失败:', error);
              alert('无法加载报名详情');
            });
  }

  // 根据状态码返回对应的样式类
  function getStatusClass(status) {
    switch (status) {
      case 0: return 'bg-status-pending/10 text-status-pending'; // 草稿
      case 1: return 'bg-status-processing/10 text-status-processing'; // 待审核
      case 3: return 'bg-status-approved/10 text-status-approved'; // 审核通过
      case -1: return 'bg-status-rejected/10 text-status-rejected'; // 已退回
      case 5: return 'status-preliminary'; // 初审晋级
      case 6: return 'status-final'; // 复审晋级
      case 2: return 'bg-neutral-100 text-neutral-600'; // 退赛
      case 4: return 'bg-status-rejected/10 text-status-rejected'; // 已淘汰
      default: return 'bg-neutral-100 text-neutral-600';
    }
  }


  // 根据状态码返回对应文本
  function getStatusBarLabel(status) {
    switch (status) {
      case 0: return '草稿';
      case 1: return '待审核';
      case 3: return '审核通过';
      case -1: return '已退回';
      case 5: return '初审晋级';
      case 6: return '复审晋级';
      case 2: return '退赛';
      case 4: return '已淘汰';
      default: return '未知';
    }
  }


  // 日期格式化
  function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toISOString().slice(0, 10); // YYYY-MM-DD
  }

  // 文件链接渲染函数
  function renderFileLink(fileUrl, linkText) {
    if (!fileUrl) return '未上传';
    return '<a href="' + fileUrl + '" target="_blank" class="text-primary hover:underline">' + linkText + '</a>';
  }

  function deleteRegistration  (id) {
    showConfirmModal('确认删除报名', '此操作将永久删除该报名信息，是否继续？', function () {
        $.post('${ctx}/apiAct2025/deleteRegistration', { id: id }, function(response) {
          if (response.success) {
            location.reload(); // 删除成功后刷新页面
          } else {
            showError('删除失败: ' + response.msg || '未知错误');
          }
        }).fail(function() {
          showError('删除失败，请重试');
        });
    });
  }
</script>

<!-- 通用确认模态框 -->
<div id="confirmModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-xl p-6 max-w-md w-full mx-4 shadow-lg text-center">
    <h3 id="confirmModalTitle" class="text-lg font-bold text-neutral-700 mb-4">确认操作</h3>
    <p id="confirmModalContent" class="text-neutral-600 mb-6">您确定要执行此操作吗？</p>
    <div class="flex justify-end gap-3">
      <button id="confirmModalCancelBtn" class="btn-secondary px-4 py-2">取消</button>
      <button id="confirmModalConfirmBtn" class="btn-primary px-4 py-2">确认</button>
    </div>
  </div>
</div>


<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> 
    <title>创青春大赛报名</title>
    <c:set var="ctxStatic" value="${pageContext.request.contextPath}/static"/>
    <c:set var="ctx" value="${pageContext.request.contextPath}"/>
    <style>

        .container {
            position: relative;
            width: 100%;
            height: 100vh;
        }

        .image-section {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('${ctxStatic}/act2025/img/cqc2025v2.jpg') no-repeat center top;
            background-size: 100% 100%;
            background-color: rgba(240, 240, 240, 0.9);
            z-index: 1;
        }

        .button-section {
            position: absolute;
            top: 80%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2;
            padding: 20px;
            background-color: rgba(255, 255, 255, 0.9); /* 可选：增加背景透明度 */
            border-radius: 10px;
        }

        .btn-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .btn-register {
            padding: 16px 48px;
            font-size: 18px;
            font-weight: bold;
            background-color: white;
            color: #1E90FF;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            min-width: 280px;
            width: 80%;
            max-width: 400px;
        }

        .btn-register:hover {
            background-color: #f5f5f5;
            transform: translateY(-5px);
            box-shadow: 0 12px 20px rgba(0,0,0,0.3);
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box; 
        }
        body, html {
            width: 100%;
            height: 100%;
            overflow: hidden; 
        }
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        .image-section {
            flex: 3; 
            background: url('${ctxStatic}/act2025/img/cqc2025v2.jpg') no-repeat center top; /* 修改为顶部对齐 */
            background-size: 100% 100%; /* 强制拉伸图片，确保长图在水平和垂直方向都填满容器 */
            object-fit: cover;
            background-color: rgba(240, 240, 240, 0.9); /* 修改为浅灰色背景 */
        }
        .button-section {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            background-color: rgba(240, 240, 240, 0.9); /* 修改为浅灰色背景 */
        }
        .btn-icon {
            font-size: 24px; /* 图标大小 */
            margin-bottom: 8px; /* 图标和文字间距 */
        }
        .btn-register {
            padding: 16px 48px;
            font-size: 18px;
            font-weight: bold;
            background-color: white;
            color: #1E90FF;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            min-width: 280px;
            width: 80%;
            max-width: 400px;
        }
        .btn-register:hover {
            background-color: #f5f5f5;
            transform: translateY(-5px);
            box-shadow: 0 12px 20px rgba(0,0,0,0.3);
        }
        @media (max-width: 768px) {
            .image-section {
                background-size: cover; /* 移动端优先保证垂直方向铺满屏幕 */
                background-position: center top; /* 长图在小屏幕上优先展示顶部内容 */
            }
            .button-section {
                flex: 1;
                padding: 10px;
            }
            .btn-register {
                padding: 14px 40px;
                font-size: 16px;
                min-width: 240px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="image-section"></div>
        <div class="button-section">
            <button class="btn-register" onclick="location.href='${ctx}/apiAct2025/formStatus'">
                <span class="btn-icon">📝</span> <!-- 使用emoji作为图标 -->
                <span>立即报名</span>
            </button>
        </div>
    </div>
</body>
</html>
    
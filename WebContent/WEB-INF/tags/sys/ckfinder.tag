<%@ tag language="java" pageEncoding="UTF-8"%>
<%@ include file="/webpage/include/taglib.jsp"%>
<link rel="stylesheet" type="text/css" href="${ctxStatic}/kindEditor/themes/default/default.css" />
<link rel="stylesheet" type="text/css" href="${ctxStatic}/kindEditor/plugins/jcrop/jquery.Jcrop.css" />
<script charset="utf-8" type="text/javascript" src="${ctxStatic}/kindEditor/kindeditor.js"></script>
<script charset="utf-8" type="text/javascript" src="${ctxStatic}/kindEditor/lang/zh_CN.js"></script>
<script charset="utf-8" type="text/javascript" src="${ctxStatic}/kindEditor/plugins/jcrop/jquery.Jcrop.js"></script>
<%@ attribute name="input" type="java.lang.String" required="true" description="输入框"%>
<%@ attribute name="type" type="java.lang.String" required="true" description="files、images、flash、thumb"%>
<%@ attribute name="uploadPath" type="java.lang.String" required="true" description="打开文件管理的上传路径"%>
<%@ attribute name="selectMultiple" type="java.lang.Boolean" required="false" description="是否允许多选"%>
<%@ attribute name="readonly" type="java.lang.Boolean" required="false" description="是否查看模式"%>
<%@ attribute name="enableJcrop" type="java.lang.Boolean" required="false" description="是否启用裁剪"%>
<%@ attribute name="maxWidth" type="java.lang.String" required="false" description="最大宽度"%>
<%@ attribute name="maxHeight" type="java.lang.String" required="false" description="最大高度"%>
<ol id="${input}Preview"></ol><c:if test="${!readonly}"><a href="javascript:" id="${input}FinderOpen" class="btn btn-primary">${selectMultiple?'添加':'选择'}</a>&nbsp;<a href="javascript:" onclick="${input}DelAll();" class="btn btn-default">清除</a></c:if>
<script type="text/javascript">
    KindEditor.ready(function(K) {
        var editor = K.editor({
            cssPath : '${ctxStatic}/kindeditor/plugins/code/prettify.css',
            uploadJson : '${ctx}/file/upload?type=${type}',
            allowFileManager : true
        });
        K('#${input}FinderOpen').click(
            function() {
                editor.loadPlugin('${type}', function() {
                    editor.plugin.imageDialog({
                        showRemote : false,
                        imageUrl : K('#F_Main_Pic').val(),
                        enableJcrop: '${enableJcrop}',
                        clickFn : function(url, title, width, height,border, align) {
                            ${input}SelectAction(url);
                            editor.hideDialog();
                        }
                    });
                });
            });
    });
    function ${input}SelectAction(url){
        $("#${input}").val($("#${input}").val()+($("#${input}").val()==""?url:"|"+url));
        ${input}Preview();
    }
    function ${input}ThumbSelectAction(fileUrl, data, allFiles){
        var url="", files=ckfinderAPI.getSelectedFiles();
        for(var i=0; i<files.length; i++){
            url += files[i].getThumbnailUrl();
            if (i<files.length-1) url+="|";
        }//<c:if test="${selectMultiple}">
        $("#${input}").val($("#${input}").val()+($("#${input}").val(url)==""?url:"|"+url));//</c:if><c:if test="${!selectMultiple}">
        $("#${input}").val(url);//</c:if>
        ${input}Preview();
        //top.$.jBox.close();
    }
    function ${input}Callback(api){
        ckfinderAPI = api;
    }
    function ${input}Del(obj){
        var url = $(obj).prev().attr("url");
        $("#${input}").val($("#${input}").val().replace("|"+url,"","").replace(url+"|","","").replace(url,"",""));
        ${input}Preview();
    }
    function ${input}DelAll(){
        $("#${input}").val("");
        ${input}Preview();
    }
    function ${input}Preview(){
        var li, urls = $("#${input}").val().split("|");
        $("#${input}Preview").children().remove();
        for (var i=0; i<urls.length; i++){
            if (urls[i]!=""){//<c:if test="${type eq 'thumb' || type eq 'images'}">
                li = "<li><a href=\""+urls[i]+"\" target=\"_blank\" url=\""+urls[i]+"\"><img src=\""+urls[i]+"\" url=\""+urls[i]+"\" style=\"width:${empty width ? 150 : width}px;height:${empty height ? 100 : height}px;_height:${empty maxHeight ? 200 : maxHeight}px;border:0;padding:3px;\"></a>";//</c:if><c:if test="${type ne 'thumb' && type ne 'images'}">
                li = "<li><a href=\""+urls[i]+"\" url=\""+urls[i]+"\" target=\"_blank\">"+decodeURIComponent(urls[i].substring(urls[i].lastIndexOf("/")+1))+"</a>";//</c:if>
                li += "&nbsp;&nbsp;<c:if test="${!readonly}"><a href=\"javascript:\" onclick=\"${input}Del(this);\">×</a></c:if></li>";
                $("#${input}Preview").append(li);
            }
        }
        if ($("#${input}Preview").text() == ""){
            $("#${input}Preview").html("<li style='list-style:none;padding-top:5px;'>无</li>");
        }
    }
    ${input}Preview();
</script>
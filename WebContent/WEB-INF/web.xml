<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://java.sun.com/xml/ns/javaee" xmlns:web="http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd" xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd" version="2.5">
  <display-name>qqchuang</display-name>
  <context-param>
    <param-name>contextConfigLocation</param-name>
    <param-value>classpath:spring-context*.xml</param-value>
  </context-param>
  <listener>
    <listener-class>com.jeeplus.modules.sys.listener.WebContextListener</listener-class>
  </listener>
  <listener>
    <listener-class>org.springframework.web.context.request.RequestContextListener</listener-class>
  </listener>
  <!-- <filter>
    <filter-name>startFilter</filter-name>
    <filter-class>com.jeeplus.common.websocket.WebSockertFilter</filter-class>
  </filter> -->

  <!--  防止sql注入攻击 start-->
<!--  <filter>-->
<!--    <filter-name>sqlInjectionFilter</filter-name>-->
<!--    <filter-class>com.jeeplus.common.filter.SqlInjectionFilter</filter-class>-->
<!--    <init-param>-->
<!--      <param-name>excludeUrls</param-name>-->
<!--      <param-value>/login</param-value>-->
<!--    </init-param>-->
<!--  </filter>-->
<!--  <filter-mapping>-->
<!--    <filter-name>sqlInjectionFilter</filter-name>-->
<!--    <url-pattern>/*</url-pattern>-->
<!--  </filter-mapping>-->
  <!--防止sql注入攻击 end-->

  <filter>
    <filter-name>encodingFilter</filter-name>
    <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
    <init-param>
      <param-name>encoding</param-name>
      <param-value>UTF-8</param-value>
    </init-param>
    <init-param>
      <param-name>forceEncoding</param-name>
      <param-value>true</param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>encodingFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>
  <filter>
    <filter-name>shiroFilter</filter-name>
    <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    <init-param>
      <param-name>targetFilterLifecycle</param-name>
      <param-value>true</param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>shiroFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>
  <filter>
    <filter-name>sitemeshFilter</filter-name>
    <filter-class>com.opensymphony.sitemesh.webapp.SiteMeshFilter</filter-class>
  </filter>
  <filter-mapping>
    <filter-name>sitemeshFilter</filter-name>
    <url-pattern>/a/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>sitemeshFilter</filter-name>
    <url-pattern>/f/*</url-pattern>
  </filter-mapping>
  <servlet>
    <servlet-name>springServlet</servlet-name>
    <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
    <init-param>
      <param-name>contextConfigLocation</param-name>
      <param-value>classpath*:/spring-mvc*.xml</param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
  </servlet>
  <servlet-mapping>
    <servlet-name>springServlet</servlet-name>
    <url-pattern>/</url-pattern>
  </servlet-mapping>
  <servlet>
    <servlet-name>RestletServlet</servlet-name>
    <servlet-class>org.restlet.ext.servlet.ServerServlet</servlet-class>
    <init-param>
      <param-name>org.restlet.application</param-name>
      <param-value>com.jeeplus.modules.act.rest.ActRestApplication</param-value>
    </init-param>
  </servlet>
  <servlet-mapping>
    <servlet-name>RestletServlet</servlet-name>
    <url-pattern>/act/rest/service/*</url-pattern>
  </servlet-mapping>
  <servlet>
    <servlet-name>DruidStatView</servlet-name>
    <servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
    <init-param>
      <param-name>allow</param-name>
      <param-value>127.0.0.1</param-value>
    </init-param>
  </servlet>
  <servlet-mapping>
    <servlet-name>DruidStatView</servlet-name>
    <url-pattern>/druid/*</url-pattern>
  </servlet-mapping>
  <servlet>
    <servlet-name>CKFinderConnectorServlet</servlet-name>
    <servlet-class>com.jeeplus.common.web.CKFinderConnectorServlet</servlet-class>
    <init-param>
      <param-name>XMLConfig</param-name>
      <param-value>/WEB-INF/ckfinder.xml</param-value>
    </init-param>
    <init-param>
      <param-name>debug</param-name>
      <param-value>false</param-value>
    </init-param>
    <init-param>
      <param-name>configuration</param-name>
      <param-value>com.jeeplus.common.web.CKFinderConfig</param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
  </servlet>
  <servlet-mapping>
    <servlet-name>CKFinderConnectorServlet</servlet-name>
    <url-pattern>/static/ckfinder/core/connector/java/connector.java</url-pattern>
  </servlet-mapping>
  <filter>
    <filter-name>FileUploadFilter</filter-name>
    <filter-class>com.ckfinder.connector.FileUploadFilter</filter-class>
    <init-param>
      <param-name>sessionCookieName</param-name>
      <param-value>JSESSIONID</param-value>
    </init-param>
    <init-param>
      <param-name>sessionParameterName</param-name>
      <param-value>jsessionid</param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>FileUploadFilter</filter-name>
    <url-pattern>/static/ckfinder/core/connector/java/connector.java</url-pattern>
  </filter-mapping>
  <servlet>
    <servlet-name>UserfilesDownloadServlet</servlet-name>
    <servlet-class>com.jeeplus.common.servlet.UserfilesDownloadServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>UserfilesDownloadServlet</servlet-name>
    <url-pattern>/userfiles/*</url-pattern>
  </servlet-mapping>
  <servlet>
    <servlet-name>ValidateCodeServlet</servlet-name>
    <servlet-class>com.jeeplus.common.servlet.ValidateCodeServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>ValidateCodeServlet</servlet-name>
    <url-pattern>/servlet/validateCodeServlet</url-pattern>
  </servlet-mapping>
  <error-page>
    <error-code>500</error-code>
    <location>/webpage/error/500.jsp</location>
  </error-page>
  <error-page>
    <error-code>404</error-code>
    <location>/webpage/error/404.jsp</location>
  </error-page>
</web-app>
<?xml version="1.0" encoding="UTF-8" ?>

<taglib xmlns="http://java.sun.com/xml/ns/j2ee"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd"
  version="2.0">
    
  <description>JSTL 1.1 functions library</description>
  <display-name>JSTL functions sys</display-name>
  <tlib-version>1.1</tlib-version>
  <short-name>fns</short-name>
  <uri>http://java.sun.com/jsp/jstl/functionss</uri>

  <function>
    <description>获取管理路径</description>
    <name>getAdminPath</name>
    <function-class>com.jeeplus.common.config.Global</function-class>
    <function-signature>java.lang.String getAdminPath()</function-signature>
    <example>${fns:getAdminPath()}</example>
  </function>
  <function>
    <description>获取网站路径</description>
    <name>getFrontPath</name>
    <function-class>com.jeeplus.common.config.Global</function-class>
    <function-signature>java.lang.String getFrontPath()</function-signature>
    <example>${fns:getFrontPath()}</example>
  </function>
  <function>
    <description>获取网站URL后缀</description>
    <name>getUrlSuffix</name>
    <function-class>com.jeeplus.common.config.Global</function-class>
    <function-signature>java.lang.String getUrlSuffix()</function-signature>
    <example>${fns:getUrlSuffix()}</example>
  </function>
  <function>
    <description>获取配置</description>
    <name>getConfig</name>
    <function-class>com.jeeplus.common.config.Global</function-class>
    <function-signature>java.lang.String getConfig(java.lang.String)</function-signature>
    <example>${fns:getConfig(key)}</example>
  </function>
  <function>
    <description>获取常量</description>
    <name>getConst</name>
    <function-class>com.jeeplus.common.config.Global</function-class>
    <function-signature>java.lang.Object getConst(java.lang.String)</function-signature>
    <example>${fns:getConst(key)}</example>
  </function>
  
  <!-- UserUtils -->
  <function>
    <description>获取当前用户对象</description>
    <name>getUser</name>
    <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
    <function-signature>com.jeeplus.modules.sys.entity.User getUser()</function-signature>
    <example>${fns:getUser()}</example>  
  </function>
  
  <function>
    <description>根据编码获取用户对象</description>
    <name>getUserById</name>
    <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
    <function-signature>com.jeeplus.modules.sys.entity.User get(java.lang.String)</function-signature>
    <example>${fns:getUserById(id)}</example>  
  </function>
  
  <function>
    <description>获取授权用户信息</description>
    <name>getPrincipal</name>
    <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
    <function-signature>com.jeeplus.modules.sys.security.SystemAuthorizingRealm.Principal getPrincipal()</function-signature>
    <example>${fns:getPrincipal()}</example>  
  </function>
  
  <function>
    <description>获取当前用户的菜单对象列表</description>
    <name>getMenuList</name>
    <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
    <function-signature>java.util.List getMenuList()</function-signature>
    <example>${fns:getMenuList()}</example>  
  </function>
  
    <function>
    <description>获取当前用户的菜单对象列表</description>
    <name>getTopMenu</name>
    <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
    <function-signature>com.jeeplus.modules.sys.entity.Menu getTopMenu()</function-signature>
    <example>${fns:getTopMenu()}</example>  
  </function>
   <function>
    <description>获取当前时间与指定时间的差值</description>
    <name>getTime</name>
    <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
    <function-signature>java.util.Date getTime(java.util.Date)</function-signature>
    <example>${fns:getTime(java.util.Date)}</example>  
  </function>
  <function>
    <description>获取当前用户的区域对象列表</description>
    <name>getAreaList</name>
    <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
    <function-signature>java.util.List getAreaList()</function-signature>
    <example>${fns:getAreaList()}</example>  
  </function>
  
  <function>
    <description>获取当前用户的部门对象列表</description>
    <name>getOfficeList</name>
    <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
    <function-signature>java.util.List getOfficeList()</function-signature>
    <example>${fns:getOfficeList()}</example>  
  </function>
  
  <function>
    <description>获取当前用户缓存</description>
    <name>getCache</name>
    <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
    <function-signature>java.lang.Object getCache(java.lang.String, java.lang.Object)</function-signature>
    <example>${fns:getCache(cacheName, defaultValue)}</example>  
  </function>

    <function>
        <description>判断当前登录用户是否是管理员</description>
        <name>isAdmin</name>
        <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
        <function-signature>java.lang.Boolean isAdmin()</function-signature>
        <example>${fns:isAdmin()}</example>
    </function>

  <!-- DictUtils -->
  <function>
    <description>获取字典标签</description>
    <name>getDictLabel</name>
    <function-class>com.jeeplus.modules.sys.utils.DictUtils</function-class>
    <function-signature>java.lang.String getDictLabel(java.lang.String, java.lang.String, java.lang.String)</function-signature>
    <example>${fns:getDictLabel(value, type, defaultValue)}</example>  
  </function>
  
  <function>
    <description>获取字典标签(多个)</description>
    <name>getDictLabels</name>
    <function-class>com.jeeplus.modules.sys.utils.DictUtils</function-class>
    <function-signature>java.lang.String getDictLabels(java.lang.String, java.lang.String, java.lang.String)</function-signature>
    <example>${fns:getDictLabels(values, type, defaultValue)}</example>  
  </function>

  <function>
    <description>获取字典值</description>
    <name>getDictValue</name>
    <function-class>com.jeeplus.modules.sys.utils.DictUtils</function-class>
    <function-signature>java.lang.String getDictValue(java.lang.String, java.lang.String, java.lang.String)</function-signature>
    <example>${fns:getDictValue(label, type, defaultValue)}</example>  
  </function>
  
  <function>
    <description>获取字典对象列表</description>
    <name>getDictList</name>
    <function-class>com.jeeplus.modules.sys.utils.DictUtils</function-class>
    <function-signature>java.util.List getDictList(java.lang.String)</function-signature>
    <example>${fns:getDictList(type)}</example>  
  </function>
  
  <function>
    <description>获取字典对象列表</description>
    <name>getDictListJson</name>
    <function-class>com.jeeplus.modules.sys.utils.DictUtils</function-class>
    <function-signature>java.lang.String getDictListJson(java.lang.String)</function-signature>
    <example>${fns:getDictListJson(type)}</example>  
  </function>
  
  <!-- Encodes -->
  <function>
    <description>URL编码</description>
    <name>urlEncode</name>
    <function-class>com.jeeplus.common.utils.Encodes</function-class>
    <function-signature>java.lang.String urlEncode(java.lang.String)</function-signature>
    <example>${fns:urlEncode(part)}</example>  
  </function>
  <function>
    <description>URL解码</description>
    <name>urlDecode</name>
    <function-class>com.jeeplus.common.utils.Encodes</function-class>
    <function-signature>java.lang.String urlDecode(java.lang.String)</function-signature>
    <example>${fns:urlDecode(part)}</example>  
  </function>
  <function>
    <description>HTML编码</description>
    <name>escapeHtml</name>
    <function-class>com.jeeplus.common.utils.Encodes</function-class>
    <function-signature>java.lang.String escapeHtml(java.lang.String)</function-signature>
    <example>${fns:escapeHtml(html)}</example>  
  </function>
  <function>
    <description>HTML解码</description>
    <name>unescapeHtml</name>
    <function-class>com.jeeplus.common.utils.Encodes</function-class>
    <function-signature>java.lang.String unescapeHtml(java.lang.String)</function-signature>
    <example>${fns:unescapeHtml(html)}</example>  
  </function>
  
  <!-- StringUtils -->
  <function>
    <description>从后边开始截取字符串</description>
    <name>substringAfterLast</name>
    <function-class>org.apache.commons.lang3.StringUtils</function-class>
    <function-signature>java.lang.String substringAfterLast(java.lang.String, java.lang.String)</function-signature>
    <example>${fns:substringAfterLast(str,separator)}</example>  
  </function>
  <function>
    <description>判断字符串是否以某某开头</description>
    <name>startsWith</name>
    <function-class>org.apache.commons.lang3.StringUtils</function-class>
    <function-signature>boolean startsWith(java.lang.CharSequence, java.lang.CharSequence)</function-signature>
    <example>${fns:startsWith(str,prefix)}</example> 
  </function>
  <function>
    <description>判断字符串是否以某某结尾</description>
    <name>endsWith</name>
    <function-class>org.apache.commons.lang3.StringUtils</function-class>
    <function-signature>boolean endsWith(java.lang.CharSequence, java.lang.CharSequence)</function-signature>
    <example>${fns:endsWith(str,suffix)}</example> 
  </function>
  <function>
    <description>缩写字符串，超过最大宽度用“...”表示</description>
    <name>abbr</name>
    <function-class>com.jeeplus.common.utils.StringUtils</function-class>
    <function-signature>java.lang.String abbr(java.lang.String, int)</function-signature>
    <example>${fns:abbr(str,length)}</example>  
  </function>
  <function>
    <description>替换掉HTML标签</description>
    <name>replaceHtml</name>
    <function-class>com.jeeplus.common.utils.StringUtils</function-class>
    <function-signature>java.lang.String replaceHtml(java.lang.String)</function-signature>
    <example>${fns:replaceHtml(html)}</example>  
  </function>
  <function>
    <description>转换为JS获取对象值，生成三目运算返回结果。</description>
    <name>jsGetVal</name>
    <function-class>com.jeeplus.common.utils.StringUtils</function-class>
    <function-signature>java.lang.String jsGetVal(java.lang.String)</function-signature>
    <example>${fns:jsGetVal('row.user.id')}  返回：!row?'':!row.user?'':!row.user.id?'':row.user.id</example>  
  </function>
  <function>
    <description>获取标签值。</description>
    <name>getLabels</name>
    <function-class>com.jeeplus.common.utils.StringUtils</function-class>
    <function-signature>java.lang.String getLabels(java.lang.String)</function-signature>
    <example>${fns:getLabels(value)}</example>
  </function>


  <!-- DateUtils -->
  <function>
    <description>获取当前日期</description>
    <name>getDate</name>
    <function-class>com.jeeplus.common.utils.DateUtils</function-class>
    <function-signature>java.lang.String getDate(java.lang.String)</function-signature>
    <example>${fns:getDate(pattern)}</example>  
  </function>
  <function>
    <description>获取过去的天数</description>
    <name>pastDays</name>
    <function-class>com.jeeplus.common.utils.DateUtils</function-class>
    <function-signature>long pastDays(java.util.Date)</function-signature>
    <example>${fns:pastDays(date)}</example>  
  </function>
  <function>
    <description>将日期对象Date转为标准格式字符串yyyy-mm-dd hh:mm:ss显示</description>
    <name>formatDateTime</name>
    <function-class>com.jeeplus.common.utils.DateUtils</function-class>
    <function-signature>java.lang.String formatDateTime(java.util.Date)</function-signature>
    <example>${fns:formatDateTime(date)}</example>  
  </function>
  <!-- JsonMapper -->
  <function>
    <description>对象转换JSON字符串</description>
    <name>toJson</name>
    <function-class>com.jeeplus.common.mapper.JsonMapper</function-class>
    <function-signature>java.lang.String toJsonString(java.lang.Object)</function-signature>
    <example>${fns:toJson(object)}</example>  
  </function>
  
  <function>
    <description>根据编码获取用户对象</description>
    <name>getByLoginName</name>
    <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
    <function-signature>com.jeeplus.modules.sys.entity.User getByLoginName(java.lang.String)</function-signature>
    <example>${fns:getByLoginName(loginName)}</example>  
  </function>

   <function>
       <description>密码加密</description>
       <name>entryptPassword</name>
       <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
       <function-signature>java.lang.String entryptPassword(java.lang.String)</function-signature>
       <example>${fns:entryptPassword(password)}</example>
   </function>

    <function>
        <description>加密身份证</description>
        <name>entryptIdCard</name>
        <function-class>com.jeeplus.modules.sys.utils.UserUtils</function-class>
        <function-signature>java.lang.String entryptIdCard(java.lang.String)</function-signature>
        <example>${entryptIdCard(IdCard)}</example>
    </function>
</taglib>

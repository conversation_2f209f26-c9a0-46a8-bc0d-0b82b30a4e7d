<config>
	<enabled>true</enabled>
	<baseDir></baseDir>
	<baseURL>/userfiles/</baseURL>
	<licenseKey></licenseKey>
	<licenseName></licenseName>
	<imgWidth>1024</imgWidth>
	<imgHeight>768</imgHeight>
	<imgQuality>80</imgQuality>
	<uriEncoding>UTF-8</uriEncoding>
	<forceASCII>false</forceASCII>
    <disallowUnsafeCharacters>false</disallowUnsafeCharacters>
	<userRoleSessionVar>CKFinder_UserRole</userRoleSessionVar>
	<checkDoubleExtension>true</checkDoubleExtension>
	<checkSizeAfterScaling>true</checkSizeAfterScaling>
	<secureImageUploads>true</secureImageUploads>
	<htmlExtensions>html,htm,xml,js</htmlExtensions>
	<hideFolders>
		<folder>.*</folder>
		<folder>CVS</folder>
	</hideFolders>
	<hideFiles>
		<file>.*</file>
	</hideFiles>
	<defaultResourceTypes></defaultResourceTypes>
	<types>
		<type name="files">
			<url>%BASE_URL%files/</url>
			<directory>%BASE_DIR%files</directory>
			<maxSize>5M</maxSize>
			<allowedExtensions>7z,aiff,asf,avi,bmp,csv,doc,docx,fla,flv,gif,gz,gzip,jpeg,jpg,mid,mov,mp3,mp4,mpc,mpeg,mpg,ods,odt,pdf,png,ppt,pptx,pxd,qt,ram,rar,rm,rmi,rmvb,rtf,sdc,sitd,swf,sxc,sxw,tar,tgz,tif,tiff,txt,vsd,wav,wma,wmv,xls,xlsx,zip</allowedExtensions>
			<deniedExtensions></deniedExtensions>
		</type>
		<type name="images">
			<url>%BASE_URL%images/</url>
			<directory>%BASE_DIR%images</directory>
			<maxSize>2M</maxSize>
			<allowedExtensions>bmp,gif,jpeg,jpg,png</allowedExtensions>
			<deniedExtensions></deniedExtensions>
		</type>
		<type name="flash">
			<url>%BASE_URL%flash/</url>
			<directory>%BASE_DIR%flash</directory>
			<maxSize>2M</maxSize>
			<allowedExtensions>swf,flv</allowedExtensions>
			<deniedExtensions></deniedExtensions>
		</type>
		<type name="qrcode">
			<url>%BASE_URL%qrcode/</url>
			<directory>%BASE_DIR%qrcode</directory>
			<maxSize>2M</maxSize>
			<allowedExtensions>bmp,gif,jpeg,jpg,png</allowedExtensions>
			<deniedExtensions></deniedExtensions>
		</type>
	</types>
	<accessControls>
		<accessControl>
			<role>*</role>
			<resourceType>*</resourceType>
			<folder>/</folder>
			<folderView>false</folderView>
			<folderCreate>false</folderCreate>
			<folderRename>false</folderRename>
			<folderDelete>false</folderDelete>
			<fileView>false</fileView>
			<fileUpload>false</fileUpload>
			<fileRename>false</fileRename>
			<fileDelete>false</fileDelete>
		</accessControl>
	</accessControls>
	<thumbs>
		<enabled>true</enabled>
		<url>%BASE_URL%_thumbs/</url>
		<directory>%BASE_DIR%_thumbs</directory>
		<directAccess>true</directAccess>
		<maxWidth>320</maxWidth>
		<maxHeight>240</maxHeight>
		<quality>80</quality>
	</thumbs>
	<plugins>
		<plugin>
			<name>imageresize</name>
			<class>com.ckfinder.connector.plugins.ImageResize</class>
			<params>
				<param name="smallThumb" value="90x90"></param>
				<param name="mediumThumb" value="120x120"></param>
				<param name="largeThumb" value="180x180"></param>
			</params>
		</plugin>
		<plugin>
			<name>fileeditor</name>
			<class>com.ckfinder.connector.plugins.FileEditor</class>
			<params></params>
		</plugin>
	</plugins>
	<basePathBuilderImpl>com.ckfinder.connector.configuration.ConfigurationPathBuilder</basePathBuilderImpl>
</config>

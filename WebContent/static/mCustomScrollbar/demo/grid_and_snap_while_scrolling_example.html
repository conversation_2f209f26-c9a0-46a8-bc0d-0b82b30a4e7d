<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>jQuery custom scrollbar demo</title>
  <!-- style for demo and examples -->
  <style>
    body{margin:0; padding:0; color:#eee; background:#222; font-family:Verdana,Geneva,sans-serif; font-size:13px; line-height:20px;}
    a:link,a:visited,a:hover{color:inherit;}
    h1{font-family:Georgia,serif; font-size:18px; font-style:italic; margin:40px; color:#26beff;}
    p{margin:0 0 20px 0;}
    hr{height:0; border:none; border-bottom:1px solid rgba(255,255,255,0.13); border-top:1px solid rgba(0,0,0,1); margin:9px 10px; clear:both;}
    .links{margin:10px;}
    .links a{display:inline-block; padding:3px 15px; margin:7px 10px; background:#444; text-decoration:none; -webkit-border-radius:15px; -moz-border-radius:15px; border-radius:15px;}
    .links a:hover{background:#eb3755; color:#fff;}
    .output{margin:20px 40px;}
    code{color:#5b70ff;}
    a[rel='toggle-buttons-scroll-type']{display:inline-block; text-decoration:none; padding:3px 15px; -webkit-border-radius:15px; -moz-border-radius:15px; border-radius:15px; background:#000; margin:5px 20px 5px 0;}
    .content{margin:40px; width:910px; height:500px; overflow:hidden;}
    #content_1{width:910px;height:480px;}
    .grid{table-layout:fixed;border-collapse:separate;border-style:none;border-spacing:0;width:0;}
    .grid th {overflow: hidden;white-space: nowrap;padding: 0;text-align: center;vertical-align: middle;width: 90px;max-width: 90px;font-size: 11px;border: 1px solid #cbcbcb;border-width: 0 1px 1px 0;background-color: #f3f3f3;color: #222;}
    .grid td {overflow: hidden;white-space: nowrap;width: 90px;max-width: 90px;height: 16px;line-height: 16px;font-size: 13px;border: 1px solid #cbcbcb;border-width: 0 1px 1px 0;text-align: right;vertical-align: middle;padding: 1px 2px;background-color: #fff;color: #000;}
    .grid th:first-child, .grid td:first-child {width: 148px;text-align: left;background-color: #f3f3f3;color: #222;padding-left: 4px;}
  </style>
  <!-- Custom scrollbars CSS -->
  <link href="jquery.mCustomScrollbar.css" rel="stylesheet" />
</head>
<body>
  <p class="links">
    <a href="http://manos.malihu.gr">malihu</a>
    <a href="http://manos.malihu.gr/jquery-custom-content-scroller">Plugin home</a>
    <a href="complete_examples.html">Plugin demo</a>
  </p>
  <hr />
  <h1>Example of snapping to a grid while scrolling</h1>
  <!-- content block -->
  <div class="content">
    <table class="grid">
      <thead>
        <tr>
          <th>A</th>
          <th>B</th>
          <th>C</th>
          <th>D</th>
          <th>E</th>
          <th>F</th>
          <th>G</th>
          <th>H</th>
          <th>I</th>
        </tr>
      </thead>
    </table>
    <div id="content_1">
      <table id="grid" class="grid">
        <thead>
          <tr class="shim">
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
          </tr>
        </thead>
        <tbody>
        </tbody>
      </table>
    </div>
  </div>
  <hr />
  <p>&nbsp;</p>
  <!-- Google CDN jQuery with fallback to local -->
  <script src="http://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
  <script>!window.jQuery && document.write(unescape('%3Cscript src="js/minified/jquery-1.9.1.min.js"%3E%3C/script%3E'))</script>
  <!-- custom scrollbars plugin -->
  <script src="jquery.mCustomScrollbar.js"></script>
  <script>
    (function($){
      $(window).load(function(){
        var $grid_body = $('#grid tbody'), html;
        for (var i = 0; i < 100; i++) {
          var label = String.fromCharCode("A".charCodeAt(0) + (i / 26) | 0) +
                      String.fromCharCode("A".charCodeAt(0) + (i % 26));
          html = "<tr><td>" + label + "</td>";
          for (var j = 0; j < 9; j++) {
            html += "<td>" + Math.round(Math.random() * 100) + "</td>";
          }
          html += "</tr>";
          $grid_body.append(html);
        }

        $("#content_1").mCustomScrollbar({
          theme:"light",
          scrollInertia:0,
          mouseWheelPixels:19,
          snapAmount:19,
          snapOffset: 1,
          scrollButtons:{
            enable:true,
            scrollType:"pixels",
            scrollAmount:19
          },
        });

      });
    })(jQuery);
  </script>
</body>
</html>
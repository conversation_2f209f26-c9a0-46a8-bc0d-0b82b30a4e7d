
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
<meta name="keywords" content="日期控件 datepicker calendar 日历控件 javascript js日历控件 带时间 自定义格式 月历控件 日期时间 日期选择" />
<title>My97日期控件 功能演示 日期范围限制 My97 Datepicker Demo</title>
<link href="../../css/base.css" tppabs="http://www.my97.net/dp/css/base.css" rel="stylesheet" type="text/css" />
<link href="demo.css" tppabs="http://www.my97.net/dp/demo/resource/demo.css" rel="stylesheet" type="text/css" />
</head>
<body>
<iframe src="../../head.asp" tppabs="http://www.my97.net/dp/head.asp" scrolling="no" frameborder="0" height="100px" width="100%"></iframe>
<script language="JavaScript" type="text/javascript" src="../../../WdatePicker.js" tppabs="http://www.my97.net/dp/My97DatePicker/WdatePicker.js"></script>
<div class="dCenter dBody">
  <div id="content">

    <h2>二. 功能及示例<a name="m2" id="m2"></a></h2>
<h3>4. 日期范围限制<a name="m24" id="m24"></a></h3>
	<oL>
              <LI>静态限制<a name="m241" id="m241"></a><br />
                <span class="STYLE1">注意:日期格式必须与 realDateFmt 和 realTimeFmt 一致而不是与 dateFmt 一致</span>
                <p>你可以给通过配置minDate(最小日期),maxDate(最大日期)为静态日期值,来限定日期的范围</p><div>
          <h4>示例4-1-1 限制日期的范围是 2006-09-10到2008-12-20</h4>
          <p><input id="d411" class="Wdate" type="text" onfocus="WdatePicker({skin:'whyGreen',minDate:'2006-09-10',maxDate:'2008-12-20'})"/><br />
&lt;input id=&quot;d411&quot; class=&quot;Wdate&quot; type=&quot;text&quot; onfocus=&quot;WdatePicker({skin:'whyGreen',<span class="STYLE2">minDate:</span><span class="STYLE1">'2006-09-10',</span><span class="STYLE2">maxDate:</span><span class="STYLE1">'2008-12-20'</span>})&quot;/&gt;</p>
        </div><div>
          <h4>示例4-1-2 限制日期的范围是 2008-3-8 11:30:00 到 2008-3-10 20:59:30</h4>
          <p><input type="text" class="Wdate" id="d412" onfocus="WdatePicker({skin:'whyGreen',dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'2008-03-08 11:30:00',maxDate:'2008-03-10 20:59:30'})" value="2008-03-09 11:00:00"/><br />
&lt;input type=&quot;text&quot; class=&quot;Wdate&quot; id=&quot;d412&quot; onfocus=&quot;WdatePicker({skin:'whyGreen',<span class="STYLE2">dateFmt:</span><span class="STYLE1">'yyyy-MM-dd HH:mm:ss',</span><span class="STYLE2">minDate:</span><span class="STYLE1">'2008-03-08 11:30:00',</span><span class="STYLE2">maxDate:</span><span class="STYLE1">'2008-03-10 20:59:30'</span>})&quot; value=&quot;2008-03-09 11:00:00&quot;/&gt;</p>
        </div><div>
          <h4>示例4-1-3 限制日期的范围是 2008年2月 到 2008年10月</h4>
          <p><input type="text" class="Wdate" id="d413" onfocus="WdatePicker({dateFmt:'yyyy年M月',minDate:'2008-2',maxDate:'2008-10'})"/><br />
&lt;input type=&quot;text&quot; class=&quot;Wdate&quot; id=&quot;d413&quot; onfocus=&quot;WdatePicker({<span class="STYLE2">dateFmt:</span><span class="STYLE1">'yyyy年M月',</span><span class="STYLE2">minDate:</span><span class="STYLE1">'2008-2',</span><span class="STYLE2">maxDate:</span><span class="STYLE1">'2008-10'</span>})&quot;/&gt;</p>
        </div><div>
          <h4>示例4-1-4 限制日期的范围是 8:00:00 到 11:30:00</h4>
          <p><input type="text" class="Wdate" id="d414" onfocus="WdatePicker({dateFmt:'H:mm:ss',minDate:'8:00:00',maxDate:'11:30:00'})"/><br />
&lt;input type=&quot;text&quot; class=&quot;Wdate&quot; id=&quot;d414&quot; onfocus=&quot;WdatePicker({<span class="STYLE2">dateFmt:</span><span class="STYLE1">'H:mm:ss',</span><span class="STYLE2">minDate:</span><span class="STYLE1">'8:00:00',</span><span class="STYLE2">maxDate:</span><span class="STYLE1">'11:30:00'</span>})&quot;/&gt;</p>
        </div></LI>
              <LI>动态限制<a name="m242" id="m242"></a><br />
                <span class="STYLE1">注意:日期格式必须与 realDateFmt 和 realTimeFmt 一致而不是与 dateFmt 一致</span>
                <p>你可以通过系统给出的动态变量,如%y(当前年),%M(当前月)等来限度日期范围,你还可以通过{}进行表达式运算,如:{%d+1}:表示明天<br />
                <br />
              动态变量表</p>
                <table width="335" border="0" cellpadding="3" cellspacing="1" bgcolor="#000000">
                  <tr>
                    <th width="62">格式</th>
                    <th width="258">说明</th>
                  </tr>
                  <tr>
                    <td align="center">%y </td>
                    <td>当前年</td>
                  </tr>
                  <tr>
                    <td align="center">%M </td>
                    <td>当前月</td>
                  </tr>
                  <tr>
                    <td align="center">%d </td>
                    <td>当前日</td>
                  </tr>
                  <tr>
                    <td align="center">%ld</td>
                    <td>本月最后一天</td>
                  </tr>
                  <tr>
                    <td align="center">%H </td>
                    <td>当前时</td>
                  </tr>
                  <tr>
                    <td align="center">%m </td>
                    <td>当前分</td>
                  </tr>
                  <tr>
                    <td align="center">%s </td>
                    <td>当前秒</td>
                  </tr>
                  <tr>
                    <td align="center">{}</td>
                    <td>运算表达式,如:{%d+1}:表示明天</td>
                  </tr>
                  <tr>
                    <td align="center">#F{}</td>
                    <td>{}之间是函数可写自定义JS代码</td>
                  </tr>
                </table>
				<div>
          <h4>示例4-2-1 只能选择今天以前的日期(包括今天)</h4>
          <p><input id="d421" class="Wdate" type="text" onfocus="WdatePicker({skin:'whyGreen',maxDate:'%y-%M-%d'})"/><br />
&lt;input id=&quot;d421&quot; class=&quot;Wdate&quot; type=&quot;text&quot; onfocus=&quot;WdatePicker({skin:'whyGreen',<span class="STYLE2">maxDate:</span><span class="STYLE1">'%y-%M-%d'</span>})&quot;/&gt;</p>
        </div>
		<div>
          <h4>示例4-2-2 <span class="STYLE2">使用了运算表达式</span> 只能选择今天以后的日期(不包括今天)</h4>
          <p><input id="d422" class="Wdate" type="text" onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})"/><br />
&lt;input id=&quot;d422&quot; class=&quot;Wdate&quot; type=&quot;text&quot; onfocus=&quot;WdatePicker({<span class="STYLE2">minDate:</span><span class="STYLE1">'%y-%M-{%d+1}'</span>})&quot;/&gt;</p>
        </div>
		<div>
          <h4>示例4-2-3 只能选择本月的日期1号至本月最后一天</h4>
          <p><input id="d423" class="Wdate" type="text" onfocus="WdatePicker({minDate:'%y-%M-01',maxDate:'%y-%M-%ld'})"/><br />
&lt;input id=&quot;d423&quot; class=&quot;Wdate&quot; type=&quot;text&quot; onfocus=&quot;WdatePicker({<span class="STYLE2">minDate:</span><span class="STYLE1">'%y-%M-01',</span><span class="STYLE2">maxDate:</span><span class="STYLE1">'%y-%M-%ld'</span>})&quot;/&gt;</p>
        </div>
		<div>
          <h4>示例4-2-4 只能选择今天7:00:00至明天21:00:00的日期</h4>
          <p><input id="d424" class="Wdate" type="text" onfocus="WdatePicker({dateFmt:'yyyy-M-d H:mm:ss',minDate:'%y-%M-%d 7:00:00',maxDate:'%y-%M-{%d+1} 21:00:00'})"/><br />
&lt;input id=&quot;d424&quot; class=&quot;Wdate&quot; type=&quot;text&quot; onfocus=&quot;WdatePicker({dateFmt:'yyyy-M-d H:mm:ss',<span class="STYLE2">minDate:</span><span class="STYLE1">'%y-%M-%d 7:00:00',</span><span class="STYLE2">maxDate:</span><span class="STYLE1">'%y-%M-{%d+1} 21:00:00'</span>})&quot;/&gt;</p>
        </div>
		<div>
          <h4>示例4-2-5 <span class="STYLE2">使用了运算表达式</span> 只能选择 20小时前 至 30小时后 的日期</h4>
          <p><input id="d425" class="Wdate" type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',minDate:'%y-%M-%d {%H-20}:%m:%s',maxDate:'%y-%M-%d {%H+30}:%m:%s'})"/><br />
&lt;input id=&quot;d425&quot; class=&quot;Wdate&quot; type=&quot;text&quot; onClick=&quot;WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',<span class="STYLE2">minDate:</span><span class="STYLE1">'%y-%M-%d {%H-20}:%m:%s'</span>,<span class="STYLE2">maxDate:</span><span class="STYLE1">'%y-%M-%d {%H+30}:%m:%s'</span>})&quot;/&gt;</p>
        </div>
              </LI>
              <LI>脚本自定义限制<a name="m243" id="m243"></a><br />
                <span class="STYLE1">注意:日期格式必须与 realDateFmt 和 realTimeFmt 一致而不是与 dateFmt 一致</span>
                <p>系统提供了$dp.$D和$dp.$DV这两个API来辅助你进行日期运算,此外你还可以通过在 #F{} 中填入你自定义的脚本,做任何你想做的日期限制</p><div>
          <h4>示例4-3-1 前面的日期不能大于后面的日期且两个日期都不能大于 2020-10-01</h4>
          <p>合同有效期从
            <input type="text" class="Wdate" id="d4311" onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'d4312\')||\'2020-10-01\'}'})"/>
到
<input type="text" class="Wdate" id="d4312" onfocus="WdatePicker({minDate:'#F{$dp.$D(\'d4311\')}',maxDate:'2020-10-01'})"/>
<br />
          &lt;input id=&quot;d4311&quot; class=&quot;Wdate&quot; type=&quot;text&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">maxDate:</span><span class="STYLE1">'#F{$dp.$D(\'d4312\')||\'2020-10-01\'}'</span>})&quot;/&gt; <br />
&lt;input id=&quot;d4312&quot; class=&quot;Wdate&quot; type=&quot;text&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">minDate:</span><span class="STYLE1">'#F{$dp.$D(\'d4311\')}'</span>,<span class="STYLE2">maxDate:'2020-10-01'</span>})&quot;/&gt;<br />
          <br />
          <span class="STYLE1">注意:</span><br />
          两个日期的日期格式必须相同<br />
          <br />$dp.$ 相当于 document.getElementById 函数.<br />
那么为什么里面的 ' 使用 \' 呢? 那是因为 &quot; 和 ' 都被外围的函数使用了,故使用转义符 \ ,否则会提示JS语法错误.<br />
所以您在其他地方使用时注意把 \' 改成 &quot; 或者 ' 来使用.<br />
<br />
#F{$dp.$D(\'d4312\')||\'2020-10-01\'}
  表示当 d4312 为空时, 采用 2020-10-01 的值作为最大值<br />
          </p>
        </div><div>
          <h4>示例4-3-2 前面的日期+3天 不能大于 后面的日期</h4>
          <p>日期从
            <input type="text" class="Wdate" id="d4321" onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'d4322\',{d:-3});}'})"/>
到
<input type="text" class="Wdate" id="d4322" onfocus="WdatePicker({minDate:'#F{$dp.$D(\'d4321\',{d:3});}'})"/>
<br />
&lt;input type=&quot;text&quot; class=&quot;Wdate&quot; id=&quot;d4321&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">maxDate:</span><span class="STYLE1">'#F{$dp.$D(\'d4322\',{d:-3});}'</span>})&quot;/&gt;<br />
&lt;input type=&quot;text&quot; class=&quot;Wdate&quot; id=&quot;d4322&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">minDate:</span><span class="STYLE1">'#F{$dp.$D(\'d4321\',{d:3});}'</span>})&quot;/&gt;<br />
          <br />
          使用 <span class="STYLE2">$dp.$D 函数</span> 可以将日期框中的值,加上定义的日期差量:<br />
两个参数: <span class="STYLE1">id={字符类型}需要处理的文本框的id值</span> , <span class="STYLE1">obj={对象类型}日期差量</span><br />
<span class="STYLE2">日期差量用法:</span><br />
属性y,M,d,H,m,s分别代表年月日时分秒<br />
如 <br />
为空时,表示直接取值,不做差量(示例4-3-1中的参数就是空的)<br />
{M:5,d:7} 表示 五个月零7天<br />
{y:1,d:-3} 表示 1年少3天<br />
{d:1,H:1} 表示一天多1小时</p>
        </div><div>
          <h4>示例4-3-3 前面的日期+3月零2天 不能大于 后面的日期 且 前面日期都不能大于 2020-4-3减去3月零2天 后面日期 不能大于 2020-4-3</h4>
          <p>住店日期从
            <input type="text" class="Wdate" id="d4331" onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'d4332\',{M:-3,d:-2})||$dp.$DV(\'2020-4-3\',{M:-3,d:-2})}'})"/>
到
<input type="text" class="Wdate" id="d4332" onfocus="WdatePicker({minDate:'#F{$dp.$D(\'d4331\',{M:3,d:2});}',maxDate:'2020-4-3'})"/>
<br />
&lt;input type=&quot;text&quot; class=&quot;Wdate&quot; id=&quot;d4331&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">maxDate:</span>'<span class="STYLE1">#F{$dp.$D(\'d4332\',{M:-3,d:-2})||$dp.$DV(\'2020-4-3\',{M:-3,d:-2})}</span>'})&quot;/&gt;<br />
&lt;input type=&quot;text&quot; class=&quot;Wdate&quot; id=&quot;d4332&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">minDate:</span>'<span class="STYLE1">#F{$dp.$D(\'d4331\',{M:3,d:2});}</span>',<span class="STYLE1">maxDate:</span>'<span class="STYLE1">2020-4-3</span>'})&quot;/&gt;<br />
<br />
<span class="STYLE1">注意:<br />
</span>#F{$dp.$D(\'d4332\',{M:-3,d:-2}) || $dp.$DV(\'2020-4-3\',{M:-3,d:-2})}<br />
表示当 d4332 为空时, 采用 $dp.$DV(\'2020-4-3\',{M:-3,d:-2})} 的值作为最大值</p>
          <p>使用 <span class="STYLE2">$dp.$DV 函数</span> 可以将显式传入的值,加上定义的日期差量:<br />
            两个参数: <span class="STYLE1">value={字符类型}需要处理的值</span> , <span class="STYLE1">obj={对象类型}日期差量</span><br />
            用法同上面的 <span class="STYLE2">$dp.$D</span> 类似,如 $dp.$DV(\'2020-4-3\',{M:-3,d:-2}) 表示 2020-4-3减去3月零2天</p>
        </div><div>
          <h4>示例4-3-4 发挥你的JS才能,定义任何你想要的日期限制</h4>
          <p>自动转到随机生成的一天,当然,此示例没有实际的用途,只是为演示目的<br />
            <script>
	//返回一个随机的日期
	function randomDate(){
		var Y = 2000 + Math.round(Math.random() * 10);
		var M = 1 + Math.round(Math.random() * 11);
		var D = 1 + Math.round(Math.random() * 27);
		return Y+'-'+M+'-'+D;
	}
            </script>
            <input type="text" class="Wdate" id="d434" onfocus="var date=randomDate();WdatePicker({minDate:date,maxDate:date})"/>
            <br />
&lt;script&gt;<br />
//返回一个随机的日期<br />
function randomDate(){<br />
var Y = 2000 + Math.round(Math.random() * 10);<br />
var M = 1 + Math.round(Math.random() * 11);<br />
var D = 1 + Math.round(Math.random() * 27);<br />
return Y+'-'+M+'-'+D;<br />
}<br />
&lt;/script&gt;<br />
&lt;input type=&quot;text&quot; class=&quot;Wdate&quot; id=&quot;d434&quot; onFocus=&quot;<span class="STYLE1">var date=randomDate();WdatePicker({minDate:date,maxDate:date})</span>&quot;/&gt;</p>
        </div>
              </LI>
              <LI>无效天<a name="m244" id="m244"></a>
                <p>可以使用此功能禁用周日至周六所对应的日期,相关属性:disabledDays (0至6 分别代表 周日至周六)</p><div>
          <h4>示例4-4-1 禁用 周六 所对应的日期</h4>
          <p><input id="d441" type="text" class="Wdate" onFocus="WdatePicker({disabledDays:[6]})"/><br />
&lt;input id=&quot;d441&quot; type=&quot;text&quot; class=&quot;Wdate&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">disabledDays:</span><span class="STYLE1">[6]</span>})&quot;/&gt;</p>
        </div><div>
          <h4>示例4-4-2 禁用 周六 周日 所对应的日期</h4>
          <p><input id="d442" type="text" class="Wdate" onFocus="WdatePicker({disabledDays:[0,6]})"/><br />            
          &lt;input id=&quot;d442&quot; type=&quot;text&quot; class=&quot;Wdate&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">disabledDays:</span><span class="STYLE1">[0,6]</span>})&quot;/&gt;</p>
        </div>
              </LI>
              <LI>无效日期<a name="m245" id="m245"></a><br />
                <span class="STYLE1">注意:日期格式必须与 realDateFmt 和 realTimeFmt 一致而不是与 dateFmt 一致</span>
                <p>可以使用此功能禁用,所指定的一个或多个日期,只要你熟悉正则表达式,你可以尽情发挥<br /><br />
                <span class="STYLE2">用法(正则匹配):</span> <br />
如果你熟悉正则表达式,会很容易理解下面的匹配用法<br />
如果不熟悉,可以参考下面的常用示例 <br />
['2008-02-01','2008-02-29'] 表示禁用 2008-02-01 和 2008-02-29<br />
['2008-..-01','2008-02-29'] 表示禁用 2008-所有月份-01 和 2008-02-29<br />
['200[0-8]]-02-01','2008-02-29'] 表示禁用 [2000至2008]-02-01 和 2008-02-29<br />
['^2006'] 表示禁用 2006年的所有日期 </p>
                <p>此外,您还可以使用  %y %M %d %H %m %s 等变量, 用法同动态日期限制 <span class="STYLE1">注意:%ld不能使用</span><br />
                  ['....-..-01','%y-%M-%d'] 表示禁用 所有年份和所有月份的第一天和今天 <br />
                  ['%y-%M-{%d-1}','%y-%M-{%d+1}'] 表示禁用 昨天和明天</p>
                <p>当然,除了可以限制日期以外,您还可以限制时间<br />
                  ['....-..-.. 10\:00\:00'] 表示禁用 每天10点 (注意 <span class="STYLE2">:</span> 需要 使用 <span class="STYLE2">\:</span> ) </p>
                <p>不再多举例了,尽情发挥你的正则才能吧!</p>
                <div>
          <h4>示例4-5-1 禁用 每个月份的 5日 15日 25日</h4>
          <p><input id="d451" type="text" class="Wdate" onFocus="WdatePicker({disabledDates:['5$']})"/><br />
&lt;input id=&quot;d451&quot; type=&quot;text&quot; class=&quot;Wdate&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">disabledDates:</span><span class="STYLE1">['5$']</span>})&quot;/&gt;<br />
<br />
<span class="STYLE1">注意
:</span><span class="STYLE2">'5$'</span> 表示以 5 结尾 注意 <span class="STYLE2">$</span> 的用法 </p>
        </div><div>
          <h4>示例4-5-2 禁用 所有早于2000-01-01的日期</h4>
          <p><input id="d452" type="text" class="Wdate" onFocus="WdatePicker({disabledDates:['^19']})"/><br />
&lt;input id=&quot;d452&quot; type=&quot;text&quot; class=&quot;Wdate&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">disabledDates:</span><span class="STYLE1">['^19']</span>})&quot;/&gt;<br />
          <br />
          <span class="STYLE1">注意:</span><span class="STYLE2">'^19'</span> 表示以 19 开头 注意 <span class="STYLE2">^</span> 的用法 <br />
          当然,可以使用minDate实现类似的功能 这里主要是 在演示 ^ 的用法</p>
        </div><div>
          <h4>示例4-5-3 配合min/maxDate使用,可以把可选择的日期分隔成多段</h4>
          <p>本示例本月可用日期分隔成五段 分别是: 1-3 8-10 16-24 26,27 29-月末<br />
            <input id="d453" type="text" class="Wdate" onFocus="WdatePicker({minDate:'%y-%M-01',maxDate:'%y-%M-%ld',disabledDates:['0[4-7]$','1[1-5]$','2[58]$']})"/><br />
&lt;input id=&quot;d453&quot; type=&quot;text&quot; class=&quot;Wdate&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">minDate:</span><span class="STYLE1">'%y-%M-01'</span>,<span class="STYLE2">maxDate:</span><span class="STYLE1">'%y-%M-%ld'</span>,<span class="STYLE2">disabledDates:</span><span class="STYLE1">['0[4-7]$','1[1-5]$','2[58]$']</span>})&quot;/&gt;</p>
        </div><div>
          <h4>示例4-5-4 <span class="STYLE2">min/maxDate disabledDays disabledDates 配合使用</span> 即使在要求非常苛刻的情况下也能满足需求</h4>
          <p><input id="d454" type="text" class="Wdate" onFocus="WdatePicker({minDate:'%y-%M-01',maxDate:'%y-%M-%ld',disabledDates:['0[4-7]$','1[1-5]$','2[58]$'],disabledDays:[1,3,6]})"/><br />
&lt;input id=&quot;d454&quot; type=&quot;text&quot; class=&quot;Wdate&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">minDate:</span><span class="STYLE1">'%y-%M-01'</span>,<span class="STYLE2">maxDate:</span><span class="STYLE1">'%y-%M-%ld'</span>,<span class="STYLE2">disabledDates:</span><span class="STYLE1">['0[4-7]$','1[1-5]$','2[58]$']</span>,<span class="STYLE2">disabledDays:</span><span class="STYLE1">[1,3,6]</span>})&quot;/&gt;</p>
        </div><div>
          <h4>示例4-5-5 禁用前一个小时和后一个小时内所有时间 使用 %y %M %d %H %m %s 等变量</h4>
          <p>鼠标点击 小时输入框时,你会发现当然时间对应的前一个小时和后一个小时是灰色的<br />
            <input id="d2a25" type="text" class="Wdate" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',disabledDates:['%y-%M-%d {%H-1}\:..\:..','%y-%M-%d {%H+1}\:..\:..']})"/>
            <br />
            &lt;input id=&quot;d2a25&quot; type=&quot;text&quot; class=&quot;Wdate&quot; onFocus=&quot;WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',<span class="STYLE2">disabledDates:</span><span class="STYLE1">['%y-%M-%d {%H-1}\:..\:..','%y-%M-%d {%H+1}\:..\:..']</span>})&quot;/&gt;          <br />
            <br />
            <span class="STYLE1">注意:</span>%y %M %d等详见<a href="#m242">动态变量表</a></p>
        </div><div>
          <h4>示例4-5-6  #F{}也是可以使用的</h4>
          <p>本示例利用自定义函数 随机禁用0-23中的任何一个小时<br />
          打开小时选择框,你会发现有一个小时被禁用的,而且每次禁用的小时都不同<br />
            <input type="text" class="Wdate" id="d456" onFocus="WdatePicker({dateFmt:'HH:mm:ss',disabledDates:['#F{randomH()}']})"/>
			<script>
function randomH(){
//产生一个随机的数字 0-23 
var H = Math.round(Math.random() * 23);
if(H<10) H='0'+H;
//返回 '^' + 数字
return '^'+H;
}
</script>
              <br />
&lt;script&gt;<br />
function randomH(){<br />
//产生一个随机的数字 0-23 <br />
var H = Math.round(Math.random() * 23);<br />
if(H&lt;10) H='0'+H;<br />
//返回 '^' + 数字<br />
return '^'+H;<br />
}<br />
&lt;/script&gt;<br />
&lt;input type=&quot;text&quot; class=&quot;Wdate&quot; id=&quot;d456&quot; onFocus=&quot;WdatePicker({dateFmt:'HH:mm:ss',<span class="STYLE2">disabledDates:</span><span class="STYLE1">['#F{randomH()}']</span>})&quot;/&gt;</p>
        </div></LI>
        <li>有效日期<a name="m246" id="m246"></a>
		  <p>使用无效日期可以很方便的禁用不可用的日期,但是在只需要启用少部分日期的情况下,有效日期的功能就非常适合了.<br />
		    <span class="STYLE1">关键属性:</span> <span class="STYLE2">opposite</span> 默认为false, 为true时,无效日期变成有效日期,<b>该属性对无效天,特殊天不起作用</b> </p>
		  <div>
          <h4>示例4-6 只启用 每个月份的 5日 15日 25日</h4>
          <p><input id="d46" type="text" class="Wdate" onFocus="WdatePicker({opposite:true,disabledDates:['5$']})"/><br />
&lt;input id=&quot;d46&quot; type=&quot;text&quot; class=&quot;Wdate&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">opposite:</span><span class="STYLE1">true</span>,<span class="STYLE2">disabledDates:</span><span class="STYLE1">['5$']</span>})&quot;/&gt;<br />
<br />
<span class="STYLE1">注意
:</span><span class="STYLE2">'5$'</span> 表示以 5 结尾 注意 <span class="STYLE2">$</span> 的用法 </p>
        </div>
		</li>
		<li>特殊天和特殊日期<a name="m247" id="m247"></a>
		  <p><span class="STYLE1">特殊天和特殊日期的用法跟完全无效天和无效日期完全相同,但是opposite属性对其无效</span><br />
		    <br />
	      <span class="STYLE1">关键属性:</span><br />
			 <span class="STYLE2">specialDays </span>(0至6 分别代表 周日至周六) 用法同无效天 <br />
		  <span class="STYLE2">specialDates</span>	用法同无效日期,但是对时分秒无效</p>
		  <div>
          <h4>示例4-7-1 高亮每周 周一 周五</h4>
          <p><input id="d471" type="text" class="Wdate" onFocus="WdatePicker({specialDays:[1,5]})"/><br />
&lt;input id=&quot;d471&quot; type=&quot;text&quot; class=&quot;Wdate&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">specialDays:</span><span class="STYLE1">[1,5]</span>})&quot;/&gt; </p>
        </div>
        <div>
          <h4>示例4-7-2 高亮每月 1号 15号</h4>
          <p><input id="d472" type="text" class="Wdate" onFocus="WdatePicker({specialDates:['....-..-01','....-..-15']})"/><br />
&lt;input id=&quot;d472&quot; type=&quot;text&quot; class=&quot;Wdate&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">specialDates:</span><span class="STYLE1">['....-..-01','....-..-15']</span>})&quot;/&gt; </p>
        </div>
		</li>
    </oL>
    <h3><a href="2.5.asp.htm" tppabs="http://www.my97.net/dp/demo/resource/2.5.asp">5. 自定义事件</a><a name="m25" id="m25"></a></h3>
<h3><a href="2.6.asp.htm" tppabs="http://www.my97.net/dp/demo/resource/2.6.asp">6. 快速选择功能</a> <a name="m26" id="m26"></a></h3>
    <h2><a href="3.asp.htm" tppabs="http://www.my97.net/dp/demo/resource/3.asp">三. 配置说明</a><a name="m3" id="m3"></a></h2>
    <h2><a href="999.asp.htm" tppabs="http://www.my97.net/dp/demo/resource/999.asp">四. 如何使用</a><a name="m4" id="m4"></a></h2>
    <br />
    <br />
  </div>
  <div style="clear:both"></div>
</div>
<div class="dCenter dBody" style="padding-left:72px">
<script type="text/javascript"><!--
google_ad_client = "ca-pub-6343250634002651";
/* 底部 */
google_ad_slot = "0599809152";
google_ad_width = 728;
google_ad_height = 90;
//-->
</script>
<script type="text/javascript">
</script>
</div>
<div id="footer" class="dCenter">&copy; 2010 <a href="mailto:<EMAIL>">My97</a> All Rights Reserved.&nbsp;&nbsp;&nbsp;&nbsp;<script type="text/javascript">
var _bdhmProtocol = (("https:" == document.location.protocol) ? " https://" : " http://");
document.write(unescape("%3Cscript src='" + _bdhmProtocol + "hm.baidu.com/h.js%3F489957c212e14340592fb2e4921b2f1d' type='text/javascript'%3E%3C/script%3E"));
</script>&nbsp;&nbsp;&nbsp;&nbsp;浙ICP备11060275号
</div>
</body>
</html>

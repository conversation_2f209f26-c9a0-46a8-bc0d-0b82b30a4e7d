
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
<meta name="keywords" content="日期控件 datepicker calendar 日历控件 javascript js日历控件 带时间 自定义格式 月历控件 日期时间 日期选择" />
<title>My97日期控件 功能演示 特色功能 常规功能 My97 Datepicker Demo</title>
<link href="../../css/base.css" tppabs="http://www.my97.net/dp/css/base.css" rel="stylesheet" type="text/css" />
<link href="demo.css" tppabs="http://www.my97.net/dp/demo/resource/demo.css" rel="stylesheet" type="text/css" />
</head>
<body>
<iframe src="../../head.asp" tppabs="http://www.my97.net/dp/head.asp" scrolling="no" frameborder="0" height="100px" width="100%"></iframe>
<script language="JavaScript" type="text/javascript" src="../../../WdatePicker.js" tppabs="http://www.my97.net/dp/My97DatePicker/WdatePicker.js"></script>
<div class="dCenter dBody">
  <div id="content">

    <h2>二. 功能及示例<a name="m2" id="m2"></a></h2>
<h3>2. 特色功能 <a name="m22" id="m22"></a></h3>
    <ol>
      <li>平面显示
        <a name="m221" id="m221"></a>
        <p>日期控件支持平面显示功能,只要设置一下eCont属性就可以把它当作日历来使用了,无需触发条件,直接显示在页面上</p>
        <br />
        <div>
          <h4>示例2-1 平面显示演示</h4>
          <div id="div1" style="border:0;padding:5px"></div>
          <script> 
WdatePicker({eCont:'div1',onpicked:function(dp){alert('你选择的日期是:'+dp.cal.getDateStr())}})
</script>
          <p> &lt;div id=&quot;<span class="STYLE1">div1</span>&quot;&gt;&lt;/div&gt;<br />
            &lt;script&gt;<br />
            WdatePicker({<span class="STYLE2">eCont:</span><span class="STYLE1">'div1'</span>,onpicked:function(dp){alert('你选择的日期是:'+dp.cal.getDateStr())}})<br />
            &lt;/script&gt;<br />
            <br />
            $dp.cal.getDateStr 用法详见<a href="999.asp.htm#m5" tppabs="http://www.my97.net/dp/demo/resource/999.asp#m5">内置函数和属性</a></p>
        </div>
      </li>
      <li>支持多种容器
        <a name="m222" id="m222"></a>
        <p>除了可以将值返回给input以外,还可以通过配置el属性将值返回给其他的元素(如:textarea,div,span)等,带有innerHTML属性的HTML元素</p>
        <div>
          <h4>示例2-2 将日期返回到&lt;span&gt;中</h4>
          <p><span id="demospan">2008-01-01</span> <img onClick="WdatePicker({el:'demospan'})" src="../../../skin/datePicker.gif" tppabs="http://www.my97.net/dp/My97DatePicker/skin/datePicker.gif" width="16" height="22" align="absmiddle" style="cursor:pointer" /><br />
            <br />
            代码:<br />
            &lt;span id=&quot;<span class="STYLE1">demospan</span>&quot;&gt;2008-01-01&lt;/span&gt; <br />
            &lt;img onClick=&quot;WdatePicker({<span class="STYLE2">el:</span><span class="STYLE1">'demospan'</span>})&quot; src=&quot;../../../skin/datePicker.gif&quot; width=&quot;16&quot; height=&quot;22&quot; align=&quot;absmiddle&quot; style=&quot;cursor:pointer&quot;  /&gt;</p>
        </div>
      </li>
      <li>起始日期功能 <a name="m223" id="m223"></a><br />
        <span class="STYLE1">注意:日期格式必须与 realDateFmt 和 realTimeFmt 一致而不是与 dateFmt 一致</span>
        <p>有时在项目中需要选择生日之类的日期,而默认点开始日期都是当前日期,导致年份选择非常麻烦,你可以通过起始日期功能加上配置alwaysUseStartDate属性轻松解决此类问题</p>
        <div>
          <h4>示例2-3-1 起始日期简单应用 </h4>
          <p>默认的起始日期为 1980-05-01<br />
            当日期框<span class="STYLE2">为空值时</span>,将使用 1980-05-01 做为起始日期 <br />
            <br />
            <input type="text" id="d221" onFocus="WdatePicker({startDate:'1980-05-01'})"/>
            <br />
            &lt;input type=&quot;text&quot; id=&quot;d221&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">startDate:</span><span class="STYLE1">'1980-05-01'</span>})&quot;/&gt;</p>
        </div>
        <div>
          <h4>示例2-3-2 alwaysUseStartDate属性应用</h4>
          <p>默认的起始日期为 1980-05-01<br />
            当日期框<span class="STYLE2">无论是何值</span>,始终使用 1980-05-01 做为起始日期 <br />
            <br />
            <input type="text" id="d222" onfocus="WdatePicker({startDate:'1980-05-01',alwaysUseStartDate:true})"/>
            <br />
            &lt;input type=&quot;text&quot; id=&quot;d222&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">startDate:</span><span class="STYLE1">'1980-05-01'</span>,<span class="STYLE2">alwaysUseStartDate:</span><span class="STYLE1">true</span>})&quot;/&gt;</p>
        </div>
        <div>
          <h4>示例2-3-3 使用内置参数</h4>
          <p>除了使用静态的日期值以外,还可以使用动态参数(如:%y,%M分别表示当前年和月)<br />
            <br />
            下例演示,<span class="STYLE2">年月日使用当年当月的1日,时分秒使用00:00:00作为起始时间</span><br />
            <br />
            <input type="text" id="d233" onFocus="WdatePicker({startDate:'%y-%M-01 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})"/>
            <br />
            &lt;input type=&quot;text&quot; id=&quot;d233&quot; onFocus=&quot;WdatePicker({<span class="STYLE2">startDate:</span><span class="STYLE1">'%y-%M-01 00:00:00'</span>,dateFmt:'yyyy-MM-dd HH:mm:ss',<span class="STYLE2">alwaysUseStartDate:</span><span class="STYLE1">true</span>})&quot;/&gt;</p>
        </div>
      </li>
      <li>自定义格式
        <a name="m224" id="m224"></a>
        <p>yMdHmswW分别代表年月日时分秒星期周,你可以任意组合这些元素来自定义你个性化的日期格式. </p>
        <br />
        日期格式表
        <table width="100%" border="0" cellspacing="1" bgcolor="#000000">
          <tr>
            <th width="12%">格式</th>
            <th width="88%">说明</th>
          </tr>
          <tr>
            <td align="center">y</td>
            <td>将年份表示为最多两位数字。如果年份多于两位数，则结果中仅显示两位低位数。</td>
          </tr>
          <tr>
            <td align="center">yy </td>
            <td>同上，如果小于两位数，前面补零。</td>
          </tr>
          <tr>
            <td align="center">yyy</td>
            <td>将年份表示为三位数字。如果少于三位数，前面补零。</td>
          </tr>
          <tr>
            <td align="center">yyyy</td>
            <td>将年份表示为四位数字。如果少于四位数，前面补零。</td>
          </tr>
          <tr>
            <td align="center">M</td>
            <td>将月份表示为从 1 至 12 的数字</td>
          </tr>
          <tr>
            <td align="center">MM</td>
            <td>同上，如果小于两位数，前面补零。</td>
          </tr>
          <tr>
            <td align="center">MMM</td>
            <td>返回月份的缩写 一月 至 十二月 (英文状态下 Jan to Dec) 。</td>
          </tr>
          <tr>
            <td align="center">MMMM</td>
            <td>返回月份的全称 一月 至 十二月 (英文状态下 January to December) 。</td>
          </tr>
          <tr>
            <td align="center">d</td>
            <td>将月中日期表示为从 1 至 31 的数字。</td>
          </tr>
          <tr>
            <td align="center">dd</td>
            <td>同上，如果小于两位数，前面补零。</td>
          </tr>
          <tr>
            <td align="center">H </td>
            <td>将小时表示为从 0 至 23 的数字。</td>
          </tr>
          <tr>
            <td align="center">HH</td>
            <td>同上，如果小于两位数，前面补零。</td>
          </tr>
          <tr>
            <td align="center">m</td>
            <td>将分钟表示为从 0 至 59 的数字。</td>
          </tr>
          <tr>
            <td align="center">mm</td>
            <td>同上，如果小于两位数，前面补零。</td>
          </tr>
          <tr>
            <td align="center">s</td>
            <td>将秒表示为从 0 至 59 的数字。</td>
          </tr>
          <tr>
            <td align="center">ss</td>
            <td>同上，如果小于两位数，前面补零。</td>
          </tr>
          <tr>
            <td align="center">w</td>
            <td>返回星期对应的数字 0 (星期天) - 6 (星期六) 。</td>
          </tr>
          <tr>
            <td align="center">D</td>
            <td>返回星期的缩写 一 至 六 (英文状态下 Sun to Sat) 。</td>
          </tr>
          <tr>
            <td align="center">DD</td>
            <td>返回星期的全称 星期一 至 星期六 (英文状态下 Sunday to Saturday) 。</td>
          </tr>
          <tr>
            <td align="center">W</td>
            <td>返回周对应的数字 (1 - 53) 。</td>
          </tr>
          <tr>
            <td align="center">WW</td>
            <td>同上，如果小于两位数，前面补零 (01 - 53) 。</td>
          </tr>
        </table>
        <br />
        示例<br />
        <table width="100%" border="0" cellspacing="1" bgcolor="#000000">
          <tr>
            <th width="191">格式字符串</th>
            <th width="460">值</th>
          </tr>
          <tr>
            <td>yyyy-MM-dd HH:mm:ss</td>
            <td>2008-03-12 19:20:00</td>
          </tr>
          <tr>
            <td>yy年M月</td>
            <td>08年3月</td>
          </tr>
          <tr>
            <td>yyyyMMdd</td>
            <td>20080312</td>
          </tr>
          <tr>
            <td>今天是:yyyy年M年d HH时mm分 </td>
            <td>今天是:2008年3月12日 19时20分</td>
          </tr>
          <tr>
            <td>H:m:s</td>
            <td>19:20:0</td>
          </tr>
          <tr>
            <td>y年</td>
            <td>8年</td>
          </tr>
          <tr>
            <td>MMMM d, yyyy</td>
            <td>三月 12, 2008</td>
          </tr>
        </table>
        <div>
          <h4>示例 2-4-1: 年月日时分秒</h4>
          <p>
            <input type="text" id="d241" onfocus="WdatePicker({dateFmt:'yyyy年MM月dd日 HH时mm分ss秒'})" class="Wdate" style="width:300px"/>
            <br />
            &lt;input type=&quot;text&quot; id=&quot;d241&quot; onfocus=&quot;WdatePicker({<span class="STYLE2">dateFmt:</span><span class="STYLE1">'yyyy年MM月dd日 HH时mm分ss秒'</span>})&quot; class=&quot;Wdate&quot; style=&quot;width:300px&quot;/&gt;<br />
            <br />
          <span class="STYLE1">注意:</span>点两次才能选择日期的原因,详见 <a href="3.asp.htm#autopickdate" tppabs="http://www.my97.net/dp/demo/resource/3.asp#autopickdate">autoPickDate</a> 属性</p>
        </div>
        <div>
          <h4>示例 2-4-2 时分秒</h4>
          <p>
            <input type="text" id="d242" onfocus="WdatePicker({skin:'whyGreen',dateFmt:'H:mm:ss'})" class="Wdate"/>
            <br />
            &lt;input type=&quot;text&quot; id=&quot;d242&quot; onfocus=&quot;WdatePicker({skin:'whyGreen',<span class="STYLE2">dateFmt:</span><span class="STYLE1">'H:mm:ss'</span>})&quot; class=&quot;Wdate&quot;/&gt;<br />
            <br />
            <span class="STYLE1">注意:</span>这里提前使用了皮肤(skin)属性,所以你会看到一个不同的皮肤,皮肤属性详见<a href="2.3.asp.htm#m232" tppabs="http://www.my97.net/dp/demo/resource/2.3.asp#m232">自定义和动态切换皮肤</a> </p>
        </div>
        <div>
          <h4>示例 2-4-3 年月</h4>
          <p>
            <input type="text" id="d243" onfocus="WdatePicker({skin:'whyGreen',dateFmt:'yyyy年MM月'})" class="Wdate"/>
            <br />
            &lt;input type=&quot;text&quot; id=&quot;d243&quot; onfocus=&quot;WdatePicker({skin:'whyGreen',<span class="STYLE2">dateFmt:</span><span class="STYLE1">'yyyy年MM月'</span>})&quot; class=&quot;Wdate&quot;/&gt;</p>
        </div>
        <div>
          <h4>示例 2-4-4 取得系统可识别的日期值<span class="STYLE1">(重要)</span></h4>
          <p>类似于 1999年7月5日 这样的日期是不能够被系统识别的,他必须转换为能够识别的类型如 1999-07-05 <br />
            <br />
            <input id="d244" type="text" class="Wdate" onfocus="WdatePicker({dateFmt:'yyyy年M月d日',vel:'d244_2'})"/>
            真实的日期值是:
            <input id="d244_2" type="text" />
            <br />
            &lt;input id=&quot;d244&quot; type=&quot;text&quot; class=&quot;Wdate&quot; onfocus=&quot;WdatePicker(<span class="STYLE2">{dateFmt:</span><span class="STYLE1">'yyyy年M月d日'</span>,<span class="STYLE2">vel:</span><span class="STYLE1">'d244_2'</span>})&quot;/&gt;<br />
            &lt;input id=&quot;<span class="STYLE1">d244_2</span>&quot; type=&quot;text&quot; /&gt;<br />
            <br />
            <span class="STYLE1">注意:</span>在实际应用中,一般会把vel指定为一个<span class="STYLE2">hidden控件</span>,这里是为了把真实值展示出来,所以使用文本框<br />
            <span class="STYLE1">关键属性:</span> <span class="STYLE2">vel</span> 指定一个控件或控件的ID,必须具有value属性(如input),用于存储真实值(也就是realDateFmt和realTimeFmt格式化后的值)</p>
        </div>
        <div>
          <h4>示例 2-4-5 星期, 月 日, 年<span class="STYLE1">(4.6新增)</span></h4>
          <p>
            <input type="text" id="d245" style="width:200px" onfocus="WdatePicker({dateFmt:'DD, MMMM d, yyyy'})" class="Wdate"/>
            <br />
            &lt;input type=&quot;text&quot; id=&quot;d245&quot; onfocus=&quot;WdatePicker({<span class="STYLE2">dateFmt:</span><span class="STYLE1">'DD, MMMM d, yyyy'</span>})&quot; class=&quot;Wdate&quot;/&gt;</p>
        </div>
      </li>
      <li> 双月日历功能<span class="STYLE1">(4.6新增)</span> <a name="m225" id="m225"></a>
        <p>可以同时弹出两个月的日历</p>
        <div>
          <h4>示例2-5  双月日历功能</h4>
          <p>
            <input class="Wdate" type="text" onfocus="WdatePicker({doubleCalendar:true,dateFmt:'yyyy-MM-dd'})"/>
            <br />
            &lt;input class=&quot;Wdate&quot; type=&quot;text&quot; onfocus=&quot;WdatePicker({<span class="STYLE2">doubleCalendar:</span><span class="STYLE1">true</span>,dateFmt:'yyyy-MM-dd'})&quot;/&gt;<br />
            <br />
          <span class="STYLE1">注意:</span>双月日历一般只用于包含年月日三个元素的场景,另外设置该属性时,autoPickDate自动设置为true</p>
        </div>
      </li>
      <li>自动纠错功能
        <a name="m226" id="m226"></a>
        <p>纠错处理可设置为3种模式:提示(默认) 自动纠错 标记,当日期框中的值不符合格式时,系统会尝试自动修复,如果修复失败会根据您设置的纠错处理模式进行处理,错误判断功能非常智能它可以保证用户输入的值是一个合法的值</p>
        <div>
          <h4>示例2-6-1 不合法的日期演示</h4>
          <p>请在下面的日期框中<span class="STYLE2">填入一个不合法的日期(如:1997-02-29)</span>,再<span class="STYLE2">尝试离开焦点</span><br />
            使用默认容错模式 <span class="STYLE2">提示模式</span> errDealMode = 0 在输入错误日期时,会先<span class="STYLE2">提示</span> <br />
            <input type="text" class="Wdate" id="d261" onclick="WdatePicker()"/>
            <br />
            <br />
            <span class="STYLE1">注意:</span>1997年不是闰年哦</p>
        </div>
        <div>
          <h4>示例2-6-2 超出日期限制范围的日期也被认为是一个不合法的日期</h4>
          <p>最大日期是2000-01-10 ,如果在下框中填入的日期 大于 2000-01-10(如2000-01-12)也会被认为是不合法的日期 <br />
            <span class="STYLE2">自动纠错模式</span> errDealMode = 1 在输入错误日期时,<span class="STYLE2">自动恢复前一次正确的值</span><br />
            <input name="text" type="text" class="Wdate" onfocus="WdatePicker({errDealMode:1,maxDate:'2000-01-10'})"/>
          </p>
        </div>
        <div>
          <h4>示例2-6-3 使用无效天和无效日期功能限制的日期也被认为是一个不合法的日期</h4>
          <p>如:<br />
            2008-02-20 无效日期限制<br />
            2008-02-02 2008-02-09 2008-02-16 2008-02-23 无效天限制<br />
            都是无效日期<br />
            您可以尝试在下框中输入这些日期,并离开焦点<br />
            <br />
            <span class="STYLE2">标记模式</span> errDealMode = 2 在输入错误日期时,<span class="STYLE2">不做提示和更改,只是做一个标记,但此时日期框不会马上隐藏</span><br />
            <input name="text2" type="text" class="Wdate" onfocus="WdatePicker({errDealMode:2,maxDate:'2010-01-10',disabledDays:[6],disabledDates:['2008-02-20']})" value="2008-02-12"/>
            <br />
            <br />
            <span class="STYLE1">注意:</span>标记类:WdateFmtErr是在skin目录下WdatePicker.css中定义的</p>
        </div>
      </li>
      <li>跨无限级框架显示
        <a name="m227" id="m227"></a>
        <p>无论你把日期控件放在哪里,你都不需要担心会被外层的iframe所遮挡进而影响客户体验,因为My97日期控件是可以跨无限级框架显示的</p>
        <div>
          <h4>示例2-7 跨无限级框架演示</h4>
          <p>可无限跨越框架iframe,无论怎么嵌套框架都不必担心了,即使有滚动条也不怕<br />
          <span class="STYLE1">注意:Javascript是无法跨越frameset的,但是My97日期控件可无限跨越框架iframe,任何的frameset都可以使用iframe代替</span></p>
          <iframe src="demo_iframe.htm" tppabs="http://www.my97.net/dp/demo/resource/demo_iframe.htm" width="100%" height="220px" frameborder="0"></iframe>
        </div>
      </li>
      <li>民国年日历和其他特殊日历
        <a name="m228" id="m228"></a>
        <p>当年份格式设置为yyy格式时,利用年份差量属性yearOffset(默认值1911民国元年),可实现民国年日历和其他特殊日历</p>
        <div>
          <h4>示例2-8 民国年演示</h4>
          <p>
            <input type="text" id="d28" onClick="WdatePicker({dateFmt:'yyy/MM/dd'})"/>
            <br />
            &lt;input type=&quot;text&quot; id=&quot;d28&quot; onClick=&quot;WdatePicker({<span class="STYLE2">dateFmt:</span><span class="STYLE1">'yyy/MM/dd'</span>})&quot;/&gt;<br />
            <br />
            <span class="STYLE1">注意:</span>年份格式设置成yyy时,真正的日期将会减去一个差量<span class="STYLE2">yearOffset(默认值为:1911)</span>,如果是民国年使用默认值即可无需另外配置,如果是其他的差量,可以通过参数的形式配置</p>
        </div>
      </li>
      <li>编辑功能 <a name="m229" id="m229"></a>
        <p>当日期框里面有值时,修改完某个属性后,只要点击这个按钮就可以实现时间和日期的编辑</p>
        <div>
          <h4>示例2-9 日期和时间的编辑演示</h4>
          <p>您可以尝试对下面框中的月份改为1,然后点击<span class="STYLE2">更新</span>,你会发现日期由 2000-<span class="STYLE1">02</span>-29 01:00:00 变为 2000-<span class="STYLE1">01</span>-29 01:00:00<br />
            <input class="Wdate" type="text" style="width:200px"  onfocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" value="2000-02-29 01:00:00"/>
          </p>
        </div>
      </li>
      <li>为编程带来方便
        <a name="m22a" id="m22a"></a>
        <p>如果el的值是this,可省略,即所有的el:this都可以不写 <br />
          日期框设置为disabled时,禁止更改日期(不弹出选择框) <br />
          如果没有定义onpicked事件,自动触发文本框的onchange事件 <br />
          如果没有定义oncleared事件,清空时,自动触发onchange事件</p>
      </li>
      <li>其他属性
        <a name="m22b" id="m22b"></a>
        <p>设置readOnly属性,可指定日期框是否只读 <br />
          设置highLineWeekDay属性,可指定是否高亮周末 <br />
          设置isShowOthers属性,可指定是否显示其他月的日期 <br />
          加上class="Wdate"就会在选择框右边出现日期图标</p>
      </li>
    </ol>
    <h3><a href="2.3.asp.htm" tppabs="http://www.my97.net/dp/demo/resource/2.3.asp">3. 多语言和自定义皮肤</a><a name="m23" id="m23"></a></h3>
    <h3><a href="2.4.asp.htm" tppabs="http://www.my97.net/dp/demo/resource/2.4.asp">4. 日期范围限制</a><a name="m24" id="m24"></a></h3>
    <h3><a href="2.5.asp.htm" tppabs="http://www.my97.net/dp/demo/resource/2.5.asp">5. 自定义事件</a><a name="m25" id="m25"></a></h3>
    <h3><a href="2.6.asp.htm" tppabs="http://www.my97.net/dp/demo/resource/2.6.asp">6. 快速选择功能</a> <a name="m26" id="m26"></a></h3>
    <h2><a href="3.asp.htm" tppabs="http://www.my97.net/dp/demo/resource/3.asp">三. 配置说明</a><a name="m3" id="m3"></a></h2>
    <h2><a href="999.asp.htm" tppabs="http://www.my97.net/dp/demo/resource/999.asp">四. 如何使用</a><a name="m4" id="m4"></a></h2>
    <br />
    <br />
  </div>
  <div style="clear:both"></div>
</div>
<div class="dCenter dBody" style="padding-left:72px">
<script type="text/javascript"><!--
google_ad_client = "ca-pub-6343250634002651";
/* 底部 */
google_ad_slot = "0599809152";
google_ad_width = 728;
google_ad_height = 90;
//-->
</script>
<script type="text/javascript">
</script>
</div>
<div id="footer" class="dCenter">&copy; 2010 <a href="mailto:<EMAIL>">My97</a> All Rights Reserved.&nbsp;&nbsp;&nbsp;&nbsp;<script type="text/javascript">
var _bdhmProtocol = (("https:" == document.location.protocol) ? " https://" : " http://");
document.write(unescape("%3Cscript src='" + _bdhmProtocol + "hm.baidu.com/h.js%3F489957c212e14340592fb2e4921b2f1d' type='text/javascript'%3E%3C/script%3E"));
</script>&nbsp;&nbsp;&nbsp;&nbsp;浙ICP备11060275号
</div>
</body>
</html>

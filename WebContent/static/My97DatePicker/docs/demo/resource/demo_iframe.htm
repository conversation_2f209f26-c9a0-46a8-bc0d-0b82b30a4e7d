<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
<meta name="keywords" content="日期控件 datepicker calendar 日历控件 javascript js日历控件 带时间 自定义格式 月历控件 日期时间 日期选择" />
<title>My97 DatePicker 4.0 演示-框架1</title>
<script language="javascript" type="text/javascript" src="../../../WdatePicker.js" tppabs="http://www.my97.net/dp/My97DatePicker/WdatePicker.js"></script>
<style type="text/css">
<!--
#Layer1 {
	position:absolute;
	width:200px;
	height:115px;
	z-index:1;
	left: 22px;
	top: 73px;
	background-color: #0033FF;
}
#Layer2 {
	position:absolute;
	width:200px;
	height:115px;
	z-index:1;
	left: 459px;
	top: 80px;
}
-->
</style>
</head>
<body bgcolor="#60FFAF">
<p>内嵌框架1:</p>
<p>格式为:yyyy-MM-dd HH:mm<br>
  <input type="text" class="Wdate" id="test2" style="width:200px" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})" />
</p>
  </p>
  <select name="select">
    <option>可以遮住DropDownList</option>
  </select>
<p> </p>
  <p>&nbsp;</p>
<p>&nbsp;</p>
<div id="Layer2">
 <iframe id='f22' src="demo_iframe2.htm" tppabs="http://www.my97.net/dp/demo/resource/demo_iframe2.htm" frameborder="0" width="300px" height="150px"></iframe> 
</div>
</body>
</html>

/* 
 * My97 DatePicker 4.7
 * Ƥ������:default
 */

/* ����ѡ������ DIV */
.WdateDiv{
	width:180px;
	background-color:#FFFFFF;
	border:#bbb 1px solid;
	padding:2px;
}
/* ˫�������Ŀ�� */
.WdateDiv2{
	width:360px;
}
.WdateDiv *{font-size:9pt;}

/****************************
 * ����ͼ�� ȫ����A��ǩ
 ***************************/
.WdateDiv .NavImg a{
	display:block;
	cursor:pointer;
	height:16px;
	width:16px;
}

.WdateDiv .NavImgll a{
	float:left;
	background:transparent url(img.gif) no-repeat scroll 0 0;
}
.WdateDiv .NavImgl a{
	float:left;
	background:transparent url(img.gif) no-repeat scroll -16px 0;
}
.WdateDiv .NavImgr a{
	float:right;
	background:transparent url(img.gif) no-repeat scroll -32px 0;
}
.WdateDiv .NavImgrr a{
	float:right;
	background:transparent url(img.gif) no-repeat scroll -48px 0;
}

/****************************
 * ����·����
 ***************************/
/* ����·��� DIV */
.WdateDiv #dpTitle{
	height:24px;
	margin-bottom:2px;
	padding:1px;
}
/* ����·������ INPUT */
.WdateDiv .yminput{
	margin-top:2px;
	text-align:center;
	height:20px;
	border:0px;
	width:50px;
	cursor:pointer;		
}
/* ����·�������ý���ʱ����ʽ INPUT */
.WdateDiv .yminputfocus{
	margin-top:2px;
	text-align:center;
	font-weight:bold;
	height:20px;
	color:blue;
	border:#ccc 1px solid;
	width:50px;
}
/* �˵�ѡ��� DIV */
.WdateDiv .menuSel{
	z-index:1;
	position:absolute;
	background-color:#FFFFFF;	
	border:#ccc 1px solid;
	display:none;
}
/* �˵�����ʽ TD */
.WdateDiv .menu{
	cursor:pointer;
	background-color:#fff;
}
/* �˵���mouseover��ʽ TD */
.WdateDiv .menuOn{
	cursor:pointer;
	background-color:#BEEBEE;
}
/* �˵���Чʱ����ʽ TD */
.WdateDiv .invalidMenu{
	color:#aaa;
}
/* ��ѡ����ƫ�� DIV */
.WdateDiv .YMenu{
	margin-top:20px;
	
}
/* ��ѡ����ƫ�� DIV */
.WdateDiv .MMenu{
	margin-top:20px;
	*width:62px;
}
/* ʱѡ����λ�� DIV */
.WdateDiv .hhMenu{
	margin-top:-90px; 
	margin-left:26px;
}
/* ��ѡ����λ�� DIV */
.WdateDiv .mmMenu{
	margin-top:-46px; 
	margin-left:26px;
}
/* ��ѡ����λ�� DIV */
.WdateDiv .ssMenu{
	margin-top:-24px; 
	margin-left:26px;
}

/****************************
 * �����
 ***************************/
 .WdateDiv .Wweek {
 	text-align:center;
	background:#DAF3F5;
	border-right:#BDEBEE 1px solid;
 }
/****************************
 * ����,�������
 ***************************/
/* ������ TR */
.WdateDiv .MTitle{
	background-color:#BDEBEE;
}
.WdateDiv .WdayTable2{
	border-collapse:collapse;
	border:#c5d9e8 1px solid;
}
.WdateDiv .WdayTable2 table{
	border:0;
}
/* ��������� TABLE */
.WdateDiv .WdayTable{
	line-height:20px;
	border:#c5d9e8 1px solid;
}
.WdateDiv .WdayTable td{
	text-align:center;
}
/* ���ڸ����ʽ TD */
.WdateDiv .Wday{
	cursor:pointer;
}
/* ���ڸ��mouseover��ʽ TD */
.WdateDiv .WdayOn{
	cursor:pointer;
	background-color:#C0EBEF;
}
/* ��ĩ���ڸ����ʽ TD */
.WdateDiv .Wwday{
	cursor:pointer;
	color:#FF2F2F;
}
/* ��ĩ���ڸ��mouseover��ʽ TD */
.WdateDiv .WwdayOn{
	cursor:pointer;
	color:#000;
	background-color:#C0EBEF;
}
.WdateDiv .Wtoday{
	cursor:pointer;
	color:blue;
}
.WdateDiv .Wselday{
	background-color:#A9E4E9;
}
.WdateDiv .WspecialDay{
	background-color:#66F4DF;
}
/* �����·ݵ����� */
.WdateDiv .WotherDay{ 
	cursor:pointer;
	color:#6A6AFF;	
}
/* �����·ݵ�����mouseover��ʽ */
.WdateDiv .WotherDayOn{ 
	cursor:pointer;
	background-color:#C0EBEF;	
}
/* ��Ч���ڵ���ʽ,�������ڷ�Χ�������ڸ����ʽ,����ѡ������� */
.WdateDiv .WinvalidDay{
	color:#aaa;
}

/****************************
 * ʱ�����
 ***************************/
/* ʱ���� DIV */
.WdateDiv #dpTime{
	float:left;
	margin-top:3px;
	margin-right:30px;
}
/* ʱ������ SPAN */
.WdateDiv #dpTime #dpTimeStr{
	margin-left:1px;
}
/* ʱ������� INPUT */
.WdateDiv #dpTime input{
	width:18px;
	height:20px;
	text-align:center;
	border:#ccc 1px solid;	
}
/* ʱ�� ʱ INPUT */
.WdateDiv #dpTime .tB{
	border-right:0px;
}
/* ʱ�� �ֺͼ���� ':' INPUT */
.WdateDiv #dpTime .tE{
	border-left:0;
	border-right:0;
}
/* ʱ�� �� INPUT */
.WdateDiv #dpTime .tm{
	width:7px;
	border-left:0;
	border-right:0;
}
/* ʱ���ұߵ����ϰ�ť BUTTON */
.WdateDiv #dpTime #dpTimeUp{
	height:10px;
	width:13px;
	border:0px;
	background:url(img.gif) no-repeat -32px -16px;
}
/* ʱ���ұߵ����°�ť BUTTON */
.WdateDiv #dpTime #dpTimeDown{
	height:10px;
	width:13px;
	border:0px;
    background:url(img.gif) no-repeat -48px -16px;
}
/****************************
 * ����
 ***************************/
 .WdateDiv #dpQS {
 	float:left;
	margin-right:3px;
	margin-top:3px;
	background:url(img.gif) no-repeat 0px -16px;
	width:20px;
	height:20px;
	cursor:pointer;
 }
.WdateDiv #dpControl {
	text-align:right;	
}
.WdateDiv .dpButton{ 
	height:20px;
	width:45px;
	border:#ccc 1px solid;
	margin-top:2px;
	margin-right:1px;
}
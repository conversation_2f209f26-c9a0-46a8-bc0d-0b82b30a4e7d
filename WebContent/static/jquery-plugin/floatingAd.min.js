(function(f,d,a,h){var e="floatingAd";var g={step:1,delay:50,isLinkClosed:false,onClose:function(i){}};var b={linkUrl:"#","z-index":"100","closed-icon":"",imgHeight:"",imgWidth:"",title:"",img:"#",linkWindow:"_blank",headFilter:0.2};function c(j,i){this.element=j;this.options=f.extend({},g,i,{width:f(d).width(),height:f(d).height(),xPos:this.getRandomNum(0,f(d).width()-f(j).innerWidth()),yPos:this.getRandomNum(0,300),yOn:this.getRandomNum(0,1),xOn:this.getRandomNum(0,1),yPath:this.getRandomNum(0,1),xPath:this.getRandomNum(0,1),hOffset:f(j).innerHeight(),wOffset:f(j).innerWidth(),fn:function(){},interval:0});this._defaults=g;this._name=e;this.init()}c.prototype={init:function(){var j=f(this.element);var l=this.options;var k=this;var i=0;var m=0;j.css({left:l.xPos+k.scrollX(),top:l.yPos+k.scrollY()});l.fn=function(){l.width=f(d).width();l.height=f(d).height();if(i==k.scrollX()&&m==k.scrollY()){j.css({left:l.xPos+k.scrollX(),top:l.yPos+k.scrollY()});if(l.yOn){l.yPos=l.yPos+l.step}else{l.yPos=l.yPos-l.step}if(l.yPos<=0){l.yOn=1;l.yPos=0}if(l.yPos>=(l.height-l.hOffset)){l.yOn=0;l.yPos=(l.height-l.hOffset)}if(l.xOn){l.xPos=l.xPos+l.step}else{l.xPos=l.xPos-l.step}if(l.xPos<=0){l.xOn=1;l.xPos=0}if(l.xPos>=(l.width-l.wOffset)){l.xOn=0;l.xPos=(l.width-l.wOffset)}}m=f(d).scrollTop();i=f(d).scrollLeft()};this.run(j,l)},run:function(i,j){this.start(i,j);this.adEvent(i,j)},start:function(i,j){i.find("div.close").hide();j.interval=d.setInterval(j.fn,j.delay);d.setTimeout(function(){i.show()},j.delay)},getRandomNum:function(i,k){var l=k-i;var j=Math.random();return(i+Math.round(j*l))},getPath:function(i){return i?0:1},clear:function(i,j){i.find("div.close").show();d.clearInterval(j.interval)},close:function(j,k,i){j.unbind("hover");j.hide();if(i){k.onClose.call(j)}},adEvent:function(j,l){var k={elem:this,fn_close:function(){this.elem.close(j,l,true)},fn_clear:function(){if(this.elem.options.isLinkClosed){this.elem.close(j,l,false)}}};j.find("div.button").bind("click",jQuery.proxy(k,"fn_close"));j.find("a").bind("click",jQuery.proxy(k,"fn_clear"));
var i={elem:this,over:function(){this.elem.clear(j,l)},out:function(){this.elem.start(j,l)}};j.hover(jQuery.proxy(i,"over"),jQuery.proxy(i,"out"))},scrollX:function(){var i=a.documentElement;return self.pageXOffset||(i&&i.scrollLeft)||a.body.scrollLeft},scrollY:function(){var i=a.documentElement;return self.pageYOffset||(i&&i.scrollTop)||a.body.scrollTop}};f.fn.floatingAd=function(i){return this.children("div").each(function(j,k){if(!f.data(this,"plugin_"+e)){f.data(this,"plugin_"+e,new c(this,i))}})};f.floatingAd=function(l){if(l){if(l.ad){var k=f("#"+e);if(k.length<=0){k=f("<div>",{id:e,"class":e}).appendTo("body")}for(var m in l.ad){var n=l.ad[m];n=f.extend({},b,n);var p=f("<div>",{"class":"ad"});p.css("z-index",n["z-index"]);var j=f("<div>",{"class":"close",style:(n.imgWidth?"width:"+n.imgWidth+"px;":"")});f("<div>",{"class":"opacity",style:"opacity: "+n.headFilter+";filter: alpha(opacity = "+n.headFilter*100+");"}).appendTo(j);f("<div>",{"class":"text"}).append(f("<div>",{"class":"title",text:n.title})).append(f("<div>",{"class":"button",style:n["closed-icon"]?'background:url("'+n["closed-icon"]+'") no-repeat;':""})).appendTo(j);j.appendTo(p);var o=f("<div>");f("<a>",{href:n.linkUrl,target:n.linkWindow,title:n.title}).append(f("<img>",{src:n.img,style:(n.imgHeight?"height:"+n.imgHeight+"px;":"")+(n.imgWidth?"width:"+n.imgWidth+"px;":"")})).appendTo(o);o.appendTo(p);p.appendTo(k)}delete l.ad;f("#"+e).floatingAd(l)}}else{f.error("漂浮广告错误!")}}})(jQuery,window,document);
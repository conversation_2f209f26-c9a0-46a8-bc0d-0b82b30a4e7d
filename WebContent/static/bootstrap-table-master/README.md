# [Bootstrap Table](http://bootstrap-table.wenzhixin.net.cn)

[![Build Status](https://travis-ci.org/wenzhixin/bootstrap-table.svg)](https://travis-ci.org/wenzhixin/bootstrap-table) 
[![GitHub version](https://badge.fury.io/gh/wenzhixin%2Fbootstrap-table.svg)](http://badge.fury.io/gh/wenzhixin%2Fbootstrap-table)
[![Bower version](https://badge.fury.io/bo/bootstrap-table.svg)](http://badge.fury.io/bo/bootstrap-table)
[![Donate](https://www.paypalobjects.com/en_US/i/btn/btn_donateCC_LG.gif)](https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=ZDHP676FQDUT6)

An extended Bootstrap table with radio, checkbox, sort, pagination, extensions and other added features.

To get started, check out [Docs](http://bootstrap-table.wenzhixin.net.cn), [More examples](https://github.com/wenzhixin/bootstrap-table-examples), [Example issues](https://github.com/wenzhixin/bootstrap-table/issues?q=label%3Aexample+is%3Aclosed), [Resource issues](https://github.com/wenzhixin/bootstrap-table/issues?q=label%3Aresource+is%3Aclosed) and [Questions / Helps](http://stackoverflow.com/questions/tagged/bootstrap-table)!


[**List of donators**](https://github.com/wenzhixin/bootstrap-table/blob/master/DONATORS.md)

## LICENSE

**NOTE:** Bootstrap Table is licensed under the [The MIT License](https://github.com/wenzhixin/bootstrap-table/blob/master/LICENSE). Completely free, you can arbitrarily use and modify this plugin. If this plugin is useful to you, you can **Star** this repo, your support is my biggest motive force, thanks.

## Features

* Created for Bootstrap 3 (Bootstrap 2 supported)
* Responsive web design
* Scrollable Table with fixed headers
* Fully configurable
* Via data attributes
* Show/Hide columns
* Show/Hide headers
* Show/Hide footers
* Get data in JSON format using AJAX
* Simple column sorting with a click
* Format column
* Single or multiple row selection
* Powerful pagination
* Card view
* Detail view
* Localization
* Extensions

## How to get it

### Manual download

Use [Releases page](https://github.com/wenzhixin/bootstrap-table/releases) or [the source](https://github.com/wenzhixin/bootstrap-table/archive/master.zip).

### Bower

```
bower install bootstrap-table
```

### Npm

```
npm install bootstrap-table
```

### CDN

You can source bootstrap-table directly from a CDN like [CDNJS](http://www.cdnjs.com/libraries/bootstrap-table) or [bootcss](http://open.bootcss.com/bootstrap-table/) or [jsdelivr](http://www.jsdelivr.com/#!bootstrap.table).

## Reporting issues

Please provide jsFiddle when creating issues!

It's really saves much time. Use this as template:

[jsFiddle Bootstrap Table](http://bootstrap-table.wenzhixin.net.cn/examples/#basic)

Your feedback is very appreciated!

## Acknowledgements

Thanks to everyone who have given feedback and submitted pull requests. A list of all the contributors can be found [here](https://github.com/wenzhixin/bootstrap-table/graphs/contributors).

## Release History

Look at the [Change Log](https://github.com/wenzhixin/bootstrap-table/blob/master/CHANGELOG.md)

## Local build

To build bootstrap-table locally please run:

```
grunt build
```

Result will appear in `dist` directory.

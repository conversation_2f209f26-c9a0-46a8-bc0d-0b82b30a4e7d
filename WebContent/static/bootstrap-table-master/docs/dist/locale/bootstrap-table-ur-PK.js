/**
 * Bootstrap Table Urdu translation
 * Author: <PERSON> <<EMAIL>>
 */
(function ($) {
    'use strict';

    $.fn.bootstrapTable.locales['ur-PK'] = {
        formatLoadingMessage: function () {
            return 'براۓ مہربانی انتظار کیجئے';
        },
        formatRecordsPerPage: function (pageNumber) {
            return pageNumber + ' ریکارڈز فی صفہ ';
        },
        formatShowingRows: function (pageFrom, pageTo, totalRows) {
            return 'دیکھیں ' + pageFrom + ' سے ' + pageTo + ' کے ' +  totalRows + 'ریکارڈز';
        },
        formatSearch: function () {
            return 'تلاش';
        },
        formatNoMatches: function () {
            return 'کوئی ریکارڈ نہیں ملا';
        },
        formatRefresh: function () {
            return 'تازہ کریں';
        },
        formatToggle: function () {
            return 'تبدیل کریں';
        },
        formatColumns: function () {
            return 'کالم';
        }
    };

    $.extend($.fn.bootstrapTable.defaults, $.fn.bootstrapTable.locales['ur-PK']);

})(jQuery);

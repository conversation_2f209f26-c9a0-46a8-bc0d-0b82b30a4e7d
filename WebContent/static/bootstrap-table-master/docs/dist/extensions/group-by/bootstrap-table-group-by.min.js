/*
* bootstrap-table - v1.9.1 - 2015-10-25
* https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2015 zhixin wen
* Licensed MIT License
*/
!function(a){"use strict";var b,c="data-tt-id",d="data-tt-parent-id",e={},f=void 0,g=function(b,c){for(var d=b.$body.find("tr").not("[data-tt-parent-id]"),e=0;e<d.length;e++)if(e===c)return a(d[e]).attr("data-tt-id");return void 0},h=function(b,c){var d={};return a.each(c,function(c,e){if(!e.IsParent)for(var f in e)isNaN(parseFloat(e[f]))||b.columns[a.fn.bootstrapTable.utils.getFieldIndex(b.columns,f)].groupBySumGroup&&(void 0===d[f]&&(d[f]=0),d[f]+=+e[f])}),d},i=function(a,g){return b.apply([a,g]),e[c.toString()]=g,a.IsParent?(f=g,delete e[d.toString()]):e[d.toString()]=void 0===f?g:f,e},j=function(){Object.keys=function(a){if(a!==Object(a))throw new TypeError("Object.keys called on a non-object");var b,c=[];for(b in a)Object.prototype.hasOwnProperty.call(a,b)&&c.push(b);return c}},k=function(a,b){for(var c=[],d=0;d<a.options.groupByField.length;d++)c.push(b[a.options.groupByField[d]]);return c},l=function(a,b,c){for(var d={},e=0;e<a.options.groupByField.length;e++)d[a.options.groupByField[e].toString()]=b[c][0][a.options.groupByField[e]];return d.IsParent=!0,d},m=function(b,c){var d={};return a.each(b,function(a,b){var e=JSON.stringify(c(b));d[e]=d[e]||[],d[e].push(b)}),Object.keys(d).map(function(a){return d[a]})},n=function(b,c){for(var d=[],e={},f=m(c,function(a){return k(b,a)}),g=0;g<f.length;g++)f[g].unshift(l(b,f,g)),b.options.groupBySumGroup&&(e=h(b,f[g]),a.isEmptyObject(e)||f[g].push(e));return d=d.concat.apply(d,f),!b.options.loaded&&d.length>0&&(b.options.loaded=!0,b.options.originalData=b.options.data,b.options.data=d),d};a.extend(a.fn.bootstrapTable.defaults,{groupBy:!1,groupByField:[],groupBySumGroup:!1,groupByInitExpanded:void 0,loaded:!1,originalData:void 0}),a.fn.bootstrapTable.methods.push("collapseAll","expandAll","refreshGroupByField"),a.extend(a.fn.bootstrapTable.COLUMN_DEFAULTS,{groupBySumGroup:!1});var o=a.fn.bootstrapTable.Constructor,p=o.prototype.init,q=o.prototype.initData;o.prototype.init=function(){if(!this.options.sortName&&this.options.groupBy&&this.options.groupByField.length>0){var a=this;Object.keys||j(),this.options.loaded=!1,this.options.originalData=void 0,b=this.options.rowAttributes,this.options.rowAttributes=i,this.$el.on("post-body.bs.table",function(){a.$el.treetable({expandable:!0,onNodeExpand:function(){a.options.height&&a.resetHeader()},onNodeCollapse:function(){a.options.height&&a.resetHeader()}},!0),void 0!==a.options.groupByInitExpanded&&("number"==typeof a.options.groupByInitExpanded?a.expandNode(a.options.groupByInitExpanded):"all"===a.options.groupByInitExpanded.toLowerCase()&&a.expandAll())})}p.apply(this,Array.prototype.slice.apply(arguments))},o.prototype.initData=function(a,b){this.options.sortName||this.options.groupBy&&this.options.groupByField.length>0&&(this.options.groupByField="string"==typeof this.options.groupByField?this.options.groupByField.replace("[","").replace("]","").replace(/ /g,"").toLowerCase().split(","):this.options.groupByField,a=n(this,a?a:this.options.data)),q.apply(this,[a,b])},o.prototype.expandAll=function(){this.$el.treetable("expandAll")},o.prototype.collapseAll=function(){this.$el.treetable("collapseAll")},o.prototype.expandNode=function(a){a=g(this,a),void 0!==a&&this.$el.treetable("expandNode",a)},o.prototype.refreshGroupByField=function(b){a.fn.bootstrapTable.utils.compareObjects(this.options.groupByField,b)||(this.options.groupByField=b,this.load(this.options.originalData))}}(jQuery);
# Documentation

Bootstrap table's documentation, included in this repo in the root directory, is built with [<PERSON><PERSON><PERSON>](http://jekyllrb.com/) and publicly hosted on http://bootstrap-table.wenzhixin.net.cn. The docs may also be run locally.

## Running documentation locally

1. If necessary, [install Jekyll](http://jekyllrb.com/docs/installation) (requires v2.5.x).
  - **Windows users:** Read [this unofficial](http://jekyll-windows.juthilo.com/) guide to get <PERSON><PERSON><PERSON> up and running without problems.

2. Install the Ruby-based syntax highlighter, [<PERSON>](https://github.com/jneen/rouge), with `gem install rouge`.
3. From the root / directory, run `jekyll serve` in the command line.
4. Open http://localhost:4000 in your browser, and voilà.

Learn more about using Jekyll by reading its [documentation](http://jekyllrb.com/docs/home/<USER>

pages:
  home:
    title: "Bootstrap Table"
    lead: "基于 Bootstrap 的 jQuery 表格插件，通过简单的设置，就可以拥有强大的单选、多选、排序、分页，以及编辑、导出、过滤（扩展）等等的功能。"
    sub_title: "专为 Twitter Bootstrap 设计"
    sub_lead: "Bootstrap table 通过简单的设置，就可以拥有强大的功能，大大提高工作效率，减少开发时间。"
    download: "下载"
    current_version: "当前版本："
  getting_started:
    title: "开始使用"
    lead: "Bootstrap Table的简单介绍，例如如何下载和使用，基本的模板等等。"
  examples:
    title: "例子"
    lead: "使用Bootstrap Table的例子。"
  documentation:
    title: "文档"
    lead: "文档包含了表格属性、列属性、事件、方法等等。"
  extensions:
    title: "扩展"
    lead: "Bootstrap Table 的扩展"
  faq:
    title: "常见问题"
    lead: "常见问题。"
  donate:
    title: "捐助"
    lead: "假如你喜欢 Bootstrap Table，假如你的项目使用到 Bootstrap Table，假如你想让 Bootstrap Table 更好……"
common:
  social_tip: "假如您喜欢 Bootstrap Table："
  qq_group: "QQ群："
# Download []({{ site.repo }}/blob/master/docs/_i18n/{{ site.lang }}/getting-started/download.md)

---

<p class="lead">
Bootstrap table (versión actual v{{ site.current_version }}) tiene maneras sencillas de empezar a usarla sin importar las habilidades. Hay que leer la documentación para cada necesidad en particular.
</p>

## Código fuente

Código fuente de css, JavaScript, locales, y extensiones, con nuestra documentación.

<a href="{{ site.master_zip }}" class="btn btn-lg btn-outline" role="button">Descargar el código fuente</a>

## Clonar vía GitHub

<a href="{{ site.repo }}" class="btn btn-lg btn-outline" role="button">Vía GitHub</a>

## CDN

Para utilizar CDN use estos links [CDNJS](http://www.cdnjs.com/libraries/bootstrap-table) o [bootcss](http://open.bootcss.com/bootstrap-table/).

```html
<!-- Latest compiled and minified CSS -->
<link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/bootstrap-table/{{ site.current_version }}/bootstrap-table.min.css">

<!-- Latest compiled and minified JavaScript -->
<script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-table/{{ site.current_version }}/bootstrap-table.min.js"></script>

<!-- Latest compiled and minified Locales -->
<script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-table/{{ site.current_version }}/locale/bootstrap-table-zh-CN.min.js"></script>
```

## Bower

Instalar y usar Bootstrap table's CSS, JavaScript, locales, y extensiones usando [Bower](http://bower.io/).

```bash
$ bower install bootstrap-table
```
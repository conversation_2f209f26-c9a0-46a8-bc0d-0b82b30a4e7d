# Select []({{ site.repo }}/blob/master/docs/_i18n/{{ site.lang }}/examples/select.md)

---

## Radio Select

Use las opciones `clickToSelect`, `selectItemName` y la opción de columna `radio` para mostra un radiobox en la tabla. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/29/embedded/html,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

## Checkbox Select

Use la opción `clickToSelect` y la opción de columna `checkbox` para mostrar un checkbox en la tabla. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/30/embedded/html,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

## Disabled Checkbox

Use las opciones `checkboxHeader`, `checkboxEnable` y la opción de columna `formatter` para deshabilitar el control. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/31/embedded/html,js,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

## Single Checkbox

Use la opción `singleSelect` para permitir solo un checkbox marcado a la vez en la tabla. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/32/embedded/html,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>
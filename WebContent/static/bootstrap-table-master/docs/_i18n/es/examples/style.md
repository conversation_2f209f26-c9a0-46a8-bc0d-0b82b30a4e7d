# Style []({{ site.repo }}/blob/master/docs/_i18n/{{ site.lang }}/examples/style.md)

---

## Classes

Use las opciones `height, classes, striped` y las opciones de columna `class, width` para setear los estilos de bootstrap table. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/15/embedded/html,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

## RowStyle

Use la opción `rowStyle` para setear el estilo de la fila de bootstrap table. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/16/embedded/html,js,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

## CellStyle

Use la opción de columna `cellStyle` para setear el estilo de la celda de bootstrap table. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/30sx4h3t/embedded/html,js,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>
# Pagination []({{ site.repo }}/blob/master/docs/_i18n/{{ site.lang }}/examples/pagination.md)

---

## Client Side

La opción de paginación por defecto de la tabla es `client`. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="400" data-src="http://jsfiddle.net/wenyi/e3nk137y/42/embedded/html,js,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>


## Server Side

Use la opción sidePagination: `server` para definir la paginación de la tabla del lado del servidor. _by [@mikepenz](https://github.com/mikepenz)_

<iframe width="100%" height="400" data-src="http://jsfiddle.net/4r6g4cfu/3/embedded/html,js,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

Aquí está el código de la paginación server-side.
https://gist.github.com/mikepenz/06df1204cbb65b874cb5
 Es un api rápida, justo para saber cómo se usa y listo.

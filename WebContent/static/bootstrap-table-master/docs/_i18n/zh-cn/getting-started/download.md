# 下载 []({{ site.repo }}/blob/master/docs/_i18n/{{ site.lang }}/getting-started/download.md)

---

<p class="lead">
Bootstrap table (当前版本 v{{ site.current_version }}) 可以有几种快速入门的方法，每种适合不同技能等级的人使用，往下看哪种适合你。
</p>

## 源码

包含了 css，JavaScript，多语言和扩展，以及文档。

<a href="{{ site.master_zip }}" class="btn btn-lg btn-outline" role="button">下载源码</a>

## 克隆或者 Fork 通过 GitHub

<a href="{{ site.repo }}" class="btn btn-lg btn-outline" role="button">通过 GitHub</a>

## CDN

[CDNJS](http://www.cdnjs.com/libraries/bootstrap-table) 或者 [bootcss](http://open.bootcss.com/bootstrap-table/) 提供了 CDN 来支持 Bootstrap table 的 CSS 和 JavaScript 文件链接。

```html
<!-- Latest compiled and minified CSS -->
<link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/bootstrap-table/{{ site.current_version }}/bootstrap-table.min.css">

<!-- Latest compiled and minified JavaScript -->
<script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-table/{{ site.current_version }}/bootstrap-table.min.js"></script>

<!-- Latest compiled and minified Locales -->
<script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-table/{{ site.current_version }}/locale/bootstrap-table-zh-CN.min.js"></script>
```

## Bower

通过 [Bower](http://bower.io/) 来安装和管理 Bootstrap table 的 CSS，JavaScript, 多语言和扩展。

```bash
$ bower install bootstrap-table
```

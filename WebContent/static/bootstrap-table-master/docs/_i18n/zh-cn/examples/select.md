# Select []({{ site.repo }}/blob/master/docs/_i18n/{{ site.lang }}/examples/select.md)

---

## Radio Select

Use `clickToSelect`, `selectItemName` options and `radio` column option to show a radio select table. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/29/embedded/html,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

## Checkbox Select

Use `clickToSelect` option and `checkbox` column option to show a checkbox select table. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/30/embedded/html,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

## Disabled Checkbox

Use `checkboxHeader`, `checkboxEnable` options and `formatter` column option to disabled select input. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/31/embedded/html,js,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

## Single Checkbox

Use `singleSelect` option to allow checkbox selecting only one row. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/32/embedded/html,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>
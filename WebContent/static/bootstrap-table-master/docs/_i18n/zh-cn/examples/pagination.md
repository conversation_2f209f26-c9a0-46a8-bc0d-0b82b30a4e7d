# Pagination []({{ site.repo }}/blob/master/docs/_i18n/{{ site.lang }}/examples/pagination.md)

---

## Client Side

The default side pagination of table is `client`. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="400" data-src="http://jsfiddle.net/wenyi/e3nk137y/42/embedded/html,js,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>


## Server Side

Use sidePagination: `server` option to define the server side pagination of table. _by [@mikepenz](https://github.com/mikepenz)_

<iframe width="100%" height="400" data-src="http://jsfiddle.net/4r6g4cfu/3/embedded/html,js,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

Here's the server-side code.
https://gist.github.com/mikepenz/06df1204cbb65b874cb5
 It's a quick and dirty api, just to showcase the usage.

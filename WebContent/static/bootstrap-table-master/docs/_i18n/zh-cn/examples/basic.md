# Basic []({{ site.repo }}/blob/master/docs/_i18n/{{ site.lang }}/examples/basic.md)

---

## Table from html

Transform table from an existing, unformatted html table. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/11/embedded/html,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

## Table from data

Transform table from an existing data. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/13/embedded/html,js,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

## Table from url

Transform table from an url. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="300" data-src="http://jsfiddle.net/wenyi/e3nk137y/14/embedded/html,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>
# Download []({{ site.repo }}/blob/master/docs/_i18n/{{ site.lang }}/getting-started/download.md)

---

<p class="lead">
Bootstrap table (currently v{{ site.current_version }}) has a few easy ways to quickly get started, each one appealing to a different skill level and use case. Read through to see what suits your particular needs.
</p>

## Source code

Source css, JavaScript, locales, and extensions, along with our docs.

<a href="{{ site.master_zip }}" class="btn btn-lg btn-outline" role="button">Download source</a>

## Clone or fork via GitHub

<a href="{{ site.repo }}" class="btn btn-lg btn-outline" role="button">Via GitHub</a>

## CDN

The folks over at [CDNJS](http://www.cdnjs.com/libraries/bootstrap-table) and [bootcss](http://open.bootcss.com/bootstrap-table/) graciously provide CDN support for CSS and JavaScript of Bootstrap table. Just use these CDN links.

```html
<!-- Latest compiled and minified CSS -->
<link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/bootstrap-table/{{ site.current_version }}/bootstrap-table.min.css">

<!-- Latest compiled and minified JavaScript -->
<script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-table/{{ site.current_version }}/bootstrap-table.min.js"></script>

<!-- Latest compiled and minified Locales -->
<script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-table/{{ site.current_version }}/locale/bootstrap-table-zh-CN.min.js"></script>
```

## Bower

Install and manage Bootstrap table's CSS, JavaScript, locales, and extensions using [Bower](http://bower.io/).

```bash
$ bower install bootstrap-table
```

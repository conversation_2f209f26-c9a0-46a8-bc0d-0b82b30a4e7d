# Events []({{ site.repo }}/blob/master/docs/_i18n/{{ site.lang }}/examples/events.md)

---

## Basic Events

Table events. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="500" data-src="http://jsfiddle.net/e3nk137y/2106/embedded/html,js,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>

## Column Events

Use `formatter`, `events` column option to define the custom events. _by [@wenzhixin](https://github.com/wenzhixin)_

<iframe width="100%" height="500" data-src="http://jsfiddle.net/wenyi/e3nk137y/39/embedded/html,js,css,result" allowfullscreen="allowfullscreen" frameborder="0"></iframe>


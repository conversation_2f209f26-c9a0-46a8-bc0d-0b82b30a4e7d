body {
  position: relative; /* For scrollspy */
}

/* By default it's not affixed in mobile views, so undo that */
.bs-sidebar.affix {
  position: static;
}
@media (min-width: 768px) {
  .bs-sidebar {
    padding-left: 20px;
  }
}

/* First level of nav */
.bs-sidenav {
  margin-top: 20px;
  margin-bottom: 20px;
}

/* All levels of nav */
.bs-sidebar .nav > li > a {
  display: block;
  padding: 4px 20px;
  font-size: 13px;
  font-weight: 500;
  color: #999;
}
.bs-sidebar .nav > li > a:hover,
.bs-sidebar .nav > li > a:focus {
  padding-left: 19px;
  color: #6D6D6D;
  text-decoration: none;
  background-color: transparent;
  border-left: 1px solid #6D6D6D;
}
.bs-sidebar .nav > .active > a,
.bs-sidebar .nav > .active:hover > a,
.bs-sidebar .nav > .active:focus > a {
  padding-left: 18px;
  font-weight: bold;
  color: #6D6D6D;
  background-color: transparent;
  border-left: 2px solid #6D6D6D;
}

/* Nav: second level (shown on .active) */
.bs-sidebar .nav .nav {
  display: none; /* Hide by default, but at >768px, show it */
  padding-bottom: 10px;
}
.bs-sidebar .nav .nav > li > a {
  padding-top: 1px;
  padding-bottom: 1px;
  padding-left: 30px;
  font-size: 12px;
  font-weight: normal;
}
.bs-sidebar .nav .nav > li > a:hover,
.bs-sidebar .nav .nav > li > a:focus {
  padding-left: 29px;
}
.bs-sidebar .nav .nav > .active > a,
.bs-sidebar .nav .nav > .active:hover > a,
.bs-sidebar .nav .nav > .active:focus > a {
  padding-left: 28px;
  font-weight: 500;
}

/* Back to top (hidden on mobile) */
.back-to-top {
  display: none;
  padding: 4px 10px;
  margin-top: 10px;
  margin-left: 10px;
  font-size: 12px;
  font-weight: 500;
  color: #999;
}
.back-to-top:hover {
  color: #6D6D6D;
  text-decoration: none;
}

@media (min-width: 768px) {
  .back-to-top {
    display: block;
  }
}

/* Show and affix the side nav when space allows it */
@media (min-width: 992px) {
  .bs-sidebar .nav > .active > ul {
    display: block;
  }
  /* Widen the fixed sidebar */
  .bs-sidebar.affix,
  .bs-sidebar.affix-bottom {
    width: 213px;
  }
  .bs-sidebar.affix {
    position: fixed; /* Undo the static from mobile first approach */
    top: 20px;
  }
  .bs-sidebar.affix-bottom {
    position: absolute; /* Undo the static from mobile first approach */
  }
  .bs-sidebar.affix-bottom .bs-sidenav,
  .bs-sidebar.affix .bs-sidenav {
    margin-top: 0;
    margin-bottom: 0;
  }
}
@media (min-width: 1200px) {
  /* Widen the fixed sidebar again */
  .bs-sidebar.affix-bottom,
  .bs-sidebar.affix {
    width: 263px;
  }
}

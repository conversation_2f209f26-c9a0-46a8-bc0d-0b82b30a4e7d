<header class="navbar navbar-static-top bs-docs-nav" id="top" role="banner">
    <div class="container">
        <div class="navbar-header">
            <button class="navbar-toggle collapsed" type="button" data-toggle="collapse"
                    data-target=".bs-navbar-collapse">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a href="{{ site.baseurl }}/" class="navbar-brand">Bootstrap Table</a>
        </div>
        <nav class="collapse navbar-collapse bs-navbar-collapse" role="navigation">
            <ul class="nav navbar-nav">
                <li {% if page.slug == "getting-started" %} class="active"{% endif %}>
                    <a href="{{ site.baseurl }}/getting-started/">{% t pages.getting_started.title %}</a>
                </li>
                <li {% if page.slug == "documentation" %} class="active"{% endif %}>
                    <a href="{{ site.baseurl }}/documentation/">{% t pages.documentation.title %}</a>
                </li>
                <li>
                    <a href="http://issues.wenzhixin.net.cn/bootstrap-table/" target="_blank">{% t pages.examples.title %}</a>
                </li>
                <li {% if page.slug == "extensions" %} class="active"{% endif %}>
                    <a href="{{ site.baseurl }}/extensions/">{% t pages.extensions.title %}</a>
                </li>
                <li {% if page.slug == "donate" %} class="active"{% endif %}>
                    <a href="{{ site.baseurl }}/donate/">{% t pages.donate.title %}</a>
                </li>
                <li {% if page.slug == "faq" %} class="active"{% endif %}>
                    <a href="{{ site.baseurl }}/faq/">{% t pages.faq.title %}</a>
                </li>
                <li>
                    <a href="http://stackoverflow.com/questions/tagged/bootstrap-table" target="_blank">Questions / Helps</a>
                </li>
                <li>
                    <a href="{{ site.repo }}" target="_blank">GitHub</a>
                </li>
            </ul>
            <ul class="nav navbar-nav navbar-right">
                <!-- Localization -->
                <li>
                    <a href="javascript:void(0)" class="dropdown-toggle" data-toggle="dropdown">
                        <span class="language">English</span> <span class="caret"></span>
                    </a>
                    <ul class="dropdown-menu">
                        {% for language in site.languages %}
                        <li data-language="{{ language }}">
                            <a href="javascript:void(0)">
                                {{ site.languages_string[forloop.index0] }}
                            </a>
                        </li>
                        {% endfor %}
                    </ul>
                </li>
            </ul>
        </nav>
    </div>
</header>

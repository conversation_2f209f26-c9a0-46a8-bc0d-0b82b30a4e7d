### Latest release

#### v{{ site.current_version }} (2015-10-25)

- [bug] Removed no records events.
- [bug] Fixed cardView fieldIndex error.
- [bug] Fixed #1130: table-condensed is not working.
- [bug] Fixed #1482: export all server sidePagination bug(export extension).
- [bug] Fixed #1248: IE8 does not support indexOf function.
- [bug] Fixed #1491: (un)check event element argument missing.
- [bug] Fixed Italian translation.
- [bug] Unified naming of MS in type names(export extension).
- [bug] Fixed selectPage method fails(cookie extension).
- [bug] Add ja-JP and ms-MY translation for formatAllRows.
- [enh] UniqueId can also be stored in a row's data property.
- [enh] Use default settings when cookie settings don't exist(cookie extension).
- [enh] Expand `filterBy` to accept and array of values.
- [enh] Added `updateByUniqueId` method.
- [doc] Added `iconSize` docs.
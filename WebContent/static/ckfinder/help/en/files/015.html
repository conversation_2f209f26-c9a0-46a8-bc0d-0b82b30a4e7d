<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder User's Guide</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Keyboard Shortcuts</h1>
	<p>
		Many functions in CKFinder have their equivalent keyboard shortcuts. This is one
		of the reasons why working with the file browser is both simple and efficient.</p>
	<p>
		The list below contains available keyboard shortcuts grouped by problem areas.</p>
	<h2>CKFinder Interface</h2>
	<ul>
		<li><em>Alt+F8</em> &ndash; enters the <strong><a href="003.html">Folders Pane</a></strong>.</li>
		<li><em>Alt+F9</em> &ndash; enters the <strong><a href="004.html">Files Pane</a></strong>.</li>
		<li><em>Alt+F10</em> &ndash; enters the <strong><a href="005.html">Toolbar</a></strong>.</li>
		<li><em>Alt+U</em> &ndash; opens the <strong><a href="006.html">Upload Pane</a></strong>.</li>
		<li><em>(Ctrl+)Shift+F10</em> &ndash; opens the <strong><a href="012.html">Context Menu</a></strong> of a file or folder.</li>
		<li><em>Esc</em> &ndash; equivalent of the <strong>Cancel</strong> button. Closes
		a CKFinder dialog window or context menu without saving any changes.</li>
		<li><em>Enter</em> &ndash; equivalent of the <strong>OK</strong> button in a dialog window.
		Selects a CKFinder function from the toolbar or context menu.</li>
		<li><em>Left and Right Arrows</em> &ndash; move between toolbar buttons.</li>
		<li><em>Up and Down Arrows</em> or <em>Tab and Shift+Tab</em> &ndash; move between context menu options.</li>
	</ul>
	<h2>
		Files Pane Navigation</h2>
	<ul>
		<li><em>Page Down</em> &ndash; scrolls down the <strong>Files Pane</strong>.</li>
		<li><em>Page Up</em> &ndash; scrolls up the <strong>Files Pane</strong>.</li>
		<li><em>End</em> &ndash; scrolls the <strong>Files Pane</strong> to the last row.</li>
		<li><em>Home</em> &ndash; scrolls the <strong>Files Pane</strong> to the first row.</li>
	</ul>
	<h2>
		Working with Files and Folders</h2>
	<ul>
		<li><em>F2</em> &ndash; equivalent to the <strong>Rename</strong> command.</li>
		<li><em>Del</em> &ndash; equivalent to the <strong>Delete</strong> command.</li>
	</ul>
</body>
</html>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKFinder &mdash; Podręcznik Użytkownika</title>
	<link href="../../files/other/help.css" type="text/css" rel="stylesheet" />
	<script type="text/javascript" src="../../files/other/help.js"></script>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="noindex, nofollow" />
</head>
<body>
	<h1>
		Pasek statusu</h1>
	<p>
		<strong>Pasek statusu (Status Bar)</strong> to obszar na dole interfejsu
		CKFindera przeznaczony do wyświetlania informacji dotyczących wybranego pliku,
		całkowitej liczby plików w folderze itd.</p>
	<p>
		Po wybraniu pliku w CKFinderze w <strong>pasku statusu</strong> wyświetlone
		zostaną szczegółowe informacje na temat tego pliku, w tym jego nazwa, rozmiar,
		a także data ostatniej modyfikacji. Na przykład:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_status_file.png" width="187" height="39" alt="Pasek statusu CKFindera dla wybranego pliku" />&nbsp;</p>
	<p>
		Jeśli żadne pliki nie są wybrane, w <strong>pasku statusu</strong> zostanie
		zamiast tego wyświetlona całkowita liczba plików znajdujących się w folderze
		bieżącym. Na przykład:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_status_folder.png" width="187" height="39" alt="Pasek statusu CKFindera przy braku wyboru pliku" />&nbsp;</p>
	<p>
		Jeśli folder jest pusty, w <strong>pasku statusu</strong> zostanie wyświetlony
		odpowiedni komunikat. Na przykład:</p>
	<p style="text-align: center">
		<img src="../../files/images/CKFinder_status_empty.png" width="187" height="39" alt="Pasek statusu CKFindera dla pustego folderu" />&nbsp;</p>
</body>
</html>

# Highlight.js

Highlight.js highlights syntax in code examples on blogs, forums and
in fact on any web pages. It's very easy to use because it works
automatically: finds blocks of code, detects a language, highlights it.

Autodetection can be fine tuned when it fails by itself (see "Heuristics").


## Installation and usage

Downloaded package includes file "highlight.pack.js" which is a full compressed
version of the library intended to use in production. All uncompressed source
files are also available, feel free to look into them!

The script is installed by linking to a single file and making a single
initialization call:

    <script type="text/javascript" src="highlight.pack.js"></script>
    <script type="text/javascript">
      hljs.initHighlightingOnLoad();
    </script>

Also you can replaces TAB ('\x09') characters used for indentation in your code
with some fixed number of spaces or with a `<span>` to set them special styling:

    <script type="text/javascript">
      hljs.tabReplace = '    '; // 4 spaces
      // ... or
      hljs.tabReplace = '<span class="indent">\t</span>';

      hljs.initHighlightingOnLoad();
    </script>

Then the script looks in your page for fragments `<pre><code>...</code></pre>`
that are used traditionally to mark up code examples. Their content is
marked up by logical pieces with defined class names.


### Custom initialization

If you use different markup for code blocks you can initialize them manually
with `highlightBlock(code, tabReplace)` function. It takes a DOM element
containing the code to highlight and optionally a string with which to replace
TAB characters.

Initialization using for example jQuery might look like this:

    $(document).ready(function() {
      $('pre code').each(function(i, e) {hljs.highlightBlock(e, '    ')});
    });


### Styling

Elements of code marked up with classes can be styled as desired:

    .comment {
      color: gray;
    }

    .keyword {
      font-weight: bold;
    }

    .python .string {
      color: blue;
    }

    .html .atribute .value {
      color: green;
    }

Highligt.js comes with several style themes located in "styles" directory that
can be used directly or as a base for your own experiments.

A full list of available classes is below ("Languages").


## Export

File export.html contains a little program that shows and allows to copy and paste
an HTML code generated by the highlighter for any code snippet. This can be useful
in situations when one can't use the script itself on a site.


## Languages

This is a full list of available classes corresponding to languages'
syntactic structures. In parentheses after language names are identifiers
used as class names in `<code>` element.

Python ("python"):

  keyword          keyword
  built_in         built-in objects (None, False, True and Ellipsis)
  number           number
  string           string (of any type)
  comment          comment
  decorator        @-decorator for functions
  function         function header "def some_name(...):"
  class            class header "class SomeName(...):"
  title            name of a function or a class inside a header
  params           everything inside parentheses in a function's or class' header

Python profiler results ("profile"):

  number           number
  string           string
  builtin          builtin function entry
  filename         filename in an entry
  summary          profiling summary
  header           header of table of results
  keyword          column header
  function         function name in an entry (including parentheses)
  title            actual name of a function in an entry (excluding parentheses)

Ruby ("ruby"):

  keyword          keyword
  string           string
  subst            in-string substitution (#{...})
  comment          comment
  yardoctag        YARD tag
  function         function header "def some_name(...):"
  class            class header "class SomeName(...):"
  title            name of a function or a class inside a header
  parent           name of a parent class
  symbol           symbol
  instancevar      instance variable

Perl ("perl"):

  keyword          keyword
  comment          comment
  number           number
  string           string
  regexp           regular expression
  sub              subroutine header (from "sub" till "{")
  variable         variable starting with "$", "%", "@"
  operator         operator
  pod              plain old doc

PHP ("php"):

  keyword          keyword
  number           number
  string           string (of any type)
  comment          comment
  phpdoc           phpdoc params in comments
  variable         variable starting with "$"
  preprocessor     preprocessor marks: "<?php" and "?>"

Scala ("scala"):

  keyword          keyword
  number           number
  string           string
  comment          comment
  annotaion        annotation
  javadoc          javadoc comment
  javadoctag       @-tag in javadoc
  class            class header
  title            class name inside a header
  params           everything in parentheses inside a class header
  inheritance      keywords "extends" and "with" inside class header

XML ("xml"):

  tag              any tag from "<" till ">"
  comment          comment
  pi               processing instruction (<? ... ?>)
  cdata            CDATA section
  attribute        attribute
  value            attribute's value

HTML ("html"):

  keyword          HTML tag
  tag              any tag from "<" till ">"
  comment          comment
  doctype          <!DOCTYPE ... > declaration
  attribute        tag's attribute with or without value
  value            attribute's value

CSS ("css"):

  keyword          HTML tag when in selectors, CSS keyword when in rules
  id               #some_name in selectors
  class            .some_name in selectors
  at_rule          @-rule till first "{" or ";"
  attr_selector    attribute selector (square brackets in a[href^=http://])
  pseudo           pseudo classes and elemens (:after, ::after etc.)
  comment          comment
  rules            everything from "{" till "}"
  value            property's value inside a rule, from ":" till ";" or
                   till the end of rule block
  number           number within a value
  string           string within a value
  hexcolor         hex color (#FFFFFF) within a value
  function         CSS function within a value
  params           everything between "(" and ")" within a function

Django ("django"):

  keyword          HTML tag in HTML, default tags and default filters in templates
  tag              any tag from "<" till ">"
  comment          comment
  doctype          <!DOCTYPE ... > declaration
  attribute        tag's attribute with or withou value
  value            attribute's value
  template_tag     template tag {% .. %}
  variable         template variable {{ .. }}
  template_comment template comment, both {# .. #} and {% comment %}
  filter           filter from "|" till the next filter or the end of tag
  argument         filter argument

Javascript ("javascript"):

  keyword          keyword
  comment          comment
  number           number
  literal          special literal: "true", "false" and "null"
  string           string
  regexp           regular expression
  function         header of a function
  title            name of a function inside a header
  params           everything inside parentheses in a function's header

VBScript ("vbscript"):

  keyword          keyword
  number           number
  string           string
  comment          comment
  built_in         built-in function

Lua ("lua"):

  keyword          keyword
  number           number
  string           string
  comment          comment
  built_in         built-in operator
  function         header of a function
  title            name of a function inside a header
  params           everything inside parentheses in a function's header
  long_brackets    multiline string in [=[ .. ]=]

Delphi ("delphi"):

  keyword          keyword
  comment          comment (of any type)
  number           number
  string           string
  function         header of a function, procedure, constructor and destructor
  title            name of a function, procedure, constructor or destructor
                   inside a header
  params           everything inside parentheses in a function's header
  class            class' body from "= class" till "end;"

Java ("java"):

  keyword          keyword
  number           number
  string           string
  comment          commment
  annotaion        annotation
  javadoc          javadoc comment
  class            class header from "class" till "{"
  title            class name inside a header
  params           everything in parentheses inside a class header
  inheritance      keywords "extends" and "implements" inside class header

C++ ("cpp"):

  keyword          keyword
  number           number
  string           string and character
  comment          comment
  preprocessor     preprocessor directive
  stl_container    instantiation of STL containers ("vector<...>")

C# ("cs"):

  keyword          keyword
  number           number
  string           string
  comment          commment
  xmlDocTag        xmldoc tag ("///", "<!--", "-->", "<..>")

RenderMan RSL ("rsl"):

  keyword          keyword
  number           number
  string           string (including @"..")
  comment          comment
  preprocessor     preprocessor directive
  shader           sahder keywords
  shading          shading keywords
  built_in         built-in function

RenderMan RIB ("rib"):

  keyword          keyword
  number           number
  string           string
  comment          comment
  commands         command

Maya Embedded Language ("mel"):

  keyword          keyword
  number           number
  string           string
  comment          comment
  variable         variable

SQL ("sql"):

  keyword          keyword (mostly SQL'92 and SQL'99)
  number           number
  string           string (of any type: "..", '..', `..`)
  comment          comment
  aggregate        aggregate function

Smalltalk ("smalltalk"):

  keyword          keyword
  number           number
  string           string
  comment          commment
  symbol           symbol
  array            array
  class            name of a class
  char             char
  localvars        block of local variables

Lisp ("lisp"):

  keyword          keyword
  number           number
  string           string
  comment          commment
  variable         variable
  literal          b, t and nil
  list             non-quoted list
  title            first symbol in a non-quoted list
  body             remainder of the non-quoted list
  quoted_list      quoted list, both "(quote .. )" and "'(..)"

Ini ("ini"):

  title            title of a section
  value            value of a setting of any type
  string           string
  number           number
  keyword          boolean value keyword

Apache ("apache"):

  keyword          keyword
  number           number
  comment          commment
  literal          On and Off
  sqbracket        variables in rewrites "%{..}"
  cbracket         options in rewrites "[..]"
  tag              begin and end of a configuration section

Nginx ("nginx"):

  keyword          keyword
  string           string
  number           number
  comment          comment
  built_in         built-in constant
  variable         $-variable

DOS ("dos"):

  keyword          keyword
  flow             batch control keyword
  stream           DOS special files ("con", "prn", ...)
  winutils         some commands (see dos.js specifically)
  envvar           environment variables

Bash ("bash"):

  keyword          keyword
  string           string
  number           number
  comment          comment
  literal          special literal: "true" и "false"
  variable         variable
  shebang          script interpreter header

Diff ("diff"):

  header           file header
  chunk            chunk header within a file
  addition         added lines
  deletion         deleted lines
  change           changed lines

Axapta ("axapta"):

  keyword          keyword
  number           number
  string           string
  comment          commment
  class            class header from "class" till "{"
  title            class name inside a header
  params           everything in parentheses inside a class header
  inheritance      keywords "extends" and "implements" inside class header
  preprocessor     preprocessor directive

1C ("1c"):

  keyword          keyword
  number           number
  date             date
  string           string
  comment          commment
  function         header of function or procudure
  title            function name inside a header
  params           everything in parentheses inside a function header
  preprocessor     preprocessor directive

AVR assembler ("avrasm"):

  keyword          keyword
  built_in         pre-defined register
  number           number
  string           string
  comment          commment
  label            label
  preprocessor     preprocessor directive
  localvars        substitution in .macro

Parser3 ("parser3"):

  keyword          keyword
  number           number
  comment          commment
  variable         variable starting with "$"
  preprocessor     preprocessor directive
  title            user-defined name starting with "@"

TeX ("tex"):

  comment          comment
  number           number
  command          command
  parameter        parameter
  formula          formula
  special          special symbol


## Heuristics

Autodetection of a code's language is done with a simple heuristics:
the program tries to highlight a fragment with all available languages and
counts all syntactic structures that it finds along the way. The language
with greatest count wins.

This means that in short fragments the probability of an error is high
(and it really happens sometimes). In this cases you can set the fragment's
language explicitly by assigning a class to the `<code>` element:

    <pre><code class="html">...</code></pre>

You can use class names recommended in HTML5: "language-html",
"language-php". Classes also can be assigned to the `<pre>` element.

To disable highlighting of a fragment altogether use "no-highlight" class:

    <pre><code class="no-highlight">...</code></pre>

## Contacts

Version: 5.11
URL:     http://softwaremaniacs.org/soft/highlight/en/
Author:  Ivan Sagalaev (<EMAIL>)

For the license terms see LICENSE files.
For the list of contributors see AUTHORS.en.txt file.

var configStr;
var imgMap = {};
var fileMap = {};
var sortImgs = [];
var sortFiles = [];
var activeId;
var enrollType;
var userId;
var state = 0;
window.onload = function() {
	configStr = window.localStorage.getItem("configStr");
	if(configStr == null){
		var id = location.href.split("?")[1].split("&")[0].split("=")[1];
		var uid = location.href.split("?")[1].split("&")[1].split("=")[1];
		var formData=new FormData();
		var json ={
			"id":id,
			"userId":uid,
		}
		formData.append("json",JSON.stringify(json));
		jQuery.ajax({
				url:"../../../api/code/codeConfig",
				type:"post",
				data:formData,
				cache: false,
				contentType: false,
				processData: false,
				success: function (data) {
					console.log(data)
					activeId = data.body.activeId;
					enrollType = data.body.type;
					console.log(enrollType)
					configStr = data.body.configStr;
					userId = data.body.userId;
					console.log(userId)
					renderHtml(configStr);
				},
				error:function (xhr,ajaxOptions, thrownError) {
					alert(xhr.status)
				}
			}
		);
	}
	if(configStr != null)
		renderHtml(JSON.parse(configStr));
}


//提交按钮
function submit() {
	if (state === 0){
		var list = configStr.config;
		var falg = true;
		//判断必填
		for (let index in list) {
			var a = list[index];
			if(a.req === '1'){
				if (a.columnType === "uploaderImg"){
					console.log(imgMap[a.title])
					if (imgMap[a.title] == null){
						falg = false;
						top.layer.alert(a.title + '未上传', {icon: 0, title: '警告'})
						return
					}
				}else if(a.columnType === "uploaderFile"){
					if (Object.keys(fileMap).length === 0){
						falg = false;
						top.layer.alert(a.title + '未上传', {icon: 0, title: '警告'})
						return
					}
				}else {
					if (a.value === "") {
						falg = false;
						top.layer.alert(a.title + '未填写', {icon: 0, title: '警告'})
						return
					}
				}
			}
		}
		state = 1;
		if(falg){
            var loadingIndex = top.layer.load(1, { //icon支持传入0-2
                shade: [0.5, '#fff'] //0.5透明度的灰色背景
            });
			var formData = new FormData();
			formData.append("id",activeId);
			formData.append("type",enrollType);
			formData.append("userId",userId);
			for(var key in imgMap){
				for (var i = 0; i < imgMap[key].length ; i++){
					if (imgMap[key][i] !== null){
						formData.append('files', imgMap[key][i]);
						sortImgs.push(key)
					}
				}
			}
			for(var key in fileMap){
				if (fileMap[key] !== null) {
					formData.append('fileItems', fileMap[key]);
					sortFiles.push(key)
				}
			}
			formData.append("sortImg",sortImgs);
			formData.append("sortFile",sortFiles);
			formData.append("json",JSON.stringify(configStr.config));
			jQuery.ajax({
					url:"../../../api/code/saveEnroll",
					dataType:"json",
					type:"POST",
					data:formData,
					contentType: false,
					processData: false,
					success: function (data) {
                        top.layer.close(loadingIndex);
						console.log(data)
						top.layer.alert(data.msg,{icon: 3, title:'系统提示'})
						if (data.success){
							state = 1;
							jQuery("#submit").html("已报名").css("background","#999").attr("disabled",true).css("pointer-events","none");
						}else{
                            state = 0;
                        }
					},
					error:function (xhr,ajaxOptions, thrownError) {
						state = 0;
					}
				}
			);

		}
	}else{

	}
}

//渲染页面html
function renderHtml(arr) {
	var html = '';
	var config = arr.config;
	for (var i = 0; i < config.length; i++){
		config[i] = JSON.parse(config[i]);
		var columnType = config[i].columnType;
		if(columnType === "inputText"){
			html = getInputText(config[i].title,config[i].tip,config[i].value);
		}
		if(columnType === "textareaText"){
			html = getTextarea(config[i].title,config[i].tip,config[i].value);
		}
		if(columnType === "numberText"){
			html = getNumber(config[i].title,config[i].tip,config[i].unit,config[i].value);
		}
		if(columnType === "singleSelect"){
			html = getSingleSelect(config[i].title,config[i].items,config[i].tip,config[i].value);
		}
		if(columnType === "multipleSelect"){
			html = getMultipleSelect(config[i].title,config[i].items,config[i].value);
		}
		if(columnType === "selectDate"){
			html = getSelectDate(config[i].title,config[i].type,config[i].type,config[i].value);
		}
		if(columnType === "dateRange"){
			html = getDateRange(config[i].title,config[i].secondTitle,config[i].type,config[i].type,config[i].value);
		}
		if(columnType === "uploaderImg"){
			html = getUploaderImg(config[i].title,config[i].value);
			var length = config[i].value.split(",").length;
			if (config[i].value !== "" && config[i].value !== null){
				var imgs = [];
				for (let j = 0; j < length; j++) {
					imgs.push(null);
					imgMap[config[i].title] = imgs;
				}
			}
		}
		if(columnType === "tips"){
			html = getTips(config[i].textnote,config[i].value);
		}
		if(columnType === "moneyInput"){
			html = getMoneyInput(config[i].title,config[i].tip,config[i].value);
		}
		if(columnType === "uploaderFile"){
			html = getUploaderFile(config[i].title,config[i].value);
			fileMap[config[i].title] = null;
		}
		jQuery(".form-list").append(html);
	}
	console.log(imgMap);
	init();
	imgFunction();
}


// 开启
function init() {
	/* 日期 */
	jQuery('.select-date').each(function() {
		var title = jQuery(this).attr('title');
		new Jdate({
			el: this,
			format: $(this).attr("date-type"),
			beginYear: 1949,
			endYear: 2100,
			confirm: function(date) {
				for (let index in configStr.config) {
					if(configStr.config[index].columnType === "dateRange"){
						if (configStr.config[index].title === title || configStr.config[index].secondTitle == title) {
							if (configStr.config[index].value === "") {
								configStr.config[index].value = date
							} else {
								configStr.config[index].value = configStr.config[index].value + "至" + date;
							}
						}
					}else{
						if (configStr.config[index].title === title) {
							configStr.config[index].value = date;
						}
					}
				}
				console.log(configStr);
			}
		})
		/*jQuery(this).datetimePicker({
			onChange: function(p, values, displayValues) {
			}
		});*/
	})

	/* 注册所有图片选择器 */
	var tmpl = '<li class="weui-uploader__file" style="background-image:url(#url#)"><span index="#xb#" title="#title#" class="delImg" style="position: relative;top: -7%;right: -85%;color: red;background: #FFF;border-radius: 25px">×</span></li>',
		jQueryuploaderInput = jQuery(".uploaderInput");

	jQueryuploaderInput.on("change", function(e) {
		var jQueryuploaderFiles = $(this).parent().parent().children(".uploaderFiles");
		var src, url = window.URL || window.webkitURL || window.mozURL,
			files = e.target.files;
		console.log(files.length)
		for (var i = 0, len = files.length; i < len; ++i) {
			var file = files[i];
			if (url) {
				src = url.createObjectURL(file);
			} else {
				src = e.target.result;
			}
			if (file.size >= 10485760) {
				top.layer.alert(jQuery(this).attr("title") + '图片不能超过10M', {icon: 0, title: '警告'});
			} else {
				var imgs = [];
				if (imgMap[jQuery(this).attr("title")] !== undefined) {
					for (let j = 0; j < imgMap[jQuery(this).attr("title")].length; j++) {
						imgs.push(imgMap[jQuery(this).attr("title")][j]);
					}
				}
				imgs.push(file);
				imgMap[jQuery(this).attr("title")] = imgs;
				console.log(imgMap);
				jQueryuploaderFiles.append(jQuery(tmpl.replace('#url#', src).replace("#title#", jQuery(this).attr("title"))));
			}
		}
		imgFunction();
	});

	/* 注册所有附件选择器 */
	var jQueryuploaderAnnexFileInput = jQuery(".uploaderAnnexInput"),
		fileShow = "<li class=\"weui-uploader__file\" ><span style='font-size: 10px'>#fielName#</span></li>"

	jQueryuploaderAnnexFileInput.on("change", function(e) {
		var jQueryuploaderAnnexFiles = $(this).parent().parent().children(".uploaderAnnex");
		var src, url = window.URL || window.webkitURL || window.mozURL,
			files = e.target.files;
		for (var i = 0, len = files.length; i < len; ++i) {
			var file = files[i];
			if (url) {
				src = url.createObjectURL(file);
			} else {
				src = e.target.result;
			}
			console.log(file.size)
			//文件路径
			var fileName = file.name;
			//获取最后一个.的位置
			var index= fileName.lastIndexOf(".");
			//获取后缀
			var ext = fileName.substr(index+1);
			//输出结果
			console.log(ext);
			if (ext !== "pdf"){
				top.layer.alert(jQuery(this).attr("title") + '附件不是PDF格市', {icon: 0, title: '警告'});
			}else if (file.size >= 10485760) {
				top.layer.alert(jQuery(this).attr("title") + '附件不能超过10M', {icon: 0, title: '警告'});
			}else{
				fileMap[jQuery(this).attr("title")] = file;
				console.log(fileMap)
				jQueryuploaderAnnexFiles.html(jQuery(fileShow.replace('#fielName#', file.name)));
			}
		}
	});

	// 注册所有的单选框
	jQuery(".singleSelect").each(function() {
		var title = jQuery(this).attr('title');
		jQuery(this).picker({
			cols: [{
				textAlign: 'center',
				values: jQuery(this).attr('data-items').split(",")
			}],
			onChange: function(e) {
				var n = e.value;
				for (let index in configStr.config) {
					if (configStr.config[index].title == title) {
						configStr.config[index].value = n[0];
					}
				}
				console.log(configStr)
			},
			onClose: function(e) {}
		})
	})

	// 注册所有的多选框
	jQuery('.multipleSelect').each(function() {
		var title = jQuery(this).attr('title');
		jQuery(this).select({
			title: "多选列表",
			multi: !0,
			min: 0,
			items: jQuery(this).attr("data-items").split(","),
			onChange: function(e) {
				var n = e.values;
				for (let index in configStr.config) {
					if (configStr.config[index].title == title) {
						configStr.config[index].value = n;
					}
				}
				console.log(configStr)
			},
			onClose: function(e) {}
		})
	})

	//注册所有的输入框
	jQuery(".input").on("change", function(e) {
		var n = jQuery(this).val();
		for (let index in configStr.config) {
			if (configStr.config[index].title === jQuery(this).attr("title")) {
				configStr.config[index].value = n;
			}
		}
		console.log(configStr)
	})

	//注册所有的大文本框
	jQuery(".weui-textarea").on("change",function (e){
		var n = jQuery(this).val();
		for (let index in configStr.config) {
			if (configStr.config[index].title == jQuery(this).attr("title")) {
				configStr.config[index].value = n;
			}
		}
		console.log(configStr)
	})
}

function imgFunction(){
	jQuery(".delImg").on("click",function(e){
		var title = jQuery(this).attr("title");
		var index = jQuery(this).parent().index();
		console.log("delImg:"+title);
		console.log("delImg:"+index);
		for(var key in imgMap) {
			if (key === title) {
				var list = [];
				for (var i = 0; i< imgMap[key].length ; i++){
					if (index !== i){
						list.push(imgMap[key][i]);
					}
				}
				imgMap[key] = list;
			}
		}
		$(this).parent().remove();
		var delUrl = jQuery(this).attr("index");
		var url = "";
		for (let index in configStr.config) {
			if (configStr.config[index].title === title) {
				for (let i = 0; i < configStr.config[index].value.split(",").length; i++) {
					if (configStr.config[index].value.split(",")[i] !==delUrl){
						if (url == ""){
							url +=configStr.config[index].value.split(",")[i];
						}else{
							url += ","+configStr.config[index].value.split(",")[i];
						}
					}
				}
				configStr.config[index].value = url;
			}
		}
	})
}
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>Changelog &mdash; CKEditor</title>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<style type="text/css">

#footer hr
{
	margin: 10px 0 15px 0;
	height: 1px;
	border: solid 1px gray;
	border-bottom: none;
}

#footer p
{
	margin: 0 10px 10px 10px;
	float: left;
}

#footer #copy
{
	float: right;
}

	</style>
</head>
<body>
	<h1>
		CKEditor Changelog
	</h1>
	<h3>
			CKEditor 3.6.4</h3>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/8887">#8887</a> : Ugly hover effect for buttons in dialog windows due to slightly wrong background position.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8783">#8783</a> : <code>getAttribute( 'contenteditable' )</code> returns 'inherited' on IE7 and IE6 for elements created from code.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8463">#8463</a> : WebKit: <em>Cut/Copy</em> buttons didn't go on "enabled" state after selecting text in the editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9043">#9043</a> : Command newpage didn't return its name when <code>afterCommandExec</code> event was fired.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9015">#9015</a> : Applied ARIA label on dialog file input element.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9016">#9016</a> : Applied the "presentation" role on the iframe element in dialog file field markup.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8949">#8949</a> : Image Properties does not show in context menu when the image is wrapped in a div with the width set.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9008">#9008</a> : Fixed list items order reversed when merging two lists.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9042">#9042</a> : [Safari] Fixed pasting left garbage html at the end of document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7745">#7745</a> : The list of frames on JAWS will not any more show the <em>"ALT+0 for help"</em> text for the editor entry.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8979">#8979</a> : Overriding matches will not any more interfere on the Font size combo.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5447">#5447</a> : Anchor names created by the Link dialog are now trimmed to protect against unwanted spaces.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8997">#8997</a> : Styles are lost on sub-lists when converting list type.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8971">#8971</a> : [IE7] Dialog size are stretched when long text field value is received.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8945">#8945</a> : Fake elements now show alternative text on High Contrast mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8985">#8985</a> : Better handling of <em>Enter</em> key events on dialogs.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8925">#8925</a> : [Firefox] Unexpected page scroll on large floating block when editor is re-focused.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8978">#8978</a> : [IE] Saved editor text selection is lost after close menu.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9022">#9022</a> : [IE7] Fixed wrong selection introduced in empty editor document when click to the bottom-right of the document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9034">#9034</a> : [IE7] Fixed JavaScript error caused by drag-to-select at the bottom of the document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9057">#9057</a> : Unexpected block established when decreasing indent of a styled list item.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9063">#9063</a> : Styles on sub-list are lost when decreasing indent of the parent list.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9047">#9047</a> : [Firefox] Anchors in the preview window will now work properly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8941">#8941</a> : [Webkit] Content region disappeared when resizing the browser.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8968">#8968</a> : [Firefox] The <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.forcePasteAsPlainText">forcePasteAsPlainText</a></code> configuration is not working when using <em>Ctrl/Cmd-V</em>.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6217">#6217</a> : Handled <em>Del/Backspace</em> key at the boundary of table to unify the cursor position.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8950">#8950</a> : Changed the cursor position after calling <code>editor::insertElement</code> on block element.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9080">#9080</a> : Fixed <em>Backspace</em> key in front of the list item to join with previous line.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3592">#3592</a> : Removed the "title" field from flash dialog advanced tab.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9084">#9084</a> : [IE] Dragging absolute positioned elements is now enabled by default.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8879">#8879</a> : Corrected styling of labels used in dialog windows.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8921">#8921</a> : [Chrome] Paste on a newly created CKEditor instance does not work on Chrome.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9036">#9036</a> : Find feature cannot locate certain words while <em>Match Whole Word</em> is enabled.</li>
		<li><a href="http://dev.ckeditor.com/ticket/9069">#9069</a> : [Chrome] Issue with popups not opening properly on Chrome 20 makes it impossible to view the file browser window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5811">#5811</a> : Code for Image dialog is missing dialog dependency.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5810">#5810</a> : Code for Div Container is missing dialog dependency.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5809">#5809</a> : Dialog a11yhelp is missing dialog dependency.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4045">#4045</a> : "Field Name" Column in <code>sample_posteddata.php</code> is to narrow.</li>
		<li>Updated the following language files as submitted to the <a href="https://www.transifex.net/projects/p/ckeditor/">CKEditor UI Translation Center</a>: Bulgarian, Chinese, Czech, Danish, Esperanto, Estonian, Greek, Italian, Norwegian, Portuguese, Romanian, Vietnamese.</li>
	</ul>
	<h3>
			CKEditor 3.6.3</h3>
	<p>
			New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/7430">#7430</a> : Justify commands now perform alignment on the element when an image is selected.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8706">#8706</a> : Better ARIA accessibility for the color picker dialog window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7366">#7366</a> : Editor paste function is now available on browser toolbar and context menu.</li>
	</ul>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/8634">#8634</a> : [IE] Command execution always returns <code>true</code> in IE browsers.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8333">#8333</a> : Dialog windows can now be closed with the <em>Esc</em> key even if there is no Cancel button available.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8644">#8644</a> : Missing variable declaration in the dialog plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8699">#8699</a> : Focus is now moved to the clicked button and back to color picker dialog window opener (button) in all browsers after the dialog window is closed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8132">#8132</a> : [IE9] Link line gets broken when it contains a <code>br</code> tag.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8629">#8629</a> : Optimize float panel layout when there is not enough space in the viewport.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7955">#7955</a> : [FF] <em>Page Up</em> and <em>Page Down</em> in the WYSIWYG mode cause the selection to become lost.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8698">#8698</a> : <em>Esc</em> key does not close the color picker dialog window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8413">#8413</a> : HTML comment nodes break content styling in tables.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7932">#7932</a>, <a href="http://dev.ckeditor.com/ticket/8643">#8643</a> : [IE] Clicking below the content region scrolls the page to the top.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5538">#5538</a>, <a href="http://dev.ckeditor.com/ticket/8729">#8729</a> : [IE] Focus jump results in incorrect context menu display.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6359">#6359</a> : [IE] Selectable area in blank WYSIWYG editor is too small.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7326">#7326</a>, <a href="http://dev.ckeditor.com/ticket/8074">#8074</a> : [FF] Scrolling does not work correctly in editor with large amount of text.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8630">#8630</a> : HTML events are now disabled when loading data.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8433">#8433</a> : [IE9] Use W3C event model.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8691">#8691</a> : [IE] Pasting invalid HTML crashes the browser.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8345">#8345</a> : [IE] Selection is not properly restored after closing the Link dialog window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6308">#6308</a> : Vertical-only resize imposes fixed width on editor chrome.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7360">#7360</a> : [WebKit] WYSIWYG area sometimes does not resize with the chrome.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5527">#5527</a> : Do not encode the <code>#</code> character sent from the file browser.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8014">#8014</a> : Autogrow now stretches to fit the content when switching editor modes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8249">#8249</a> : Inconsistent behavior with the <em>Backspace</em> key used at the start of a list item.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8617">#8617</a> : [WebKit] Selection becomes broken after an inline style is opened.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8527">#8527</a> : Insertion with cursor before an empty anchor is error-prone.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8632">#8632</a> : Cursor panic when the <em>Backspace</em> key is used in a list item.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8455">#8455</a> : Mousedown focus is too agressive.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8475">#8475</a> : Issue with deleting elements with inline styling in tables.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8324">#8324</a> : [IE8] Undo generates an error when a control type element is selected.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7946">#7946</a> : Find and Replace dialog window result does not scroll into view when the editor is off the viewport.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8157">#8157</a> : Flash/IFrame dialog window cannot be closed when displayed over Flash/IFrame with <code>dialog_startupFocusTab</code> set to <code>true</code>.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8305">#8305</a> : Text direction lost when converting a paragraph to a list.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8379">#8379</a> : The cursor moves to the start of the document when inserting a row or column is undone.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6666">#6666</a> : Removed references to <code>element.all</code>.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8732">#8732</a> : Issue when <code>CKEDITOR.range::enlarge</code> passes through comment nodes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7492">#7492</a> : The Style system can now use overrides with the same element (change classes instead of nesting).</li>
		<li><a href="http://dev.ckeditor.com/ticket/6111">#6111</a> : Splitting table cells results in incorrect column spans.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8247">#8247</a> : [IE] Error when aligning a document with page breaks inside of contents.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8540">#8540</a> : Orphan texts inside a list or table are now properly fixed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8774">#8774</a> : The Entities plugin can now be configured to only output XML entities.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8602">#8602</a> : Image source is now properly hidden when running data through the data processor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8812">#8812</a> : [IE] Issues with the native range translation with comments in the DOM.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7907">#7907</a> : Decreasing indentation of a RTL block in a list item loses text direction.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8835">#8835</a> : Right margin on IE&lt;8 removed to avoid mouse click confusion.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8400">#8400</a> : [IE] Script error when closing the Cell Properties dialog window if the table cell text is selected.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8248">#8248</a> : [IE8] Issues with <em>Backspace</em> and <em>Del</em> keys when used at the start or end of list items.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8855">#8855</a> : [Chrome] Issue with the popup window size in Chrome 18 makes it impossible to view the file browser window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8407">#8407</a> : Unneeded <code>aria-posinset</code> and <code>aria-setsize</code> for a list block are now removed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8701">#8701</a> : Highlight field in the color picker does not show the correct color when keyboard navigation is used.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8509">#8509</a> : Table height gets copied from the width value when the Advanced tab is hidden.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8432">#8432</a> : [IE8] <code>openDialog('image')</code> error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8493">#8493</a> : [IE] It is impossible to add a link to the selected text.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8528">#8528</a> : Redundant <code>imagePreviewBoxId</code> in the Image Properties dialog window is now removed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8864">#8864</a> : [IE] Fix ARIA presentation of toolbar rich combo in JAWS.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8459">#8459</a> : Fix wrong undo step on enter key.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8895">#8895</a> : Editing anchors using the Link dialog window could not work in release version.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8876">#8876</a> : &lt;link&gt; element in the floating panels have been moved to &lt;head&gt;.</li>
		<li>Updated the following language files as submitted to the <a href="https://www.transifex.net/projects/p/ckeditor/">CKEditor UI Translation Center</a>: Arabic, Bulgarian, Chinese, Croatian, Czech, Danish, Dutch, Esperanto, Estonian, Faroese, French, German, Greek, Gujarati, Hindi, Italian, Macedonian, Norwegian (Bokmål and Nynorsk), Polish, Portuguese, Turkish, Uighur, Ukrainian, Vietnamese, Welsh.</li>
	</ul>
	<h3>
			CKEditor 3.6.2</h3>
	<p>
			New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/6089">#6089</a> : The editor is now enabled on iOS 5 (iPad and iPhone).</li>
		<li><a href="http://dev.ckeditor.com/ticket/6089">#7354</a> : It is now possible to exit from blockquotes by using the <em>Enter</em> key on empty paragraphs.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7931">#7931</a> : The <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.editor.html#event:mode">mode</a></code> event now carries the previous editor mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6161">#6161</a> : New <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.autoGrow_onStartup">autoGrow_onStartup</a></code> configuration option.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8052">#8052</a> : <code>autogrow</code> is now available as an editor <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.editor.html#execCommand">command</a>.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3457">#3457</a> : It is now possible to edit the contents of <code>&lt;textarea&gt;</code> elements through the dialog window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8242">#8242</a> : The "&raquo;" character is now added to the Special Character dialog window.</li>
	</ul>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/8171">#8171</a>, <a href="http://dev.ckeditor.com/ticket/8172">#8172</a> : Updated links to WebSpellChecker.net.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8155">#8155</a> : Tooltips in the Special Character dialog window corrected.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8163">#8163</a> : The name of the <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.filebrowserWindowFeatures">filebrowserWindowFeatures</a></code> configuration setting corrected to match the documented name.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8124">#8124</a> : The Style fields in Advanced dialog window tabs are now validated according to CSS style attribute syntax.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8025">#8025</a> : The checkboxes in the Find and Replace dialog window are now part of a fieldset.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7943">#7943</a> : CSS conflict no longer appears when the page styles set the <code>float</code> property of <code>label</code> elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8016">#8016</a> : [WebKit] Flash content is not visible when previewing content.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6908">#6908</a> : Text color should always be applied to the linked text.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7619">#7619</a> : [IE] IFrame shim now consolidates the editor dialog window to avoid having it masked by embedded objects.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7900">#7900</a> : [FF] Copy/Paste operations for table cells no longer break the Table Properties dialog window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7243">#7243</a> : Inline JavaScript events may become corrupted.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7448">#7448</a> : List creation in RTL blocks is wrongly merged with the above LTR block.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6957">#6957</a> : Highlighting of searched terms does not reach read-only blocks.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7948">#7948</a> : Tooltips with information about correct length units are now displayed for the Width/Height fields of the Table Properties dialog window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6212">#6212</a> : [WebKit] Editor resize may scroll the host page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6540">#6540</a> : [Safari] Editor loses focus on resizing.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7908">#7908</a> : [IE] Unlink command is sometimes missing from the context menu of a link.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8159">#8159</a> : Editor fails to load if the browser has no default language set.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7490">#7490</a> : [IE] Block format leaks to the next unselected line when <code>enterMode</code> is set to <code>BR</code>.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8087">#8087</a> : Indenting list items may add redundant text direction attributes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6200">#6200</a> : Add styling for certain dialog window element types that miss focus outline (like checkbox).</li>
		<li><a href="http://dev.ckeditor.com/ticket/7894">#7894</a> : Fault tolerance when parsing a malformed link.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8049">#8049</a> : Bullets/Numbers are invisible for list items with LTR text direction.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8222">#8222</a> : [IE] Incorrect selection locking after opening a dialog window in some cases.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7002">#7002</a> : [IE] Undo operation when a table is selected results in an error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8232">#8232</a> : [IE] Unable to apply inline style that starts at the end of a link text.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7153">#7153</a> : Fail to load the source version of the editor after the window is loaded.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8246">#8246</a> : Bad editing performance on certain document contents.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7912">#7912</a> : Cursor trapped in an invisible element after pressing the <em>Enter</em> key.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7645">#7645</a> : Full list or table deletion with the <em>Backspace/Delete</em> key.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8050">#8050</a> : AutoGrow feature better fits the content styles.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8349">#8349</a> : [IE9] Larger toolbar top offset on HTML5 pages.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8352">#8352</a> : [IE9] Toolbar wrapping is incorrect.</li>
		<li><a href="http://dev.ckeditor.com/ticket/8080">#8080</a> : JavaScript error when inserting a new table row.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/8263">#8263</a> : Dutch;</li>
			<li><a href="http://dev.ckeditor.com/ticket/8238">#8238</a> : Estonian;</li>
			<li><a href="http://dev.ckeditor.com/ticket/8193">#8193</a> : Finnish;</li>
			<li>German;</li>
			<li>Hebrew;</li>
			<li><a href="http://dev.ckeditor.com/ticket/8179">#8179</a> : Hungarian;</li>
			<li><a href="http://dev.ckeditor.com/ticket/8128">#8128</a> : Italian;</li>
			<li><a href="http://dev.ckeditor.com/ticket/8371">#8371</a> : Lithuanian;</li>
			<li><a href="http://dev.ckeditor.com/ticket/8126">#8126</a>, <a href="http://dev.ckeditor.com/ticket/8256">#8256</a> : Norwegian (Bokmal and Nynorsk);</li>
			<li><a href="http://dev.ckeditor.com/ticket/8356">#8356</a> : Persian;</li>
			<li>Polish;</li>
			<li>Portuguese (Brazil);</li>
			<li><a href="http://dev.ckeditor.com/ticket/8151">#8151</a>, <a href="http://dev.ckeditor.com/ticket/8298">#8298</a> : Russian;</li>
			<li>Spanish;</li>
		</ul></li>
	</ul>
	<h3>
			CKEditor 3.6.1</h3>
	<p>
			New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/4556">#4556</a> : Initial support for HTML5 elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6492">#6492</a> : The Find/Replace dialog window will now be populated with text selected in the editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7323">#7323</a> : New <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.dialog.definition.uiElement.html#align">align</a></code> property in dialog window UI elements for field alignment.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6462">#6462</a> : A wider range of CSS length units (like pt and percentage) are now supported in related dialog window fields.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7911">#7911</a> : New Remove Anchor option is now available in the context menu.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7387">#7387</a> : Allow <code>styleDefinition</code> to be applied to a set of elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4345">#4345</a> : A new <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.editor.html#event:langLoaded">langLoaded</a></code> event added to <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.editor.html">CKEDITOR.editor</a></code> in order to make it possible to perform "by code" language updates.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7959">#7959</a> : The cursor will now blink in the first cell after a table is inserted.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7885">#7885</a> : New <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.editor.html#removeMenuItem">editor::removeMenuItem</a></code> API for removing plugin context menu items introduced.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7991">#7991</a> : Introduce the <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.dialog.definition.labeledElement.html#controlStyle">controlStyle</a></code> and <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.dialog.definition.labeledElement.html#inputStyle">inputStyle</a></code> definitions to allow fine-grained controlling of dialog window element styles.</li>
	</ul>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/7914">#7914</a> : <strong>ATTENTION!</strong> The signature for the <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.editor.html#setReadOnly">setReadOnly()</a></code> function has been changed, reversing the meaning of the parameter to be passed to it. Please make sure to update your code when upgrading.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7657">#7657</a> : Wrong margin mirroring when creating a list from RTL paragraphs.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7620">#7620</a> : A glitch in list pasting from Microsoft Word caused by broken child references when filtering.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7811">#7811</a> : [IE] Deleting table row throws a JavaScript error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6962">#6962</a> : Changed the <code>CKEDITOR.CTRL</code>, <code>CKEDITOR.SHIFT</code> and <code>CKEDITOR.ALT</code> constant values to avoid collision with any possible Unicode character.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6263">#6263</a> : Some table cell context menu options may be incorrectly disabled.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6247">#6247</a> : Focus is not restored properly after a drop-down menu is closed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7334">#7334</a> : [IE7] Indentation style does not apply to RTL lists.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6845">#6845</a> : Spaces inside the URL field in the Link dialog window will now be removed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7840">#7840</a> : [IE] Opening the Table Properties dialog window via the context menu causes a JavaScript error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7733">#7733</a> : Flash movies inserted with the Flash Properties dialog window are not displaying properly when injected into the page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7837">#7837</a> : [IE&lt;8] Inserting a page break results in an error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7804">#7804</a> : The HTML5 <a href="http://www.w3.org/TR/html-markup/wbr.html"><code>wbr</code></a> tag is now recognized by the editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7867">#7867</a> : The file browser for the background image in the Document Properties plugin dialog window does not work.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7130">#7130</a> : The column resizer gripping area is invading adjacent table cells.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7844">#7844</a> : [FF] Calling <code>setData()</code> on a hidden editor caused editor not to display.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7860">#7860</a> : The BBCode plugin was stripping BBCode tags that were not implemented in the plugin, but from now on they will be handled as simple text.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7321">#7321</a> : [IE6] Contents inside the RTL fields in dialog windows are overflowing.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7323">#7323</a> : [IE Quirks] Some fields are not centered in the dialog window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5955">#5955</a> : Editor accessibility issue with JAWS when a drop-down menu is placed as the first item in the toolbar.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6671">#6671</a> : [FF] Selection of an item from the Styles drop-down list is not refreshed after the style is removed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7879">#7879</a> : The Style and Height/Width fields of the Table Properties dialog window are not synchronized.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7581">#7581</a> : [IE] The <em>Enter</em> key pressed at the end of a list item containing the <code>start</code> attribute crashes the browser.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7266">#7266</a> : Dialog window fields that did not pass validation are now ARIA-compatible with <code>aria-invalid</code>.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7742">#7742</a> : [WebKit] Indentation, alignment, and language direction are not applied on an empty document without the editor being in focus.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7801">#7801</a> : [Opera] Pasted paragraphs now split partially selected blocks.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6663">#6663</a> : Table caption that contains rich text is corrupted after an edit done with the Table Properties dialog window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7893">#7893</a> : [WebKit, Opera, IE&lt;8] It is impossible to link to anchors in the document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7637">#7637</a> : Cursor position might in some cases cause problems after inserting a page break.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5314">#5314</a> : The <code>aria-selected</code> attribute is not removed when toolbar drop-down menu items are deselected.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7749">#7749</a> : Small check introduced to avoid issues with custom data processors and the <code>insertHtml</code> function.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7269">#7269</a> : [WebKit] Paste from Word is including the full <code>file://</code> URL path for anchor links.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7584">#7584</a> : Start number of the List dialog window now works with numbered list items.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6975">#6975</a> : [IE6, IE7] A definition list crashes Internet Explorer on HTML output.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7841">#7841</a> : Deleting a column with a cell deleted in one of the rows does not work.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7944">#7944</a> : The <em>Enter</em> key should not split or create new paragraphs inside caption elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7639">#7639</a> : [IE9] Browser might crash when an object is selected in the document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7847">#7847</a> : [IE8] Inserting an image with non-secure source in a HTTPS page breaks the dialog window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7953">#7953</a> : [IE] Text selection lost after the browser context menu is opened.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5239">#5239</a> : Inconsistent focus behavior after closing a toolbar drop-down menu.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6470">#6470</a> : The Start attribute of a Numbered List is rendered incorrectly if the field is left empty.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7324">#7324</a> : [IE6 Quirks] Context menus are not displayed correctly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7566">#7566</a> : BiDi: Increasing indentation of a list item changes the language direction.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7839">#7839</a> : [IE] Pasting multi-level numbered lists from Microsoft Word does not work properly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/188">#188</a> : [IE] Object selection was making the toolbar inactive in some situations.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/7834">#7834</a> : Dutch;</li>
			<li><a href="http://dev.ckeditor.com/ticket/7869">#7869</a>, <a href="http://dev.ckeditor.com/ticket/7869">#7999</a> : Welsh;</li>
			<li>Polish;</li>
			<li>Hebrew;</li>
			<li>German</li>
		</ul></li>
	</ul>
	<h3>
			CKEditor 3.6</h3>
	<p>
			New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/7044">#7044</a> : New BBCode sample plugin that makes the editor output (one dialect of) BBCode format.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5647">#5647</a> : Accessibility enhancements to the structure of the toolbar.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5647">#5647</a> : The Kama skin now presents separators for the toolbar items, making it easier to group buttons and have a cleaner layout.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5647">#5647</a> : Usability enhancements to keyboard navigation on the toolbar. The <em>Tab</em> key is now used to jump between toolbar groups, while the <em>Arrow</em> keys can be used to cycle within the group. The new <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.toolbarGroupCycling">toolbarGroupCycling</a></code> setting can be used to change the <em>Arrow</em> keys behavior.</li>
		<li><a href="http://dev.ckeditor.com/ticket/1376">#1376</a> : It is now possible to put the editor in the "read-only" state, so that the users would not be able to introduce changes to the contents. Check out the new <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.editor.html#setReadOnly">CKEDITOR.editor::setReadOnly</a></code> method, the <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.editor.html#readOnly">CKEDITOR.editor::readOnly</a></code> property, the <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.editor.html#event:readOnly">CKEDITOR.editor::readOnly</a></code> event, and the <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.readOnly">readOnly</a></code> setting.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3582">#3582</a> : New presentation of anchor elements in the WYSIWYG mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6737">#6737</a> : The Format drop-down list will now display the preview of its contents exactly as defined in their style configurations.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6654">#6654</a> : A new <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.autoParagraph">autoParagraph</a></code> configuration setting is added to disable the auto paragraphing feature.</li>
		<li><a href="http://dev.ckeditor.com/ticket/901">#901</a> : New Stylesheet Parser (<code>stylesheetparser</code>) plugin that fills the Styles drop-down list based on the CSS classes available for the content. Check the new sample to learn how to use it.</li>
		<li><a href="http://dev.ckeditor.com/ticket/2988">#2988</a> : New Document Properties (<code>docprops</code>) plugin that sets the metadata of the page in the Full Page mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7240">#7240</a> : New Developer Tools (<code>devtools</code>) plugin that shows information about dialog window UI elements to allow for easier customization.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6841">#6841</a> : Pressing the <em>Enter</em> key at the end of a pre-formatted block will now exit from it.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6850">#6850</a> : The About CKEditor dialog window now contains a link to CKEditor User's Guide.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5745">#5745</a> : Extra configuration options for the <code>iframeDialog</code> can now be passed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6589">#6589</a> : The <code>onDialogEvent</code> function will now be used automatically in the <code>iframeDialog</code> contents if no callback is used on creation.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7757">#7757</a> : Georgian localization added.</li>
	</ul>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/6774">#6774</a> : Internal styles are not included in the <code>contents.css</code> sample.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6521">#6521</a> : Added sample for the TableResize plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6664">#6664</a> : Page break is sometimes merged into block-level elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7594">#7594</a> : Toolbar keyboard navigation is not possible after recreating the editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6657">#6657</a> : Allow to style the entire dialog window field when the input element is disabled.</li>
		<li>Updated the following language files:<ul>
			<li>Hebrew;</li>
			<li>Polish;</li>
		</ul></li>
	</ul>
	<h3>
			CKEditor 3.5.4</h3>
	<p>
			Fixed issues:</p>
	<ul>
		<li>Added protection against XSS attacks in PHP samples when displaying element names.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7347">#7347</a> : The <em>Enter</em> key will no longer be caught by the dialog window covering the editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6718">#6718</a> : Paste from Word command overrides the Force Paste as Plain Text configuration.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6629">#6629</a> : Padding body is no longer needed when the last block is pre-formatted.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4844">#4844</a> : [IE] Dialog windows fail to load if there are too many editor instances on the page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5788">#5788</a> : HTML parser trims empty spaces following <code>&lt;br&gt;</code> elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7513">#7513</a> : Invalid markup could cause the editor to hang.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6109">#6109</a> : Paste and Paste as Plain Text dialog windows now use the standard <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.dialog.html#commitContent">commitContent</a></code> and <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.dialog.html#setupContent">setupContent</a></code> methods.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7588">#7588</a> : The editor code now has a protection system to avoid issues when including <code>ckeditor.js</code> more than once in the page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7322">#7322</a> : Text font plugin now recognizes font family names that contain quotes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7540">#7540</a> : Paste from Word introduces wrong spaces.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7697">#7697</a> : Successive calls of the <code>replace()</code> method did not work after SCAYT context menu initialization.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/7647">#7647</a> : Slovak;</li>
		</ul></li>
	</ul>
	<h3>
			CKEditor 3.5.3</h3>
	<p>
			New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/4890">#4890</a> : Added the possibility to edit the <code>rel</code> attribute for links.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7004">#7004</a> : Allow loading plugin translations even if they are not present in the plugin definition.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7315">#7315</a> : Firing the <code>resize</code> event on dialog window instances is now possible.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7259">#7259</a> : Dialog window definition allows to specify initial <code>width</code> and <code>height</code> values.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7131">#7131</a> : List item numbering is now supported on pasting from Microsoft Word.</li>
	</ul>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/1272">#1272</a> : [WebKit] It is now possible to apply styles to collapsed selections in Safari and Chrome.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7054">#7054</a> : The tooltips for special characters are now lowercased, making them more readable.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7102">#7102</a> : "Replace DIV" sample did not work when double-clicking inside the formatted text.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7088">#7088</a> : Loading of plugins failed on new instances of the editor after the Insert Special Character dialog window was used.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6215">#6215</a> : Removal of inline styles now also removes overrides.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6144">#6144</a> : Rich text drop-down lists have wrong height when toolbar is wrapped.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6387">#6387</a> : AutoGrow may cause an error when editor instance is destroyed too quickly after a height change.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6901">#6901</a> : Mixed direction content was not properly respected in a shared toolbar setting.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4809">#4809</a> : Table-related tags are output in wrong order.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7092">#7092</a> : Corrupted toolbar button state for inline style after switching to Source.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6921">#6921</a> : Pasted text marked by SCAYT in one language is not re-checked if another spellchecking language is selected in the editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6614">#6614</a> : Enhancement of the resize handle in RTL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5924">#5924</a> : Flash plugin now recognizes Flash content without an <code>embed</code> tag.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4475">#4475</a> : Protected source in attributes and inline CSS text is not handled correctly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6984">#6984</a> : [FF] Trailing line breaks are lost in <code>ENTER_BR</code>.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6987">#6987</a> : [IE] Text selection lost when calling <code>editor::insertHtml</code> from a dialog window in some situations.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6865">#6865</a> : BiDi mirroring does not work when a text direction change is done through a dialog window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6966">#6966</a> : [IE] Unintended paragraph is created in an empty document in <code>enterMode</code> set for <code>BR</code> and <code>DIV</code>.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7084">#7084</a> : SCAYT dialog window is now working properly with more than one editor instance in a page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6662">#6662</a> : [FF] List structure pasting error caused by a regression from FF3.5.x is now fixed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7300">#7300</a> : Link dialog window now loads numeric values correctly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7330">#7330</a> : New list items no longer inherit the <code>value</code> attribute from their sibling.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7293">#7293</a> : The "Automatic" color button is now presented correctly without focus inside the editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7018">#7018</a> : [IE] Toolbar drop-down lists did not have a border around them.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7073">#7073</a> : Image dialog window no longer allows zero height and width value to be entered.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7316">#7316</a> : [FF] Clicking on "Paste" button incorrectly breaks the line at the cursor position.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6751">#6751</a> : Inline whitespaces are incorrectly stripped when pasting from Word.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6236">#6236</a> : [IE] Fixing malformed nested list structure which was introduced by the <em>Backspace</em> key.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6649">#6649</a> : [IE] Selection of the full table sometimes does not work.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6946">#6946</a> : HTML parser is now able to fix orphan list items.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6861">#6861</a> : Indenting a list item should retain the text direction.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6938">#6938</a> : Outdenting a list item should retain the text direction.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6849">#6849</a> : Correct <em>Enter</em> key behavior on list item.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7113">#7113</a> : [WebKit] Undesired document scroll on click after scrolling.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6491">#6491</a> : Undesired Image dialog window dimension lock reset on URL change.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7284">#7284</a> : [FF Quirks] Maximize now works correctly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6609">#6609</a> : [IE9] Browser in high contrast mode is not properly detected.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7222">#7222</a> : [WebKit] Impossible to apply a single style to a collapsed selection without giving the editor focus.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7180">#7180</a> : [IE9] When using Kama skin and RTL layout dialog window buttons were not being displayed correctly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7182">#7182</a> : [IE9] When using Office2003/v2 skin and RTL layout dialog window shadows were corrupted.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6913">#6913</a> : Invalid escape sequence (<code>\b</code>) was used in the PHP integration.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5757">#5757</a> : [IE6] Text was not wrapping in the accessibility instructions dialog window.</li>
		<li><a href="http://dev.ckeditor.com/changeset/6604">[6604]</a> : <code>Xml.js</code> and <code>Ajax.js</code> are now available as plugins ('xml' and 'ajax').</li>
		<li><a href="http://dev.ckeditor.com/ticket/7304">#7304</a> : Microsoft Word cleanup function is not always invoked when clicking on the "Paste From Word" button.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6658">#6658</a> : [IE] Pasting text from Microsoft Word with one or more tabs between list items was failing.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7433">#7433</a> : [IE9] <code>ENTER_BR</code> at the end of a block breaks due to an IE9 regression.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7432">#7432</a> : [WebKit] Unable to create a new list in an empty document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4880">#4880</a> : CKEditor changes tag style inside HTML comment with <code>cke_protected</code>.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7023">#7023</a> : [IE] JavaScript error when a Selection Field is inserted into a page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7034">#7034</a> : Inserting special characters into styled text.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7132">#7132</a> : Paste toolbar buttons are becoming disabled.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7138">#7138</a> : The <code>api.html</code> sample in Opera does not work as expected.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7160">#7160</a> : Cannot paste the form element on top of the page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7171">#7171</a> : Double-clicking an image in non-editable content opens the editing dialog window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7455">#7455</a> : Extra line break is added automatically to the preformatted element.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7467">#7467</a> : [Firefox] Extra <code>br</code> element is added in a nested list.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/7124">#7124</a> : Czech;</li>
			<li><a href="http://dev.ckeditor.com/ticket/7126">#7126</a> : French;</li>
			<li><a href="http://dev.ckeditor.com/ticket/7140">#7140</a> : Catalan;</li>
			<li><a href="http://dev.ckeditor.com/ticket/7215">#7215</a> : Faroese;</li>
			<li><a href="http://dev.ckeditor.com/ticket/7177">#7177</a> : Finnish;</li>
			<li><a href="http://dev.ckeditor.com/ticket/7163">#7163</a> : Norwegian (no and nb);</li>
			<li><a href="http://dev.ckeditor.com/ticket/7219">#7219</a> : Swedish;</li>
			<li><a href="http://dev.ckeditor.com/ticket/7183">#7183</a> : Afrikaans;</li>
			<li>Hebrew;</li>
			<li>Spanish;</li>
			<li>Polish;</li>
			<li>German;</li>
		</ul></li>
	</ul>
	<h3>
			CKEditor 3.5.2</h3>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/7168">#7168</a> : [IE9] Destroying an editor instance throws an error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7169">#7169</a> : [IE9] Menu item has incorrect height.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7178">#7178</a> : [IE9] Read-only attributes do not work in IE9.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7181">#7181</a> : [IE9] Toolbar items are not aligned in v2 and Office2003 skins.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7174">#7174</a> : [IE9] Elements path does not load correctly when the editor is switched back from Source to WYSIWYG.</li>
	</ul>
	<h3>
			CKEditor 3.5.1</h3>
	<p>
			New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/6107">#6107</a> : It is now possible to remove block styles using Styles and Paragraph Format drop-down lists.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5590">#5590</a> : Remove Format command works in collapsed selections.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5755">#5755</a> : The <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.dialog_buttonsOrder">dialog_buttonsOrder</a></code> option now works in Internet Explorer.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6869">#6869</a> : The <code>data-cke-nostyle</code> attribute (which was introduced for escaping the element from been influenced by the style system since 3.5) is deprecated in favor of the new <code>data-nostyle</code> attribute.</li>
		<li>Revised sample pages with code examples and clarifications.</li>
	</ul>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/5855">#5855</a> : Updating a link multiple times generates wrong <code>href</code> attribute.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6166">#6166</a> : Error on Maximize command, when the toolbar button is not shown.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6607">#6607</a> : Table cell "merge down" and "merge right" commands work only once.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6228">#6228</a> : Merge down does not work, throwing a JavasSript error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6625">#6625</a> : BIDI: Mixed LTR/RTL direction causes incorrect behavior.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6881">#6881</a> : IFrame capitalization is now consistent throughout labels.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6686">#6686</a> : BIDI: [FF] When we apply explicit language direction to a numbered/bulleted list, the corresponding language direction toolbar icon is not highlighted.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6566">#6566</a> : It is now possible to exit a blockquote using <code>ENTER_BR</code>.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6868">#6868</a> : Partial (invalid) list structure crashes the editor on load.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6804">#6804</a> : Buggy behavior when editing the <code>legend</code> element inside a <code>fieldset</code>.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6724">#6724</a> : [IE7] Nested list display bug on empty list item.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6715">#6715</a> : List items do not create paragraphs after the list placed in a table cell is removed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6695">#6695</a> : [Webkit] Display bug after the editor is restored from the full screen mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6661">#6661</a> : [IE] Pre-formatted style does not preserve applied text direction.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6655">#6655</a> : Using the editor resize grip causes small visual offsets.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6604">#6604</a> : The <code>div</code> element should be used as a formatting block in <code>ENTER_BR</code>.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6249">#6249</a> : BIDI: List item bullets are off viewport with RTL text direction.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6610">#6610</a> : BIDI: <code>ENTER_BR</code> change direction in one line out of multiple.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6872">#6872</a> : [IE] Link target field is not populated properly when no target is set.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6880">#6880</a> : Samples: Added a user-friendly message for users on servers without PHP support.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6628">#6628</a> : Setting <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.enterMode">config.enterMode</a></code> from PHP fails.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6278">#6278</a> : Comments were moved above the <code>br</code> tags.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6687">#6687</a> : Empty tag should be removed in inline-style format.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6645">#6645</a> : Allow to configure whether &quot; (double quotes) characters should be encoded in the contents.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6336">#6336</a> : IE: (double)clicking an <code>input type="submit"</code> button submitted the form.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6646">#6646</a> : Context menu was not working for text inputs present in the initial content.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6641">#6641</a> : Copying and pasting links inside the editor was not working.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4208">#4208</a> : The <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.disableObjectResizing">disableObjectResizing</a></code> setting now works in IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6242">#6242</a> : [IE] Editing existing links with <code>href</code> of a relative path mangles containing text.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5930">#5930</a> : [IE] Style definitions are no longer lowercased.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5361">#5361</a> : Preview window's title should reflect the title tag in full page mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5522">#5522</a> : [IE] In versions &lt; 8 or compatibility mode, <code>type="text"</code> was missing in text fields.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6126">#6126</a> : [IE] Avoid problems if there are two buttons named "submit".</li>
		<li><a href="http://dev.ckeditor.com/ticket/6791">#6791</a> : [IE7] Editor did not show up when the name of a replaced textarea matched the name of a <code>meta</code> tag in the page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5684">#5684</a> : [FF] When <code><a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.forcePasteAsPlainText">forcePasteAsPlainText</a></code> is used, the cursor disappears after paste.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6390">#6390</a> : Prevent toolbar dialog window buttons from being clicked twice.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6684">#6684</a> : [Webkit] Toolbar buttons are not wrapping correctly when the editor is displayed inside a table.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6703">#6703</a> : [IE] editor <code>focus</code> event not fired in an instance, when a dialog window closes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6873">#6873</a> : Difficult to drag the resize grip of the spell checker dialog window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6896">#6896</a> : [Webkit] Unable to paste into source area when the editor is maximized.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6020">#6020</a> : The state of the Cut, Copy, and Paste toolbar now matches the state of the context menu buttons.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5256">#5256</a> : JavaScript error thrown when percent (%) sign is used in image URL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6577">#6577</a> : [FF] Selection error when an element containing the editor instance is hidden.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5500">#5500</a> : [IE] <code>value</code> attribute of text input dialog window field was missing.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6665">#6665</a> : [IE] <code>name</code> field of Link dialog window was missing.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6639">#6639</a> : Line-breaks inside pasted list item from Microsoft Word break the list structure.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6909">#6909</a> : [IE] GIF icons of toolbar button from custom plugins are not diplayed in zoom level 100%.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6860">#6860</a> : [FF] Double-clicking the placeholder element in order to open a Placeholder dialog window throws a JavaScript error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6630">#6630</a> : Empty <code>pre</code> elements are output differently in various browsers.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6568">#6568</a> : Insert table row/column does not work with spanning.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6735">#6735</a> : Inaccurate read-only selection detection.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6728">#6728</a> : BIDI: Change direction does not work with list nested inside a blockquote.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6432">#6432</a> : Inserting a table in place of a fully selected list results in a JavaScript error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6438">#6438</a> : [IE] Performance enhancement when typing inside an element with many child nodes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6970">#6970</a> : [IE] Dialog window shadows were presented inaccurately.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6672">#6672</a> : [IE] Unnecessary <code>br</code> element is no longer inserted after a form.</li>
		<li><a href="http://dev.ckeditor.com/ticket/7087">#7087</a> : [FF] Sometimes it was not possible to move cursor out of link at the end of block.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/6981">#6981</a> : English (GB);</li>
			<li><a href="http://dev.ckeditor.com/ticket/6991">#6991</a> : Finnish;</li>
			<li><a href="http://dev.ckeditor.com/ticket/6357">#6357</a> : French;</li>
			<li><a href="http://dev.ckeditor.com/ticket/7055">#7055</a> : Polish;</li>
			<li><a href="http://dev.ckeditor.com/ticket/7068">#7068</a> : German;</li>
		</ul></li>
	</ul>
	<h3>
			CKEditor 3.5</h3>
	<p>
			New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/4090">#4090</a> : Full Adobe AIR support.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5084">#5084</a> : Dialog windows are now resizable with a grip located in the footer.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5755">#5755</a> : Introduced the <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.dialog_buttonsOrder">dialog_buttonsOrder</a> setting, making it possible to control the buttons order.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4648">#4648</a> : Added the new iFrame plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6010">#6010</a> : The Automatic option of the font/background color panel now represents the real color.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5654">#5654</a> : New "placeholder" plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6334">#6334</a> : CKEditor now uses <a href="http://www.w3.org/TR/2010/WD-html5-20101019/elements.html#embedding-custom-non-visible-data-with-the-data-attributes">HTML5's data-* attributes</a> for its internal attributes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6103">#6103</a> : It's now possible to control the styling of inline read-only elements with the <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.disableReadonlyStyling">disableReadonlyStyling</a> setting. It's also possible to avoid inline-styling any element by setting its "data-cke-nostyle" attribute to "1".</li>
		<li><a href="http://dev.ckeditor.com/ticket/5404">#5404</a> : <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.fillEmptyBlocks">fillEmptyBlocks</a> configuration option of v2 is now available.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5367">#5367</a> : New <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.editor.html#insertText">CKEDITOR.editor#insertText</a> method (check api.html sample page for usages) is now provided to insert plain text into editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5367">#5915</a> : New <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.removeDialogTabs">removeDialogTabs</a> configuration option to hide certain dialog tabs.</li>
	</ul>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/4821">#4821</a> : Icons in the toolbar were distorted with IE and zoom != 100%.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5587">#5587</a> : Visual improvements in dialogs, reinforce field label on separate line.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4652">#4652</a> : Now it's able to disable editor context menu by simply removing the "contextmenu" plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5599">#5599</a> : Labels of "specialchar" dialog are now translated.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6419">#6419</a> : [IE] List creation by merging problem.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6502">#6502</a> : Removed IE6 image preloading, which was used to defect the duplicate request of background images.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6822">#6822</a> : Added labels to fake objects.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6898">#6898</a> : [IE6] Toolbar icons becomes invisible in RTL.</li>
		<li>Updated the following language files:<ul>
			<li>Hebrew</li>
		</ul></li>
	</ul>
	<h3>
			CKEditor 3.4.3</h3>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/6554">#6554</a> : [Webkit] cannot type after inserting Page Break.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6569">#6569</a> : Indentation now complies with text direction of the only item.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6579">#6579</a> : The jQuery adapter was not working properly and was turned on in incompatible environments.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6644">#6644</a> : Restrict <code>onmousedown</code> handler to the toolbar area.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6656">#6656</a> : Panelbutton's buttons became active when clicking on Source.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6248">#6248</a> : Whitespaces (<code>nbsp</code> elements) were incorrectly added into empty table cells and list items.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6575">#6575</a> : Tabs disappearing in Link dialog window after a specific sequence of actions.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6510">#6510</a> : Margin mirroring does not respect style configuration.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6471">#6471</a> : BIDI: Pressing Decrease Indent in an RTL bulleted list causes incorrect behaviour.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6479">#6479</a> : BIDI: Language direction is not being preserved when pressing Enter after a Paragraph Format was applied.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6670">#6670</a> : BIDI: Indent &amp; List icons are not reversed when we apply RTL direction to a paragraph with any of Paragraph Formatting options.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6640">#6640</a> : Floating panels are now being closed when switching modes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4790">#4790</a> : Remove list with multiple items in <code>enterBr</code> doesnot preserve line breaks.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6297">#6297</a> : Floated inline elements are not taking part in behavior of blocks anymore.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6171">#6171</a> : [Firefox] Opening rich content drop-down list scrolls host page to the top when editor has a vertical scrollbar.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6330">#6330</a> : List markers from MS Word with Roman numbering are not preserved.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6720">#6720</a> : Attribute protection might detect wrong elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6580">#6580</a> : [IE9] Flash dialog window does not get filled up.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6447">#6447</a> : Decreasing indentation of a list with <code>indentClasses</code> config does not work.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5894">#5894</a> : Adding custom buttons at the bottom of a dialog window does not cause it to expand to include its contents.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6513">#6513</a> : Wrong ARIA attributes created on list options of Styles drop-down list.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6150">#6150</a> : [Safari] Color dialog window was broken.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6747">#6747</a> : Full screen layout issue caused by page element focus outside editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6779">#6779</a> : Clicking the <code>body</code> element on elements path turns the selection on and off immediately.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6781">#6781</a> : [IE7] Dialog windows are broken with RTL, Office 2003 and v2 skins.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6798">#6798</a> : [IE7] Dialog window buttons disappearing in RTL after dragging.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6806">#6806</a> : [IE7] Dialog window buttons invisible on focus.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6588">#6588</a> : Copy and paste adds <code>&lt;span&gt;</code> if SCAYT is enabled.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6673">#6673</a> : IE Target combo for Image Link shown as blank even when we select <code>&lt;not set&gt;</code> as an option.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/6756">#6756</a> : Hungarian;</li>
			<li><a href="http://dev.ckeditor.com/ticket/6794">#6794</a> : Japanese;</li>
		</ul></li>
	</ul>
	<h3>
			CKEditor 3.4.2</h3>
	<p>
			New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/5024">#5024</a> : Added a sample that shows how to output HTML that is valid for Flash.</li>
	</ul>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/5237">#5237</a> : English text in dialogs' title was flipped when using RTL language (office2003 and v2 skins).</li>
		<li><a href="http://dev.ckeditor.com/ticket/6289">#6289</a> : Deleting nested table removed the parent cell.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6341">#6341</a> : The editor contents now have the text cursor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6153">#6153</a> : Chrome: tab focus is wrong.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6261">#6261</a> : Focus and infinite loop between multiple editors.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6170">#6170</a> : Dedicated class names are removed from floating panels when opening another panel.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6339">#6339</a> : Autogrow plugin now doesn't work on maximized editors.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6237">#6237</a> : BIDI: Applying same language direction to all paragraphs not working.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6353">#6353</a> : [IE] Resize was broken with office2003 and v2 skins.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6375">#6375</a> : Avoiding errors when hiding the editor after the blur event.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6133">#6133</a> : Styled paragraphs result on buggy list creation.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5074">#5074</a> : Link target is not removed when changing to popup.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6408">#6408</a> : [IE] Autogrow now works correctly on Quirks.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6420">#6420</a> : [IE] The table properties dialog now correctly retrieves the caption text.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6141">#6141</a> : It was impossible to outdent a list when indentOffset was set to 0.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6377">#6377</a> : FF width and height are not shown for smiley in Image properties dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5399">#5399</a> : Lists pasted from Word do not maintain their nesting.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6225">#6225</a> : [FF] Cannot transform several lines to list with enterMode BR.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6467">#6467</a> : [FF] It is now possible to disable the plugin command on "mode" event.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6461">#6461</a> : Attributes are now being kept when changing block formatting.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6226">#6226</a> : BIDI: Language direction applied to a Paragraph is removed when we apply one of Paragraph formatting options.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5395">#5395</a> : [Opera] Native context menu incorrectly opened after Opera 10.2.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6444">#6444</a> : [Opera] Close panels and dialogs don't return focus to wysiwyg frame.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6332">#6332</a> : IE: V2 skin bottom dialog's border broken.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5646">#5646</a> : Parser incorrectly removes inline element when there's only one comment node enclosed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6189">#6189</a> : Minor code size reduction.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5045">#5045</a> : uiColor behaved wrong if multiple editors were used with period in their names.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5766">#5766</a> : Config entry "ignoreEmptyParagraph" should only remove one single empty paragraph in document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5931">#5931</a> : Unable to apply inline style because of nested elements with same style name.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6083">#6083</a> : Dialog close sometimes cause collapsed editor selection before the insertion.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6253">#6253</a> : BIDI: creating a Numbered/Bulleted list causing improper behavior on bidi.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4023">#4023</a> : [Opera] Maximize plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6403">#6403</a> : [Opera] Font name options are not correctly marked in dropdown list.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4534">#4534</a> : [Opera] Arrow key to navigate through combo list has side effects of window scrolling.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6534">#6534</a> : [Opera] Menu key brings up both CKEditor and browser context menu.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6534">#6534</a> : [Opera] Menu key brings up both CKEditor and browser context menu.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6416">#6416</a> : [IE9] Unable to make text selection with mouse in source area.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6417">#6417</a> : [IE9] Context menu opens at the upper-left corner always.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6501">#6501</a> : [IE9] Context menu item layout is broken.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6099">#6099</a> : BIDI: when we apply explicit language direction to Numbered/Bulleted List the corresponding BIDI Tool bar icon is not highlighted in the Toolbar.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6100">#6100</a> : BIDI: when we change Table language direction indentation of text in Table cells is not applied correctly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6376">#6376</a> : BIDI: buttons should not toggle the base language direction.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6235">#6235</a> : BIDI: Applying direction to multi-paragraph selection within a div.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6187">#6187</a> : [IE6] Multi-instance loading produces 404s on background images.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5446">#5446</a> : Setting config.filebrowserImageBrowseUrl results in displaying also Browser Server on links.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5626">#5626</a> : CKeditor 3.2.1 : html content attached makes ckeditor crash the browser FF/IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6508">#6508</a> : BiDi: Margin mirroring logic doesn't honor CSS direction.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6043">#6043</a> : BIDI: When we apply RTL direction to a right aligned Paragraph, Paragraph is not moved to the left &amp; Alignment of Paragraph is not changed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6485">#6485</a> : BIDI: When direction is applied on partial selected list, the style is been incorrectly applied to the entire list.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6087">#6087</a> : Cursor of input fields in dialog isn't visible in RTL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5595">#5595</a> : Extra leading spaces added in preformatted block.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6094">#6094</a> : Match full word option doesn't stop on block boundaries.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5730">#5730</a> : [Safari] Continual pastes (holding paste key) breaks document contents.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5850">#5850</a> : [IE] Inline style misbehaviors at the beginning of numbered/bulleted list.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/6427">#6427</a> : Ukrainian;</li>
			<li><a href="http://dev.ckeditor.com/ticket/6464">#6464</a> : Finnish;</li>
			<li>Hebrew;</li>
		</ul></li>
	</ul>
	<h3>
			CKEditor 3.4.1</h3>
	<p>
			New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/5308">#5308</a> : Introduced the <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.filebrowserWindowFeatures">filebrowserWindowFeatures</a> setting, making it possible to have custom window features in the file browser window.</li>
	</ul>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/6027">#6027</a> : Modifying Table Properties by selecting more than one cell caused issues.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6146">#6146</a> : IE: Floating panels were being opened in the wrong place in RTL pages with horizontal scrollbars.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6055">#6055</a> : The timestamp is now added only once to each loaded file.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6097">#6097</a> : The bookmarks now use the right name.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5717">#5717</a> : Removed the scayt_contextMenuOntop setting and the SCAYT context menu options are always on top.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5956">#5956</a> : [FF] It was impossible to create an editor inside an hidden container.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5753">#5753</a> : It was impossible to have a default value for the name field in the select dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6041">#6041</a> : BIDI: Direction of Increase Indent &amp; Decrease Indent icons are not reversed after changing Lang direction to RTL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6138">#6138</a> : List indentation is not working.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5649">#5649</a> : Image dialog too wide when many styles are set.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5715">#5715</a> : Cell color picker dialog returns focus to document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6108">#6108</a> : Fixed div style.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5336">#5336</a> : Remove object style.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6155">#6155</a> : [[FF]] Modifying Table Header Properties by selecting first Row, causing several issues.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6163">#6163</a> : Focus not going to Tabs in Image dialog when we went to Edit the Image.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6177">#6177</a> : IE we can't start Numbered/Bulleted list on a Empty page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6034">#6034</a> : Horizontal Alignment applied to Table cell is not updated correctly in the Toolbar.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6112">#6112</a> : BIDI: Alignment set to text in Table cell is not shown in the Tool bar when we press Enter to start a new Paragraph.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6117">#6117</a> : BIDI: Language direction is changing when we come out of Numbered/Bulleted list.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6182">#6182</a> : Language Direction field on the Advanced tab of Table Properties dialog has a fixed pixel width.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5487">#5487</a> : Fullpage writer problem with line-break.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6197">#6197</a> : The CKEDITOR.loader base path auto-detection was not working with the _source folder.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6240">#6240</a> : Font Names &amp; Font Sizes should be shown Left Align even for RTL Languages.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5975">#5975</a> : Page-break should have proper Alt Text instead of Unknown object. so that JAWS reads it properly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6255">#6255</a> : Inserting a page break as the first node triggered an error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6188">#6188</a> : [IE7] Automatic color button had the wrong cursor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6129">#6129</a> : The show blocks' labels are now shown in the right for RTL languages.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5421">#5421</a> : &amp;shy; entity not converted when config.entities=false.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5769">#5769</a> : xhtml code generation problem &amp;nbsp; instead of &amp;#160; (htmlentities, entities,entities_additional,..., configuration).</li>
		<li><a href="http://dev.ckeditor.com/ticket/4472">#4472</a> : [FF3] Browser window scrolls to loaded CKEditor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6230">#6230</a> : Fixed invalid parameter count for setTimeout function call.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5335">#5335</a> : Several lines' formatted data will be merged to one line when we apply Numbers/Bullets.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5353">#5353</a> : wrong width of editor after resize() called in Firefox 3.6.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5778">#5778</a> : [IE] Unwanted scroll on first mouse right-click.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5218">#5218</a> : [FF] Copy/paste of an image from same domain changed URL to relative URL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6265">#6265</a> : Popup window properties were visible in the link dialog's target tab when nothing was selected.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6075">#6075</a> : [FF] Newly created links didn't fill in information on edit.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6183">#6183</a> : The toolbar panels options sometimes had the contents' link color.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6192">#6192</a> : [WebKit] Inserting smileys was not working because of editor focus issues.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6178">#6178</a> : [WebKit] Inserting elements by code was failing if the editor didn't receive the focus first.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6179">#6179</a> : [WebKit] The Image dialog was not working if the editor didn't receive the focus first.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4657">#4657</a> : [Opera] Styles where not working with collapsed selections.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5839">#5839</a> : "Insert row after" was removing the ids of the elements from the clicked row.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6315">#6315</a> : DIV plugin TT #2885 regression.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/6246">#6246</a> : Chinese Simplified;</li>
			<li><a href="http://dev.ckeditor.com/ticket/6256">#6256</a> : Dutch;</li>
			<li><a href="http://dev.ckeditor.com/ticket/6271">#6271</a> : English;</li>
		</ul></li>
	</ul>
	<h3>
			CKEditor 3.4</h3>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/6118">#6118</a> : Initial focus is now set to the tabs in the table properties dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6135">#6135</a> : The dialogadvtab plugin now uses the correct label.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6125">#6125</a> : Focus was lost after applying commands in Opera.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6137">#6137</a> : The table dialog was missing the default width value on second opening.</li>
	</ul>
	<h3>
			CKEditor 3.4 Beta</h3>
	<p>
			New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/5909">#5909</a> : New BiDi feature, making it possible to switch the base language direction of block elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5268">#5268</a> : Introducing the "tableresize" plugin, which makes it possible to resize tables columns by mouse drag. It's not enabled by default, so it must be enabled in the configurations file.</li>
		<li><a href="http://dev.ckeditor.com/ticket/979">#979</a> : New <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.enableTabKeyTools">enableTabKeyTools</a> configuration to allow using the TAB key to navigate through table cells.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4606">#4606</a> : Introduce the "autogrow" plugin, which makes the editor resize automatically, based on the contents size.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5737">#5737</a> : Added support for the <a href="http://www.w3.org/TR/html5/editing.html#contenteditable">HTML5 contenteditable attribute</a>, making it possible to define read only regions into the editor contents.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5418">#5418</a> : New "Advanced" tab introduced on the Table Properties dialog. It's based on the new dialogadvtab plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6082">#6082</a> : Introduced the <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.useComputedState">useComputedState</a> setting, making it possible to control whether toolbar features, like alignment and direction, should reflect the "computed" selection states, even when the effective feature value is not applied.</li>
	</ul>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/5911">#5911</a> : BiDi: List items should support and retain correct base language direction</li>
		<li><a href="http://dev.ckeditor.com/ticket/5689">#5689</a> : Make it possible to run CKEditor inside of Firefox chrome.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6042">#6042</a> : It wasn't possible to align a paragraph with the dir attribute to the opposite direction.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6058">#6058</a> : Fixed a small style glitch with file upload fields in IE+Quirks.</li>
	</ul>
	<h3>
			CKEditor 3.3.2</h3>
	<p>
			New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/5882">#5882</a> : Introduce the dialog#selectPage event, replicating the OnDialogTabChange feature available in FCKeditor 2.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5927">#5927</a> : The native controls in ui.dialog.elements can be styled with the controlStyle definition.</li>
	</ul>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/1644">#1644</a> : Removed references to cursor:hand in the stylesheets.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5411">#5411</a> : Anchor, hidden fields and Page-Break objects can no longer be resized.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5456">#5456</a> : Initial focus incorect in api_dialog sample page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5628">#5628</a> : Incorrect &lt;pre&gt; siblings merging.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5829">#5829</a> : Adding validation for start number field in list style dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5845">#5845</a> : Context menu on empty list item loses selection.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5860">#5860</a> : [IE] &gt; in attribute values are incorrectly escaped.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5905">#5905</a> : SCAYT is not any more enabled by default.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5736">#5736</a> : Improved the text generated for mailto: links if no text was selected.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4779">#4779</a> : Adjust resize_minWidth and resize_minHeight if smaller than actual dimensions.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5687">#5687</a> : Navigation through colors is now compatible with RTL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4615">#4615</a> : [IE] Text fields are no longer disrupted in dialog with RTL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5887">#5887</a> : The number of columns in the smileys table is now configurable via the smiley_columns setting.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5100">#5100</a> : It was possible to drag&amp;drop some elements like context menu items or dropdown entries.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5933">#5933</a> : Text color and background color panels don't have scrollbars anymore under office2003 and v2 skins.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5943">#5943</a> : An error is no longer generated when using percent or pixel values in the image dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5951">#5951</a> : Avoid problems with security systems due to the usage of UniversalXPConnect.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5441">#5441</a> : Avoid errors if the editor instance is removed from the DOM before calling its destroy() method.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4997">#4997</a> : Provide better access to the native input in the ui.dialog.file element.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5914">#5914</a> : Modified the Smileys dialog to make active only the images and not their borders.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5565">#5565</a> : The scrollbar does not behaves erratically when opening a rich combo in RTL page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5843">#5843</a> : In CKEditor 3.3: When we set the focus in the 'instanceReady' event, FF3.6 is giving js error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5902">#5902</a> : paste and pastetext dialogs cannot be skinned easily.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5959">#5959</a> : Dialog auto focus does not check for hidden tabs.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5415">#5415</a> : Undo not working when we change the Table Properties for the table on a saved page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5435">#5435</a> : IE: we can't start Numbered/Bulleted list in Tables by Clicking on Insert/Remove Numbers/Bullets Icon.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5832">#5832</a> : The JQuery adapter sample is not working properly with SSL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5728">#5728</a> : Text field &amp; Upload Button in Upload Tab of Image Properties dialog are not shown Properly in Arabic.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5436">#5436</a> : IE: Cursor goes to next Table Cell after we insert a Smiley in the Table Cell.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5580">#5580</a> : Maximize does not work properly in the Office 2003 and V2 skins.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5495">#5495</a> : The link dialog was breaking the undo system on some situations.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5775">#5775</a> : Required field's label to contain a CSS class to allow it to be styled differently.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5999">#5999</a> : Table dialog rows and columns fields are now marked as required.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5693">#5693</a> : baseHref detection in the flash dialog now works correctly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5690">#5690</a> : Table cell's width attribute is now respected properly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5819">#5819</a> : Introducing the new removeFormatCleanup event and making sure remove format doesn't break the showborder plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5558">#5558</a> : After pasting on WebKit based browsers the editor now scrolls to the end of the pasted content.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5799">#5799</a> : Correct plugin dependencies for the liststyle plugin with contextMenu and dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5436">#5436</a> : IE: The cursor was moving to the wrong position when inserting inline elements at the end of cells on tables.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5984">#5984</a> : Firefox: CTRL+HOME was creating an unwanted empty paragraph at the start of the document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5634">#5634</a> : IE: It was needed to click twice in the editor to make it editable on some situations.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5338">#5338</a> : Pasting from Open Office could lead on error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5224">#5224</a> : Some invalid markup could break the editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5455">#5455</a> : It was not possible to remove formatting from pasted content on specific cases.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5735">#5735</a> : IE: The editor was having focus issues when the previous selection got hidden by scroll operations.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5563">#5563</a> : Firefox: The disableObjectResizing and disableNativeTableHandles settings stopped working.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5781">#5781</a> : Firefox: Editing was not possible in an empty document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5293">#5293</a> : Firefox: Unwanted BR tags were being left in the editor output when it should be empty.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5280">#5280</a> : IE: Scrollbars where reacting improperly when clicking in the bar space.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5840">#5840</a> : Some dialog access keys are conflicting with "Ctrl + A", select all text behavior on text input.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6059">#6059</a> : Changing list type didn't preserve the list's attributes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5193">#5193</a> : In Firefox, the element path options had the text cursor instead of the arrow.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6073">#6073</a> : The list context menu was showing the wrong option when in a mixed list hierarchy.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6074">#6074</a> : The Insert Table Column command was duplicating the selected column cells ids.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6066">#6066</a> : The toolbar combos had the text cursor instead of the arrow.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6062">#6062</a> : The toolbar buttons had the text cursor instead of the arrow.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6068">#6068</a> : [IE7] A few labels were hidden in a RTL language.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6000">#6000</a> : Safari and Chrome where scrolling the contents to the top when moving the focus to the editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/6090">#6090</a> : IE: Textarea with selection inside causes Link dialog issues.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5079">#5079</a> : Page break in lists move to above the list when you switch from WYSIWYG to HTML mode and back.</li>
		<li>Updated the following language files:<ul>
			<li>Chinese Simplified;</li>
			<li>Hebrew;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5962">#5962</a> : German;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5645">#5645</a> : Portuguese;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5797">#5797</a> : Turkish;</li>
		</ul></li>
	</ul>
	<h3>
			CKEditor 3.3.1</h3>
	<p>
			Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/5780">#5780</a> : Text selection lost when opening some of the dialogs.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5787">#5787</a> : Liststyle plugin wasn't packaged into the core (CKEDITOR.resourceManager.load exception).</li>
		<li><a href="http://dev.ckeditor.com/ticket/5637">#5637</a> : Fix wrong nesting that generated "&lt;head&gt; must be a child of &lt;html&gt;" warning in Webkit.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5790">#5790</a> : Internal only attributes output on fullpage &lt;html&gt; tag.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5761">#5761</a> : [IE] Color dialog matrix buttons are barely clickable in quirks mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5759">#5759</a> : [IE] Clicking on the scrollbar and then on the host page causes error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5772">#5772</a> : List style dialog is missing tab page ids.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5782">#5782</a> : [FF] Wysiwyg mode is broken by 'display' style changes on editor's parent DOM tree.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5801">#5801</a> : [IE] contentEditable="false" doesn't apply in effect on inline-elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5794">#5794</a> : Empty find matching twice results in JavaScript error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5732">#5732</a> : If it isn't possible to connect to the SCAYT servers the dialogs might hang in Firefox. Fix for Firefox&gt;=3.6.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5807">#5807</a> : [FF2] New page command results in uneditable document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5807">#5807</a> : [FF2] SCAYT plugin is disabled in Firefox2 due to selection interference.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5772">#5772</a> : [IE] Some numbered list style types are not supported by IE6/7 and causes JavaScript error.</li>
	</ul>
	<h3>
		CKEditor 3.3</h3>
	<p>
		New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/635">#635</a> : The properties dialog will now open when double clicking on objects.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3893">#3893</a> : It's now possible to indent/outdent lists when selecting the first list item.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4968">#4968</a> : The <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.contentsLangDirection">contentsLangDirection</a> setting now has a default value 'ui' which inherit language direction from the editor UI language.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4649">#4649</a> : The color picker dialog is now accessible.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3593">#3593</a> : The editing area is now enabled by contentEditable="true" instead of designMode="on" to allow creating uneditable content elements in all browsers.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4056">#4056</a> : Hidden fields will now be displayed as fake element just like in FCKeditor 2.</li>
	</ul>
<h3>
		CKEditor 3.2.2</h3>
	<p>
		New features:</p>
	<ul>
		<li>The SCAYT spell checker is now enabled by default through the <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.scayt_autoStartup">autoStartup</a> setting.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5631">#5631</a> : The SCAYT context menu options can now be reorganized through the <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.scayt_contextMenuItemsOrder">scayt_contextMenuItemsOrder</a> setting.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4231">#4231</a> : Introducing the <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.resize_dir">resize_dir setting</a>, to be able to restrict manual resizing of the editor to only one direction (horizontal/vertical).</li>
		<li><a href="http://dev.ckeditor.com/ticket/5479">#5479</a> : Introducing the classic ASP integration files and samples.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5024">#5024</a> : Added samples (<a href="http://nightly.ckeditor.com/latest/ckeditor/_samples/output_html.html">HTML</a> and <a href="http://nightly.ckeditor.com/latest/ckeditor/_samples/output_xhtml.html">XHTML</a>) to show how to output HTML using fonts and other attributes instead of styles.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4358">#4358</a> : Introduced the List Properties dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5485">#5485</a> : Adding the <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.contentsLanguage">contentsLanguage</a> configuration option to be able to set the language for the editor contents.</li>
	</ul>
	<p>
		Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/5330">#5330</a> : Corrected detection of CTRL and META keys in Macs for the context menu.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5434">#5434</a> : Fixed access denied issues with IE when accessing web sites through IPv6 IP addresses.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4476">#4476</a> : [IE] Inaccessible empty list item contains sub list.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4881">#4881</a> : [IE] Selection range broken because of cutting a single control type element from it.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5505">#5505</a> : Image dialog throw JavaScript error when click close dialog before preview area is loading.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5144">#5144</a> : [Chrome] Paste in Webkit sometimes leaves extra 'div' element.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5021">#5021</a> : [Firefox] Typing in empty document start from second line when <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.enterMode">enterMode</a> = CKEDITOR.ENTER_BR.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5416">#5416</a> : [IE] Delete table throws a error when <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.enterMode">enterMode</a> = CKEDITOR.ENTER_BR.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4459">#4459</a> : [IE] Select element is penetrating the maximized editor in IE6.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5559">#5559</a> : [IE] The first call to <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.editor.html#setData">setData</a> is affected by iframe cache when loading the wysiwyg mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5567">#5567</a> : [IE] Remove inline styles in some case doesn't join identical siblings.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5450">#5450</a> : [FireFox] Press ENTER on 'replace' button result wrong.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5121">#5121</a> : Recognizes the &lt;br /&gt; tag as a separator when apply block styles and <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.enterMode">enterMode</a> = CKEDITOR.ENTER_BR.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5575">#5575</a> : <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.html#.replaceAll">CKEDITOR.replaceAll</a> should consider all kind of white spaces between class names.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5582">#5582</a> : Prevent the default behavior when click the 'x' button to close dialog box.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5584">#5584</a> : ENTER key with <a href="http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html#.forceEnterMode">forceEnterMode</a> turns on doesn't inherit current block attributes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4797">#4797</a> : [Opera] Press ENTER key in dialog fields to close throws JavaScript error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5578">#5578</a> : Add flash fake element align property when switch mode (source to wysiwyg).</li>
		<li><a href="http://dev.ckeditor.com/ticket/5577">#5577</a> : Update delete column behavior when choose multiple cells in the same column.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5512">#5512</a> : Open context menu with SHIFT+F10 doesn't get correct editor selection.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5433">#5433</a> : English protocol text directions in Link dialog are not incorrect in 'rtl' UI languages.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5553">#5553</a> : Paste dialog clipboard area text direction is incorrect for 'rtl' content languages.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4734">#4734</a> : Font size resets when font name is changed in an empty numbered list.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5237">#5237</a> : English text in dialogs' title is flipped when using RTL language.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3257">#3257</a> : Create list doesn't keep blocks as headings.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5111">#5111</a> : [Firefox] JAWS doesn't respect PC cursor mode (application role) on toolbar.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5530">#5530</a> : Page break for printing can't be removed with undo.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5381">#5381</a> : Unable to place cursor between two paragraphs in body.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5568">#5568</a> : [IE6/7] Selecting a entire table cell changes the original range.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5623">#5623</a> : [Firefox] Apply style that edges another inline style result incorrect.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5586">#5586</a> : [Firefox] Maximize the second editor ruins full screen mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5617">#5617</a> : HTML filter system does not allow two 'text' filter rules.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5663">#5663</a> : General memory clean up after destroying last instance.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5461">#5461</a> : [IE] Fix Paste from Word dialog doesn't accept imput problem.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5676">#5676</a> : Make color buttons use RRGGBB instead of RGB for better compatibility with IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4948">#4948</a> : [Safari] Select the first/last cell of table to open context menu may lead to undetected table.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5591">#5591</a> : [Firefox] Select a list item makes selected element broken.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5667">#5667</a> : Pasting in a RTL page content causes shows up the horizontal scrollbar.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5688">#5688</a> : Duplicate ids are used in dialog definition.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5719">#5719</a> : [IE] 'change' dialog event should not be triggered when dialog is already closed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5747">#5747</a> : [IE] Error thrown when IE input field editing mode is turned on.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5516">#5516</a> : IE8: Toolbar buttons have higher bottom padding.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5402">#5402</a> : SHIFT-ENTER could now be used to exit from preformat block.</li>
		<li>SCAYT plugin related:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/4836">#4836</a> : Using SCAYT result in fragile elements when applying inline styles.</li>
			<li><a href="http://dev.ckeditor.com/ticket/5425">#5425</a> : [Opera] Disable SCAYT plugin for Opera browser.</li>
			<li><a href="http://dev.ckeditor.com/ticket/5632">#5632</a> : SCAYT word marker is not visible on text with background-color set.</li>
			<li><a href="http://dev.ckeditor.com/ticket/4125">#4125</a> : Remove Format command incorrectly removes SCAYT word markers.</li>
			<li><a href="http://dev.ckeditor.com/ticket/5671">#5671</a> : SCAYT bootstrap script could be added multiple times unnecessarily.</li>
			<li><a href="http://dev.ckeditor.com/ticket/5573">#5573</a> : SCAYT move cursor position after insert element into marked word text.</li>
			<li><a href="http://dev.ckeditor.com/ticket/5546">#5546</a> : SCAYT interferes with undo/redo commands.</li>
			<li><a href="http://dev.ckeditor.com/ticket/5570">#5570</a> : [IE] First enabling SCAYT blind cursor in editor.</li>
			<li><a href="http://dev.ckeditor.com/ticket/5741">#5741</a> : Enable SCAYT cause error in multiple editor instances.</li>
			<li><a href="http://dev.ckeditor.com/ticket/5744">#5744</a> : Remove editor with SCAYT enabled in source mode throws error.</li>
		</ul></li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/5432">#5432</a> : Dutch;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5619">#5619</a> : Finnish;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5515">#5515</a> : Hebrew;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5588">#5588</a> : Turkish;</li>
		</ul></li>
	</ul>
	<h3>
		CKEditor 3.2.1</h3>
	<p>
		New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/4478">#4478</a> : Enable the SelectAll command in source mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5150">#5150</a> : Allow names in the CKEDITOR.config.colorButton_colors setting.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4810">#4810</a> : Adding configuration option for image dialog preview area filling text.</li>
		<li><a href="http://dev.ckeditor.com/ticket/536">#536</a> : Object style now could be applied on any parent element of current selection.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5290">#5290</a> : Unified stylesSet loading removing dependencies from the styles combo.
					Now the configuration entry is named 'config.stylesSet' instead of config.stylesCombo_stylesSet and the default location
					is under the 'styles' plugin instead of 'stylescombo'.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5352">#5352</a> : Allow to define the stylesSet array in the config object for the editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5302">#5302</a> : Adding config option "forceEnterMode".</li>
		<li><a href="http://dev.ckeditor.com/ticket/5216">#5216</a> : Extend CKEDITOR.appendTo to allow a data parameter for the initial value.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5024">#5024</a> : Added sample to show how to output XHTML and avoid deprecated tags.</li>
	</ul>
	<p>
		Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/5152">#5152</a> : Indentation using class attribute doesn't work properly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4682">#4682</a> : It wasn't possible to edit block elements in IE that had styles like width, height or float.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4750">#4750</a> : Correcting default order of buttons layout in dialogs on Mac.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4932">#4932</a> : Fixed collapse button not clickable on simple toolbar.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5228">#5228</a> : Link dialog is automatically changes protocol when URLs that starts with '?'.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4877">#4877</a> : Fixed CKEditor displays source code in one long line (IE quirks mode + office2003 skin).</li>
		<li><a href="http://dev.ckeditor.com/ticket/5132">#5132</a> : Apply inline style leaks into sibling words which are seperated spaces.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3599">#3599</a> : Background color style on sized text displayed as narrow band behind.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4661">#4661</a> : Translation missing in link dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5240">#5240</a> : Flash alignment property is not presented visually on fake element.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4910">#4910</a> : Pasting in IE scrolls document to the end.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5041">#5041</a> : Table summary attribute can't be removed with dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5124">#5124</a> : All inline styles cannot be applied on empty spaces.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3570">#3570</a> : SCAYT marker shouldn't appear inside elements path bar.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4553">#4553</a> : Dirty check result incorrect when editor document is empty.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4555">#4555</a> : Unreleased memory when editor is created and destroyed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5118">#5118</a> : Arrow keys navigation in RTL languages is incorrect.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4721">#4721</a> : Remove attribute 'value' of checkbox in IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5278">#5278</a> : IE: Add validation to check for bad window names of popup window.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5171">#5171</a> : Dialogs contains lists don't have proper voice labels.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4791">#4791</a> : Can't place cursor inside a form that end with a checkbox/radio.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4479">#4479</a> : StylesCombo doesn't reflect the selection state until it's first opened.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4717">#4717</a> : 'Unlink' and 'Outdent' command buttons should be disabled on editor startup.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5119">#5119</a> : Disabled command buttons are not being properly styled when focused.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5307">#5307</a> : Hide dialog page cause problem when there's two tab pages remain.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5343">#5343</a> : Active list item ARIA role is wrongly placed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3599">#3599</a> : Background color style applying to text with font size style has been narrowly rendered.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4711">#4711</a> : Line break character inside preformatted text makes it unable to type text at the end of previous line.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4829">#4829</a> : [IE] Apply style from combo has wrong result on manually created selection.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4830">#4830</a> : Retrieving selected element isn't always right, especially selecting using keyboard (SHIFT+ARROW).</li>
		<li><a href="http://dev.ckeditor.com/ticket/5128">#5128</a> : Element attribute inside preformatted text is corrupted when converting to other blocks.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5190">#5190</a> : Template list entry shouldn't gain initial focus open templates list dialog opens.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5238">#5238</a> : Menu button doesn't display arrow icon in high-contrast mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3576">#3576</a> : Non-attributed element of the same name with the applied style is incorrectly removed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5221">#5221</a> : Insert table into empty document cause JavaScript error thrown.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5242">#5242</a> : Apply 'automatic' color option of text color incorrectly removes background-color style.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4719">#4719</a> : IE does not escape attribute values properly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5170">#5170</a> : Firefox does not insert text into styled element properly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4026">#4026</a> : Office2003 skin has no toolbar button borders in High Contrast in IE7.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4348">#4348</a> : There should have exception thrown when 'CKEDITOR_BASEPATH' couldn't be figured out automatically.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5364">#5364</a> : Focus may not be put into dialog correctly when dialog skin file is loading slow.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4016">#4016</a> : Justify the layout of forms select dialog in Chrome and IE7.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5373">#5373</a> : Variable 'pathBlockElements' defines wrong items in CKEDITOR.dom.elementPath.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5082">#5082</a> : Ctrl key should be described as Cmd key on Mac.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5182">#5182</a> : Context menu is not been announced correctly by ATs.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4898">#4898</a> : Can't navigate outside table under the last paragraph of document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4950">#4950</a> : List commands could compromise list item attribute and styles.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5018">#5018</a> : Find result highlighting remove normal font color styles unintentionally.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5376">#5376</a> : Unable to exit list from within a empty block under list item.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5145">#5145</a> : Various SCAYT fixes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5319">#5319</a> : Match whole word doesn't work anymore after replacement has happened.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5363">#5363</a> : 'title' attribute now presents on all editor iframes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5374">#5374</a> : Unable to toggle inline style when the selection starts at the linefeed of the previous paragraph.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4513">#4513</a> : Selected link element is not always correctly detected when using keyboard arrows to perform such selection.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5372">#5372</a> : Newly created sub list should inherit nothing from the original (parent) list, except the list type.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5274">#5274</a> : [IE6] Templates preview image is displayed in wrong size.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5292">#5292</a> : Preview in font size and family doesn't work with custom styles.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5396">#5396</a> : Selection is lost when use cell properties dialog to change cell type to header.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4082">#4082</a> : [IE+Quirks] Preview text in the image dialog is not wrapping.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4197">#4197</a> : Fixing format combo don't hide when editor blur on Safari.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5401">#5401</a> : The context menu break layout with Office2003 and V2 skin on IE quirks mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4825">#4825</a> : Fixing browser context menu is opened when clicking right mouse button twice.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5356">#5356</a> : The SCAYT dialog had issues with Prototype enabled pages.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5266">#5266</a> : SCAYT was disturbing the rendering of TH elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4688">#4688</a> : SCAYT was interfering on checkDirty.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5429">#5429</a> : High Contrast mode was being mistakenly detected when loading the editor through Dojo's xhrGet.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5221">#5221</a> : Range is mangled when making collapsed selection in an empty paragraph.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5261">#5261</a> : Config option 'scayt_autoStartup' slow down editor loading.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3846">#3846</a> : Google Chrome - No Img properties after inserting.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5465">#5465</a> : ShiftEnter=DIV doesn't respect list item when pressing ENTER at end of list item.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5454">#5454</a> : After replaced success, the popup window couldn't be closed and a js error occured.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4784">#4784</a> : Incorrect cursor position after delete table cells.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5149">#5149</a> : [FF] Cursor disappears after maximize when the editor has focus.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5220">#5220</a> : DTD now shows tolerance to &lt;style&gt; appear inside content.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5440">#5440</a> : Mobile browsers (iPhone, Android...) are marked as incompatible as they don't support editing features.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5504">#5504</a> : [IE6/7] 'Paste' dialog will always get opened even when user allows the clipboard access dialog when using 'Paste' button.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/5326">#5326</a> : Catalan;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5370">#5370</a> : Faroese;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5392">#5392</a> : Finnish;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4580">#4580</a> : Hungarian;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5301">#5301</a> : Norwegian;</li>
		</ul></li>
	</ul>
	<h3>
		CKEditor 3.2</h3>
	<p>
		New features:</p>
	<ul>
		<li>Several accessibility enhancements:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/4502">#4502</a> : The editor accessibility is now totally based on <a href="http://www.w3.org/WAI/intro/aria">WAI-ARIA</a>.</li>
			<li><a href="http://dev.ckeditor.com/ticket/5015">#5015</a> : Adding accessibility help dialog plugin.</li>
			<li><a href="http://dev.ckeditor.com/ticket/5014">#5014</a> : Keyboard navigation compliance with screen reader suggested keys.</li>
			<li><a href="http://dev.ckeditor.com/ticket/4595">#4595</a> : Better accessibility in the Templates dialog.</li>
			<li><a href="http://dev.ckeditor.com/ticket/3389">#3389</a> : Esc/Arrow Key now works for closing sub menu.</li>
		</ul></li>
		<li><a href="http://dev.ckeditor.com/ticket/4973">#4973</a> : The Style field in the Div Container dialog is now loading the styles defined in the default styleset used by the Styles toolbar combo.</li>
	</ul>
	<p>
		Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/5049">#5049</a> : Form Field list command in JAWS incorrectly lists extra fields.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5008">#5008</a> : Lock/Unlock ratio buttons in the Image dialog was poorly designed in High Contrast mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3980">#3980</a> : All labels in dialogs now use &lt;label&gt; instead of &lt;div&gt;.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5213">#5213</a> : Reorganization of some entries in the language files to make it more consistent.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5199">#5199</a> : In IE, single row toolbars didn't have the bottom padding.</li>
	</ul>
	<h3>
		CKEditor 3.1.1</h3>
	<p>
		New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/4399">#4399</a> : Improved support for external file browsers by allowing executing a callback function.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4612">#4612</a> : The text of links is now updated if it matches the URL to which it points to.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4936">#4936</a> : New localization support for the Welsh language.</li>
	</ul>
	<p>
		Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/4272">#4272</a> : Kama skin toolbar was broken in IE+Quirks+RTL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4987">#4987</a> : Changed the url which is called by the Browser Server button in the Link tab of Image Properties dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5030">#5030</a> : The CKEDITOR.timestamp wasn't been appended to the skin.js file.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4993">#4993</a> : Removed the float style from images when the user selects 'not set' for alignment.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4944">#4944</a> : Fixed a bug where nested list structures with inconsequent levels were not being pasted correctly from MS Word.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4637">#4637</a> : Table cells' 'nowrap' attribute was not being loaded by the cell property dialog. Thanks to pomu0325.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4724">#4724</a> : Using the mouse to insert a link in IE might create incorrect results.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4640">#4640</a> : Small optimizations for the fileBrowser plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4583">#4583</a> : The "Target Frame Name" field is now visible when target is set to 'frame' only.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4863">#4863</a> : Fixing iframedialog's height doesn't stretch to 100% (except IE Quirks).</li>
		<li><a href="http://dev.ckeditor.com/ticket/4964">#4964</a> : The BACKSPACE key positioning was not correct in some cases with Firefox.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4980">#4980</a> : Setting border, vspace and hspace of images to zero was not working.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4773">#4773</a> : The fileBrowser plugin was overwriting onClick functions eventually defined on fileButton elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4731">#4731</a> : The clipboard plugin was missing a reference to the dialog plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5051">#5051</a> : The about plugin was missing a reference to the dialog plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5146">#5146</a> : The wsc plugin was missing a reference to the dialog plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4632">#4632</a> : The print command will now properly break on the insertion point of page break for printing.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4862">#4862</a> : The English (United Kingdom) language file has been renamed to en-gb.js.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4618">#4618</a> : Selecting an emoticon or the lock and reset buttons in the image dialog fired the onBeforeUnload event in IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4678">#4678</a> : It was not possible to set tables' width to empty value.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5012">#5012</a> : Fixed dependency issues with the menu plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5040">#5040</a> : The editor will not properly ignore font related settings that have extra item separators (semi-colons).</li>
		<li><a href="http://dev.ckeditor.com/ticket/4046">#4046</a> : Justify should respect config.enterMode = CKEDITOR.ENTER_BR.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4622">#4622</a> : Inserting tables multiple times was corrupting the undo system.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4647">#4647</a> : [IE] Selection on an element within positioned container is lost after open context-menu then click one menu item.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4683">#4683</a> : Double-quote character in attribute values was not escaped in the editor output.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4762">#4762</a> : [IE] Unexpected vertical-scrolling behavior happens whenever focus is moving out of editor in source mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4772">#4772</a> : Text color was not being applied properly on links.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4795">#4795</a> : [IE] Press 'Del' key on horizontal line or table result in error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4824">#4824</a> : [IE] &lt;br/&gt; at the very first table cell breaks the editor selection.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4851">#4851</a> : [IE] Delete table rows with context-menu may cause error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4951">#4951</a> : Replacing text with empty string was throwing errors.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4963">#4963</a> : Link dialog was not opening properly for e-mail type links.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5043">#5043</a> : Removed the possibility of having an unwanted script tag being outputted with the editor contents.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3678">#3678</a> : There were issues when editing links inside floating divs with IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4763">#4763</a> : Pressing ENTER key with text selected was not deleting the text in some situations.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5096">#5096</a> : Simple ampersand attribute value doesn't work for more than one occurrence.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3494">#3494</a> : Context menu is too narrow in some translations.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5005">#5005</a> : Fixed HTML errors in PHP samples.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5123">#5123</a> : Fixed broken XHTML in User Interface Languages sample.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4893">#4893</a> : Editor now understands table cell inline styles.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4611">#4611</a> : Selection around &lt;select&gt; in editor doesn't cause error anymore.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4886">#4886</a> : Extra BR tags were being created in the output HTML.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4933">#4933</a> : Empty tags with BR were being left in the DOM.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5127">#5127</a> : There were errors when removing dialog definition pages through code.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4767">#4767</a> : CKEditor was not working when ckeditor_source.js is loaded in the &lt;body&gt; .</li>
		<li><a href="http://dev.ckeditor.com/ticket/5062">#5062</a> : Avoided security warning message when loading the wysiwyg area in IE6 under HTTPS.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5135">#5135</a> : The TAB key will now behave properly when in Source mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4988">#4988</a> : It wasn't possible to use forcePasteAsPlainText with Safari on Mac.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5095">#5095</a> : Safari on Mac deleted the current selection in the editor when Edit menu was clicked.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5140">#5140</a> : In High Contrast mode, arrows were now been displayed for menus with submenus.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5163">#5163</a> : The undo system was not working on some specific cases.</li>
		<li><a href="http://dev.ckeditor.com/ticket/5162">#5162</a> : The ajax sample was throwing errors when loading data.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4999">#4999</a> : The Template dialog was not generating an undo snapshot.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/5006">#5006</a> : Dutch;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5039">#5039</a> : Finnish;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5148">#5148</a> : Hebrew;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5071">#5071</a> : Russian;</li>
			<li><a href="http://dev.ckeditor.com/ticket/5147">#5147</a> : Spanish;</li>
		</ul></li>
	</ul>
	<h3>
		CKEditor 3.1</h3>
	<p>
		New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/4067">#4067</a> : Introduced the full page editing support (from &lt;html&gt; to &lt;/html&gt;).</li>
		<li><a href="http://dev.ckeditor.com/ticket/4228">#4228</a> : Introduced the Shared Spaces feature.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4379">#4379</a> : Introduced the new powerful pasting system and word cleanup procedure, including enhancements to the paste as plain text feature.</li>
		<li><a href="http://dev.ckeditor.com/ticket/2872">#2872</a> : Introduced the new native PHP API, the first standardized server side support.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4210">#4210</a> : Added CKEditor plugin for jQuery.</li>
		<li><a href="http://dev.ckeditor.com/ticket/2885">#2885</a> : Added 'div' dialog and corresponding context menu options.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4574">#4574</a> : Added the table merging tools and corresponding context menu options.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4340">#4340</a> : Added the email protection option for link dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4463">#4463</a> : Added inline CSS support in all places where custom stylesheet could apply.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3881">#3881</a> : Added color dialog for 'more color' option in color buttons.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4341">#4341</a> : Added the 'showborder' plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4549">#4549</a> : Make the anti-cache query string configurable.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4708">#4708</a> : Added the 'htmlEncodeOutput' config option.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4342">#4342</a> : Introduced the bodyId and bodyClass settings to specify the id and class. to be used in the editing area at runtime.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3401">#3401</a> : Introduced the baseHref setting so it's possible to set the URL to be used to resolve absolute and relative URLs in the contents.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4729">#4729</a> : Added support to fake elements for comments.</li>
	</ul>
	<p>
		Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/4707">#4707</a> : Fixed invalid link is requested in image preview.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4461">#4461</a> : Fixed toolbar separator line along side combo enlarging the toolbar height.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4596">#4596</a> : Fixed image re-size lock buttons aren't accessible in high-contrast mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4676">#4676</a> : Fixed editing tables using table properties dialog overwrites original style values.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4714">#4714</a> : Fixed IE6 JavaScript error when editing flash by commit 'Flash' dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3905">#3905</a> : Fixed 'wysiwyg' mode causes unauthenticated content warnings over SSL in FF 3.5.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4768">#4768</a> : Fixed open context menu in IE throws js error when focus is not inside document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4822">#4822</a> : Fixed applying 'Headers' to existing table does not work in IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4855">#4855</a> : Fixed toolbar doesn't wrap well for 'v2' skin in all browsers.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4882">#4882</a> : Fixed auto detect paste from MS-Word is not working for Safari.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4882">#4882</a> : Fixed unexpected margin style left behind on content cleaning up from MS-Word.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4896">#4896</a> : Fixed paste nested list from MS-Word with measurement units set to cm is broken.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4899">#4899</a> : Fixed unable to undo pre-formatted style.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4900">#4900</a> : Fixed ratio-lock inconsistent between browsers.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4901">#4901</a> : Fixed unable to edit any link with popup window's features in Firefox.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4904">#4904</a> : Fixed when paste happen from dialog, it always throw JavaScript error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4905">#4905</a> : Fixed paste plain text result incorrect when content from dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4889">#4889</a> : Fixed unable to undo 'New Page' command after typing inside editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4892">#4892</a> : Fixed table alignment style is not properly represented by the wrapping div.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4918">#4918</a> : Fixed switching mode when maximized is showing background page contents.</li>
	</ul>
	<h3>
		CKEditor 3.0.2</h3>
	<p>
		New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/4343">#4343</a> : Added the configuration option &#39;browserContextMenuOnCtrl&#39; so it&#39;s possible to enable the default browser context menu by holding the CTRL key.</li>
	</ul>
	<p>
		Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/4552">#4552</a> : Fixed float panel doesn't show up since editor instanced been destroyed once.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3918">#3918</a> : Fixed fake object is editable with Image dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4053">#4053</a> : Fixed 'Form Properties' missing from context menu when selection collapsed inside form.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4401">#4401</a> : Fixed customized by removing 'upload' tab page from 'Link dialog' cause JavaScript error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4477">#4477</a> : Adding missing tag names in object style elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4567">#4567</a> : Fixed IE throw error when pressing BACKSPACE in source mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4573">#4573</a> : Fixed 'IgnoreEmptyPargraph' config doesn't work with the config 'entities' is set to 'false'.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4614">#4614</a> : Fixed attribute protection fails because of line-break.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4546">#4546</a> : Fixed UIColor plugin doesn't work when editor id contains CSS selector preserved keywords.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4609">#4609</a> : Fixed flash object is lost when loading data from outside editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4625">#4625</a> : Fixed editor stays visible in a div with style 'visibility:hidden'.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4621">#4621</a> : Fixed clicking below table caused an empty table been generated.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3373">#3373</a> : Fixed empty context menu when there's no menu item at all.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4473">#4473</a> : Fixed setting rules on the same element tag name throws error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4514">#4514</a> : Fixed press 'Back' button breaks wysiwyg editing mode is Firefox.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4542">#4542</a> : Fixed unable to access buttons using tab key in Safari and Opera.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4577">#4577</a> : Fixed relative link url is broken after opening 'Link' dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4597">#4597</a> : Fixed custom style with same attribute name but different attribute value doesn't work.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4651">#4651</a> : Fixed 'Deleted' and 'Inserted' text style is not rendering in wysiwyg mode and is wrong is source mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4654">#4654</a> : Fixed 'CKEDITOR.config.font_defaultLabel(fontSize_defaultLabel)' is not working.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3950">#3950</a> : Fixed table column insertion incorrect when selecting empty cell area.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3912">#3912</a> : Fixed UIColor not working in IE when page has more than 30+ editors.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4031">#4031</a> : Fixed mouse cursor on toolbar combo has more than 3 shapes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4041">#4041</a> : Fixed open context menu on multiple cells to remove them result in only one removed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4185">#4185</a> : Fixed resize handler effect doesn't affect flash object on output.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4196">#4196</a> : Fixed 'Remove Numbered/Bulleted List' on nested list doesn't work well on nested list.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4200">#4200</a> : Fixed unable to insert 'password' type filed with attributes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4530">#4530</a> : Fixed context menu couldn't open in Opera.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4536">#4536</a> : Fixed keyboard navigation doesn't work at all in IE quirks mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4584">#4584</a> : Fixed updated link Target field is not updating when updating to certain values.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4603">#4603</a> : Fixed unable to disable submenu items in contextmenu.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4672">#4672</a> : Fixed unable to redo the insertion of horizontal line.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4677">#4677</a> : Fixed 'Tab' key is trapped by hidden dialog elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4073">#4073</a> : Fixed insert template with replace option could result in empty document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4455">#4455</a> : Fixed unable to start editing when image inside document not loaded.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4517">#4517</a> : Fixed 'dialog_backgroundCoverColor' doesn't work on IE6.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3165">#3165</a> : Fixed enter key in empty list item before nested one result in collapsed line.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4527">#4527</a> : Fixed checkbox generate invalid 'checked' attribute.</li>
		<li><a href="http://dev.ckeditor.com/ticket/1659">#1659</a> : Fixed unable to click below content to start editing in IE with 'config.docType' setting to standard compliant.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3933">#3933</a> : Fixed extra &lt;br&gt; left at the end of document when the last element is a table.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4736">#4736</a> : Fixed PAGE UP and PAGE DOWN keys in standards mode are not working.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4725">#4725</a> : Fixed hitting 'enter' before html comment node produces a JavaScript error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4522">#4522</a> : Fixed unable to redo when typing after insert an image with relative url.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4594">#4594</a> : Fixed context menu goes off-screen when mouse is at right had side of screen.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4673">#4673</a> : Fixed undo not available straight away if shift key is used to enter first character.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4690">#4690</a> : Fixed the parsing of nested inline elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4450">#4450</a> : Fixed selecting multiple table cells before apply justify commands generates spurious paragraph in Firefox.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4733">#4733</a> : Fixed dialog opening sometimes hang up Firefox and Safari.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4498">#4498</a> : Fixed toolbar collapse button missing tooltip.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4738">#4738</a> : Fixed inserting table inside bold/italic/underline generates error on ENTER_BR mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4246">#4246</a> : Fixed avoid XHTML deprecated attributes for image styling.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4543">#4543</a> : Fixed unable to move cursor between table and hr.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4764">#4764</a> : Fixed wrong exception message when CKEDITOR.editor.append() to non-existing elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4521">#4521</a> : Fixed dialog layout in IE6/7 may have scroll-bar and other weird effects.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4709">#4709</a> : Fixed inconsistent scroll-bar behavior on IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4776">#4776</a> : Fixed preview page failed to open when relative URl contains in document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4812">#4812</a> : Fixed 'Esc' key not working on dialogs in Opera.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/4346">#4346</a> : Dutch;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4837">#4837</a> : Finnish;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4371">#4371</a> : Hebrew;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4371">#4607</a> <a href="http://dev.ckeditor.com/ticket/4713">#4713</a> : Japanese;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4660">#4660</a> : Norwegian.</li>
		</ul></li>
	</ul>
	<h3>
		CKEditor 3.0.1</h3>
	<p>
		New features:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/4219">#4219</a> : Added fallback mechanism for config.language.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4194">#4194</a> : Added support for using multiple css style sheets within the editor.</li>
	</ul>
	<p>
		Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/3898">#3898</a> : Added validation for URL value in Image dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3528">#3528</a> : Fixed Context Menu issue when triggered using Shift+F10.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4028">#4028</a> : Maximize control's tool tip was wrong once it is maximized.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4237">#4237</a> : Toolbar is chopped off in Safari browser 3.x.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4241">#4241</a> : Float panels are left on screen while editor is destroyed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4274">#4274</a> : Double click event is incorrect handled in 'divreplace' sample.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4354">#4354</a> : Fixed TAB key on toolbar to not focus disabled buttons.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3856">#3856</a> : Fixed focus and blur events in source view mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3438">#3438</a> : Floating panels are off by (-1px, 0px) in RTL mode.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3370">#3370</a> : Refactored use of CKEDITOR.env.isCustomDomain().</li>
		<li><a href="http://dev.ckeditor.com/ticket/4230">#4230</a> : HC detection caused js error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3978">#3978</a> : Fixed setStyle float on IE7 strict.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4262">#4262</a> : Tab and Shift+Tab was not working to cycle through CTRL+SHIFT+F10 context menu in IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3633">#3633</a> : Default context menu isn't disabled in toolbar, status bar, panels...</li>
		<li><a href="http://dev.ckeditor.com/ticket/3897">#3897</a> : Now there is no image previews when the URL is empty in image dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4048">#4048</a> : Context submenu was lacking uiColor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3568">#3568</a> : Dialogs now select all text when tabbing to text inputs.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3727">#3727</a> : Cell Properties dialog was missing color selection option.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3517">#3517</a> : Fixed "Match cyclic" field in Find &amp; Replace dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4368">#4368</a> : borderColor table cell attribute haven't worked for none-IE</li>
		<li><a href="http://dev.ckeditor.com/ticket/4203">#4203</a> : In IE quirks mode + toolbar collapsed + source mode editing block height was incorrect.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4387">#4387</a> : Fixed: right clicking in Kama skin can lead to a javascript error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4397">#4397</a> : Wysiwyg mode caused the host page scroll.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4385">#4385</a> : Fixed editor's auto adjusting on DOM structure were confusing the dirty checking mechanism.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4397">#4397</a> : Fixed regression of [3816] where turn on design mode was causing Firefox3 to scroll the host page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4254">#4254</a> : Added basic API sample.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4107">#4107</a> : Normalize css font-family style text for correct comparision.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3664">#3664</a> : Insert block element in empty editor document should not create new paragraph.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4037">#4037</a> : 'id' attribute is missing with Flash dialog advanced page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4047">#4047</a> : Delete selected control type element when 'Backspace' is pressed on it.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4191">#4191</a> : Fixed: dialog changes confirmation on image dialog appeared even when no changes have been made.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4351">#4351</a> : Dash and dot could appear in attribute names.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4355">#4355</a> : 'maximize' and 'showblock' commands shouldn't take editor focus.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4504">#4504</a> : Fixed 'Enter'/'Esc' key is not working on dialog button.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4245">#4245</a> : 'Strange Template' now come with a style attribute for width.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4512">#4512</a> : Fixed styles plugin incorrectly adding semicolons to style text.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3855">#3855</a> : Fixed loading unminified _source files when ckeditor_source.js is used.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3717">#3717</a> : Dialog settings defaults can now be overridden in-page through the CKEDITOR.config object.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4481">#4481</a> : The 'stylesCombo_stylesSet' configuration entry didn't work for full URLs.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4480">#4480</a> : Fixed scope attribute in th.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4467">#4467</a> : Fixed bug to use custom icon in context menus. Thanks to george.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4190">#4190</a> : Fixed select field dialog layout in Safari.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4518">#4518</a> : Fixed unable to open dialog without editor focus in IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4519">#4519</a> : Fixed maximize without editor focus throw error in IE.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/3947">#3947</a> : Arabic;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4466">#4466</a> : Czech;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4363">#4363</a> : Danish;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4346">#4346</a> : Dutch;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4371">#4371</a> <a href="http://dev.ckeditor.com/ticket/4456">#4456</a> : Hebrew;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4382">#4382</a> : Polish.</li>
		</ul></li>
	</ul>
	<h3>
		CKEditor 3.0</h3>
	<p>
		New features:</p>
	<ul>
	<li><a href="http://dev.ckeditor.com/ticket/3188">#3188</a> : Introduce
		&lt;pre&gt; formatting feature when converting from other blocks.</li>
	<li><a href="http://dev.ckeditor.com/ticket/4445">#4445</a> : editor::setData now support an optional callback parameter.</li>
	</ul>
	<p>
		Fixed issues:</p>
	<ul>
		<li><a href="http://dev.ckeditor.com/ticket/2856">#2856</a> : Fixed problem with inches in Paste From Word plugin.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3929">#3929</a> : Using Paste dialog,
			the text is pasted into current selection</li>
		<li><a href="http://dev.ckeditor.com/ticket/3920">#3920</a> : Mouse cursor over characters in
			Special Character dialog now is correct</li>
		<li><a href="http://dev.ckeditor.com/ticket/3882">#3882</a> : Fixed an issue
			with PasteFromWord dialog in which default values was ignored</li>
		<li><a href="http://dev.ckeditor.com/ticket/3859">#3859</a> : Fixed Flash dialog layout in Webkit</li>
		<li><a href="http://dev.ckeditor.com/ticket/3852">#3852</a> : Disabled textarea resizing in dialogs</li>
		<li><a href="http://dev.ckeditor.com/ticket/3831">#3831</a> : The attempt to remove the contextmenu plugin
			will not anymore break the editor</li>
		<li><a href="http://dev.ckeditor.com/ticket/3781">#3781</a> : Colorbutton is now disabled in 'source' mode</li>
		<li><a href="http://dev.ckeditor.com/ticket/3848">#3848</a> : Fixed an issue with Webkit in witch
			elements in the Image and Link dialogs had wrong dimensions.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3808">#3808</a> : Fixed UI Color Picker dialog size in example page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3658">#3658</a> : Editor had horizontal scrollbar in IE6.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3819">#3819</a> : The cursor was not visible
			when applying style to collapsed selections in Firefox 2.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3809">#3809</a> : Fixed beam cursor
			when mouse cursor is over text-only buttons in IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3815">#3815</a> : Fixed an issue
			with the form dialog in which the "enctype" attribute is outputted as "encoding".</li>
		<li><a href="http://dev.ckeditor.com/ticket/3785">#3785</a> : Fixed an issue
			in CKEDITOR.tools.htmlEncode() which incorrectly outputs &amp;nbsp; in IE8.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3820">#3820</a> : Fixed an issue in
			bullet list command in which a list created at the bottom of another gets merged to the top.
			</li>
		<li><a href="http://dev.ckeditor.com/ticket/3830">#3830</a> : Table cell properties dialog
			doesn't apply to all selected cells.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3835">#3835</a> : Element path is not refreshed
			after click on 'newpage'; and safari is not putting focus on document also.
			</li>
		<li><a href="http://dev.ckeditor.com/ticket/3821">#3821</a> : Fixed an issue with JAWS in which
			toolbar items are read inconsistently between virtual cursor modes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3789">#3789</a> : The &quot;src&quot; attribute
			was getting duplicated in some situations.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3591">#3591</a> : Protecting flash related elements
			including '&lt;object&gt;', '&lt;embed&gt;' and '&lt;param&gt;'.
			</li>
		<li><a href="http://dev.ckeditor.com/ticket/3759">#3759</a> : Fixed CKEDITOR.dom.element::scrollIntoView
			logic bug which scroll even element is inside viewport.
			</li>
		<li><a href="http://dev.ckeditor.com/ticket/3773">#3773</a> : Fixed remove list will merge lines.
			</li>
		<li><a href="http://dev.ckeditor.com/ticket/3829">#3829</a> : Fixed remove empty link on output data.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3730">#3730</a> : Indent is performing on the whole
			block instead of selected lines in enterMode = BR.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3844">#3844</a> : Fixed UndoManager register keydown on obsoleted document</li>
		<li><a href="http://dev.ckeditor.com/ticket/3805">#3805</a> : Enabled SCAYT plugin for IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3834">#3834</a> : Context menu on table caption was incorrect.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3812">#3812</a> : Fixed an issue in which the editor
			may show up empty or uneditable in IE7, 8 and Firefox 3.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3825">#3825</a> : Fixed JS error when opening spellingcheck.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3862">#3862</a> : Fixed html parser infinite loop on certain malformed
			source code.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3639">#3639</a> : Button size was inconsistent.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3874">#3874</a> : Paste as plain text in Safari loosing lines.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3849">#3849</a> : Fixed IE8 crashes when applying lists and indenting.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3876">#3876</a> : Changed dialog checkbox and radio labels to explicit labels.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3843">#3843</a> : Fixed context submenu position in IE 6 &amp; 7 RTL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3864">#3864</a> : [FF]Document is not editable after inserting element on a fresh page.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3883">#3883</a> : Fixed removing inline style logic incorrect on Firefox2.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3884">#3884</a> : Empty "href" attribute was duplicated on output data.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3858">#3858</a> : Fixed the issue where toolbars
			break up in IE6 and IE7 after the browser is resized.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3868">#3868</a> : [chrome] SCAYT toolbar options was in reversed order.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3875">#3875</a> : Fixed an issue in Safari where
			table row/column/cell menus are not useable when table cells are selected.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3896">#3896</a> : The editing area was
			flashing when switching forth and back to source view.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3894">#3894</a> : Fixed an issue where editor failed to initialize when using the on-demand loading way.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3903">#3903</a> : Color button plugin doesn't read config entry from editor instance correctly.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3801">#3801</a> : Comments at the start of the document was lost in IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3871">#3871</a> : Unable to redo when undos to the front of snapshots stack.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3909">#3909</a> : Move focus from editor into a text input control is broken.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3870">#3870</a> : The empty paragraph
			desappears when hitting ENTER after &quot;New Page&quot;.</li>
 		<li><a href="http://dev.ckeditor.com/ticket/3887">#3887</a> : Fixed an issue in which the create
 			list command may leak outside of a selected table cell and into the rest of document.</li>
 		<li><a href="http://dev.ckeditor.com/ticket/3916">#3916</a> : Fixed maximize does not enlarge editor width when width is set.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3879">#3879</a> : [webkit] Color button panel had incorrect size on first open.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3839">#3839</a> : Update Scayt plugin to reflect the latest change from SpellChecker.net.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3742">#3742</a> : Fixed wrong dialog layout for dialogs without tab bar in IE RTL mode .</li>
		<li><a href="http://dev.ckeditor.com/ticket/3671">#3671</a> : Fixed body fixing should be applied to the real type under fake elements.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3836">#3836</a> : Fixed remove list in enterMode=BR will merge sibling text to one line.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3949">#3949</a> : Fixed enterKey within pre-formatted text introduce wrong line-break.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3878">#3878</a> : Whenever possible,
		dialogs will not present scrollbars if the content is too big for its standard
		size.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3782">#3782</a> : Remove empty list in table cell result in collapsed cell.</li>
		<li>Updated the following language files:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/4183">#4183</a> : Basque;</li>
			<li><a href="http://dev.ckeditor.com/ticket/3837">#3837</a> : Brazilian Portuguese;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4171">#4171</a> : Catalan;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4115">#4115</a> : Chinese (Simplified);</li>
			<li><a href="http://dev.ckeditor.com/ticket/4179">#4179</a> : Chinese (Traditional);</li>
			<li><a href="http://dev.ckeditor.com/ticket/4102">#4102</a> : Croatian;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4105">#4105</a> : French;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4104">#4104</a> : German;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4116">#4116</a> : Italian;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4091">#4091</a> : Japanese;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4120">#4120</a> : Polish;</li>
			<li><a href="http://dev.ckeditor.com/ticket/3987">#3987</a> : Spanish;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4089">#4089</a> : Ukrainian;</li>
			<li><a href="http://dev.ckeditor.com/ticket/4166">#4166</a> : Vietnamese.</li>
		</ul></li>
		<li><a href="http://dev.ckeditor.com/ticket/3984">#3984</a> : [IE]The pre-formatted style is generating error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3946">#3946</a> : Fixed unable to hide contextmenu.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3956">#3956</a> : Fixed About dialog in Source Mode for IE.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3953">#3953</a> : Fixed keystroke for close Paste dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3951">#3951</a> : Reset size and lock ratio options were not accessible in Image dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3921">#3921</a> : Fixed Container scroll issue on IE7.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3940">#3940</a> : Fixed list operation doesn't stop at table.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3891">#3891</a> : [IE] Fixed 'automatic' font color doesn't work.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3972">#3972</a> : Fixed unable to remove a single empty list in document in Firefox with enterMode=BR.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3973">#3973</a> : Fixed list creation error at the end of document.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3959">#3959</a> : Pasting styled text from word result in content lost.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3793">#3793</a> : Combined images into sprites.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3783">#3783</a> : Fixed indenting command in table cells create collapsed paragraph.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3968">#3968</a> : About dialog layout was broken with IE+Standards+RTL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3991">#3991</a> : In IE quirks, text was not visible in v2 and office2003 skins.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3983">#3983</a> : In IE, we&#39;ll now
			silently ignore wrong toolbar definition settings which have extra commas being
			left around.</li>
		<li>Fixed the following test cases:<ul>
			<li><a href="http://dev.ckeditor.com/ticket/3992">#3992</a> : core/ckeditor2.html</li>
			<li><a href="http://dev.ckeditor.com/ticket/4138">#4138</a> : core/plugins.html</li>
			<li><a href="http://dev.ckeditor.com/ticket/3801">#3801</a> : plugins/htmldataprocessor/htmldataprocessor.html</li>
		</ul></li>
		<li><a href="http://dev.ckeditor.com/ticket/3989">#3989</a> : Host page horizontal scrolling a lot when on having righ-to-left direction.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4001">#4001</a> : Create link around existing image result incorrect.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3988">#3988</a> : Destroy editor on form submit event cause error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3994">#3994</a> : Insert horizontal line at end of document cause error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4074">#4074</a> : Indent error with 'indentClasses' config specified.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4057">#4057</a> : Fixed anchor is lost after switch between editing modes.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3644">#3644</a> : Image dialog was missin radio lock.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4014">#4014</a> : Firefox2 had no dialog button backgrounds.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4018">#4018</a> : Firefox2 had no richcombo text visible.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4035">#4035</a> : [IE6] Paste dialog size was too small.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4049">#4049</a> : Kama skin was too wide with config.width.</li>
		<li>The following released files now doesn't require the _source folder<ul>
			<li><a href="http://dev.ckeditor.com/ticket/4086">#4086</a> : _samples/ui_languages.html</li>
			<li><a href="http://dev.ckeditor.com/ticket/4093">#4093</a> : _tests/core/dom/document.html</li>
			<li><a href="http://dev.ckeditor.com/ticket/4094">#4094</a> : Smiley plugin file</li>
			<li><a href="http://dev.ckeditor.com/ticket/4097">#4097</a> : No undo/redo support for fontColor and backgroundColor buttons.</li>
		</ul></li>
		<li><a href="http://dev.ckeditor.com/ticket/4085">#4085</a> : Paste and Paste from Word dialogs were not well styled in IE+RTL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3982">#3982</a> : Fixed enterKey on empty list item result in weird dom structure.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4101">#4101</a> : Now it is possible to close dialog before gets focus.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4075">#4075</a> : [IE6/7]Fixed apply custom inline style with "class" attribute failed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4087">#4087</a> : [Firefox]Fixed extra blocks created on create list when full document selected.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4097">#4097</a> : No undo/redo support for fontColor and backgroundColor buttons.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4111">#4111</a> : Fixed apply block style after inline style applied on full document error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3622">#3622</a> : Fixed shift enter with selection not deleting highlighted text.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4092">#4092</a> : [IE6] Close button was missing for dialog without multiple tabs.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4003">#4003</a> : Markup on the image dialog was disrupted when removing the border input.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4096">#4096</a> : Editor content area was pushed down in IE RTL quirks.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4112">#4112</a> : [FF] Paste dialog had scrollbars in quirks.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4118">#4118</a> : Dialog dragging was
			occasionally behaving strangely .</li>
		<li><a href="http://dev.ckeditor.com/ticket/4077">#4077</a> : The toolbar combos
			were rendering incorrectly in some languages, like Chinese.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3622">#3622</a> : The toolbar in the v2
			skin was wrapping improperly in some languages.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4119">#4119</a> : Unable to edit image link with image dialog.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4117">#4117</a> : Fixed dialog error when transforming image into button.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4058">#4058</a> : [FF] wysiwyg mode is sometimes not been activated.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4114">#4114</a> : [IE] RTE + IE6/IE7 Quirks = dialog mispositoned.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4123">#4123</a> : Some dialog buttons were broken in IE7 quirks.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4122">#4122</a> : [IE] The image dialog
			was being rendered improperly when loading an image with long URL.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4144">#4144</a> : Fixed the white-spaces at the end of &lt;pre&gt; is incorrectly removed.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4143">#4143</a> : Fixed element id is lost when extracting contents from the range.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4007">#4007</a> : [IE] Source area overflow from editor chrome.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4145">#4145</a> : Fixed the on demand
			(&quot;basic&quot;) loading model of the editor.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4139">#4139</a> : Fixed list plugin regression of [3903].</li>
		<li><a href="http://dev.ckeditor.com/ticket/4147">#4147</a> : Unify style text normalization logic when comparing styles.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4150">#4150</a> : Fixed enlarge list result incorrect at the inner boundary of block.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4164">#4164</a> : Now it is possible to paste text
			in Source mode even if forcePasteAsPlainText = true.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4129">#4129</a> : [FF]Unable to remove list with Ctrl-A.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4172">#4172</a> : [Safari] The trailing
			&lt;br&gt; was not been always added to blank lines ending with &amp;nbsp;.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4178">#4178</a> : It&#39;s now possible to
			copy and paste Flash content among different editor instances.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4193">#4193</a> : Automatic font color produced empty span on Firefox 3.5.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4186">#4186</a> : [FF] Fixed First open float panel cause host page scrollbar blinking.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4227">#4227</a> : Fixed destroy editor instance created on textarea which is not within form cause error.</li>
		<li><a href="http://dev.ckeditor.com/ticket/4240">#4240</a> : Fixed editor name containing hyphen break editor completely.</li>
		<li><a href="http://dev.ckeditor.com/ticket/3828">#3828</a> : Malformed nested list is now corrected by the parser.</li>
	</ul>
	<h3>
		CKEditor 3.0 RC</h3>
	<p>
		Changelog starts at this release.</p>
	<div id="footer">
		<hr />
		<p>
			CKEditor - The text editor for Internet - <a href="http://ckeditor.com/">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2012, <a href="http://cksource.com/">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>

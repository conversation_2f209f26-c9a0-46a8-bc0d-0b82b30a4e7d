/*
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

/* "Source" button label */
.cke_skin_v2 .cke_button_source .cke_label
{
	display: inline;		/* FF2 */
	display: inline-block;
}

/* "Styles" panel size */
.cke_skin_v2 .cke_styles_panel
{
	width: 150px;
	height: 170px;
}

/* "Format" panel size */
.cke_skin_v2 .cke_format_panel
{
	width: 150px;
	height: 170px;
}

/* "Font" panel size */
.cke_skin_v2 .cke_font_panel
{
	width: 150px;
	height: 170px;
}

/* "Font Size" panel size */
.cke_skin_v2 .cke_fontSize_panel
{
	height: 170px;
}

/* "Font Size" combo width */
.cke_skin_v2 .cke_fontSize .cke_text
{
	width: 20px;
}

/* "Font Size" combo width (IE Quirks) */
.cke_skin_v2 .cke_browser_iequirks .cke_fontSize .cke_text
{
	width: 32px;
}

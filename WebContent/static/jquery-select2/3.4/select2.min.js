(function(a){if(typeof a.fn.each2=="undefined"){a.extend(a.fn,{each2:function(f){var d=a([0]),e=-1,b=this.length;while(++e<b&&(d.context=d[0]=this[e])&&f.call(d[0],e,d)!==false){}return this}})}})(jQuery);(function(D,m){if(window.Select2!==m){return}var K,N,x,c,a,p,o={x:0,y:0},v,w,K={TAB:9,ENTER:13,ESC:27,SPACE:32,LEFT:37,UP:38,RIGHT:39,DOWN:40,SHIFT:16,CTRL:17,ALT:18,PAGE_UP:33,PAGE_DOWN:34,HOME:36,END:35,BACKSPACE:8,DELETE:46,isArrow:function(O){O=O.which?O.which:O;switch(O){case K.LEFT:case K.RIGHT:case K.UP:case K.DOWN:return true}return false},isControl:function(P){var O=P.which;switch(O){case K.SHIFT:case K.CTRL:case K.ALT:return true}if(P.metaKey){return true}return false},isFunctionKey:function(O){O=O.which?O.which:O;return O>=112&&O<=123}},B="<div class='select2-measure-scrollbar'></div>",d={"\u24B6":"A","\uFF21":"A","\u00C0":"A","\u00C1":"A","\u00C2":"A","\u1EA6":"A","\u1EA4":"A","\u1EAA":"A","\u1EA8":"A","\u00C3":"A","\u0100":"A","\u0102":"A","\u1EB0":"A","\u1EAE":"A","\u1EB4":"A","\u1EB2":"A","\u0226":"A","\u01E0":"A","\u00C4":"A","\u01DE":"A","\u1EA2":"A","\u00C5":"A","\u01FA":"A","\u01CD":"A","\u0200":"A","\u0202":"A","\u1EA0":"A","\u1EAC":"A","\u1EB6":"A","\u1E00":"A","\u0104":"A","\u023A":"A","\u2C6F":"A","\uA732":"AA","\u00C6":"AE","\u01FC":"AE","\u01E2":"AE","\uA734":"AO","\uA736":"AU","\uA738":"AV","\uA73A":"AV","\uA73C":"AY","\u24B7":"B","\uFF22":"B","\u1E02":"B","\u1E04":"B","\u1E06":"B","\u0243":"B","\u0182":"B","\u0181":"B","\u24B8":"C","\uFF23":"C","\u0106":"C","\u0108":"C","\u010A":"C","\u010C":"C","\u00C7":"C","\u1E08":"C","\u0187":"C","\u023B":"C","\uA73E":"C","\u24B9":"D","\uFF24":"D","\u1E0A":"D","\u010E":"D","\u1E0C":"D","\u1E10":"D","\u1E12":"D","\u1E0E":"D","\u0110":"D","\u018B":"D","\u018A":"D","\u0189":"D","\uA779":"D","\u01F1":"DZ","\u01C4":"DZ","\u01F2":"Dz","\u01C5":"Dz","\u24BA":"E","\uFF25":"E","\u00C8":"E","\u00C9":"E","\u00CA":"E","\u1EC0":"E","\u1EBE":"E","\u1EC4":"E","\u1EC2":"E","\u1EBC":"E","\u0112":"E","\u1E14":"E","\u1E16":"E","\u0114":"E","\u0116":"E","\u00CB":"E","\u1EBA":"E","\u011A":"E","\u0204":"E","\u0206":"E","\u1EB8":"E","\u1EC6":"E","\u0228":"E","\u1E1C":"E","\u0118":"E","\u1E18":"E","\u1E1A":"E","\u0190":"E","\u018E":"E","\u24BB":"F","\uFF26":"F","\u1E1E":"F","\u0191":"F","\uA77B":"F","\u24BC":"G","\uFF27":"G","\u01F4":"G","\u011C":"G","\u1E20":"G","\u011E":"G","\u0120":"G","\u01E6":"G","\u0122":"G","\u01E4":"G","\u0193":"G","\uA7A0":"G","\uA77D":"G","\uA77E":"G","\u24BD":"H","\uFF28":"H","\u0124":"H","\u1E22":"H","\u1E26":"H","\u021E":"H","\u1E24":"H","\u1E28":"H","\u1E2A":"H","\u0126":"H","\u2C67":"H","\u2C75":"H","\uA78D":"H","\u24BE":"I","\uFF29":"I","\u00CC":"I","\u00CD":"I","\u00CE":"I","\u0128":"I","\u012A":"I","\u012C":"I","\u0130":"I","\u00CF":"I","\u1E2E":"I","\u1EC8":"I","\u01CF":"I","\u0208":"I","\u020A":"I","\u1ECA":"I","\u012E":"I","\u1E2C":"I","\u0197":"I","\u24BF":"J","\uFF2A":"J","\u0134":"J","\u0248":"J","\u24C0":"K","\uFF2B":"K","\u1E30":"K","\u01E8":"K","\u1E32":"K","\u0136":"K","\u1E34":"K","\u0198":"K","\u2C69":"K","\uA740":"K","\uA742":"K","\uA744":"K","\uA7A2":"K","\u24C1":"L","\uFF2C":"L","\u013F":"L","\u0139":"L","\u013D":"L","\u1E36":"L","\u1E38":"L","\u013B":"L","\u1E3C":"L","\u1E3A":"L","\u0141":"L","\u023D":"L","\u2C62":"L","\u2C60":"L","\uA748":"L","\uA746":"L","\uA780":"L","\u01C7":"LJ","\u01C8":"Lj","\u24C2":"M","\uFF2D":"M","\u1E3E":"M","\u1E40":"M","\u1E42":"M","\u2C6E":"M","\u019C":"M","\u24C3":"N","\uFF2E":"N","\u01F8":"N","\u0143":"N","\u00D1":"N","\u1E44":"N","\u0147":"N","\u1E46":"N","\u0145":"N","\u1E4A":"N","\u1E48":"N","\u0220":"N","\u019D":"N","\uA790":"N","\uA7A4":"N","\u01CA":"NJ","\u01CB":"Nj","\u24C4":"O","\uFF2F":"O","\u00D2":"O","\u00D3":"O","\u00D4":"O","\u1ED2":"O","\u1ED0":"O","\u1ED6":"O","\u1ED4":"O","\u00D5":"O","\u1E4C":"O","\u022C":"O","\u1E4E":"O","\u014C":"O","\u1E50":"O","\u1E52":"O","\u014E":"O","\u022E":"O","\u0230":"O","\u00D6":"O","\u022A":"O","\u1ECE":"O","\u0150":"O","\u01D1":"O","\u020C":"O","\u020E":"O","\u01A0":"O","\u1EDC":"O","\u1EDA":"O","\u1EE0":"O","\u1EDE":"O","\u1EE2":"O","\u1ECC":"O","\u1ED8":"O","\u01EA":"O","\u01EC":"O","\u00D8":"O","\u01FE":"O","\u0186":"O","\u019F":"O","\uA74A":"O","\uA74C":"O","\u01A2":"OI","\uA74E":"OO","\u0222":"OU","\u24C5":"P","\uFF30":"P","\u1E54":"P","\u1E56":"P","\u01A4":"P","\u2C63":"P","\uA750":"P","\uA752":"P","\uA754":"P","\u24C6":"Q","\uFF31":"Q","\uA756":"Q","\uA758":"Q","\u024A":"Q","\u24C7":"R","\uFF32":"R","\u0154":"R","\u1E58":"R","\u0158":"R","\u0210":"R","\u0212":"R","\u1E5A":"R","\u1E5C":"R","\u0156":"R","\u1E5E":"R","\u024C":"R","\u2C64":"R","\uA75A":"R","\uA7A6":"R","\uA782":"R","\u24C8":"S","\uFF33":"S","\u1E9E":"S","\u015A":"S","\u1E64":"S","\u015C":"S","\u1E60":"S","\u0160":"S","\u1E66":"S","\u1E62":"S","\u1E68":"S","\u0218":"S","\u015E":"S","\u2C7E":"S","\uA7A8":"S","\uA784":"S","\u24C9":"T","\uFF34":"T","\u1E6A":"T","\u0164":"T","\u1E6C":"T","\u021A":"T","\u0162":"T","\u1E70":"T","\u1E6E":"T","\u0166":"T","\u01AC":"T","\u01AE":"T","\u023E":"T","\uA786":"T","\uA728":"TZ","\u24CA":"U","\uFF35":"U","\u00D9":"U","\u00DA":"U","\u00DB":"U","\u0168":"U","\u1E78":"U","\u016A":"U","\u1E7A":"U","\u016C":"U","\u00DC":"U","\u01DB":"U","\u01D7":"U","\u01D5":"U","\u01D9":"U","\u1EE6":"U","\u016E":"U","\u0170":"U","\u01D3":"U","\u0214":"U","\u0216":"U","\u01AF":"U","\u1EEA":"U","\u1EE8":"U","\u1EEE":"U","\u1EEC":"U","\u1EF0":"U","\u1EE4":"U","\u1E72":"U","\u0172":"U","\u1E76":"U","\u1E74":"U","\u0244":"U","\u24CB":"V","\uFF36":"V","\u1E7C":"V","\u1E7E":"V","\u01B2":"V","\uA75E":"V","\u0245":"V","\uA760":"VY","\u24CC":"W","\uFF37":"W","\u1E80":"W","\u1E82":"W","\u0174":"W","\u1E86":"W","\u1E84":"W","\u1E88":"W","\u2C72":"W","\u24CD":"X","\uFF38":"X","\u1E8A":"X","\u1E8C":"X","\u24CE":"Y","\uFF39":"Y","\u1EF2":"Y","\u00DD":"Y","\u0176":"Y","\u1EF8":"Y","\u0232":"Y","\u1E8E":"Y","\u0178":"Y","\u1EF6":"Y","\u1EF4":"Y","\u01B3":"Y","\u024E":"Y","\u1EFE":"Y","\u24CF":"Z","\uFF3A":"Z","\u0179":"Z","\u1E90":"Z","\u017B":"Z","\u017D":"Z","\u1E92":"Z","\u1E94":"Z","\u01B5":"Z","\u0224":"Z","\u2C7F":"Z","\u2C6B":"Z","\uA762":"Z","\u24D0":"a","\uFF41":"a","\u1E9A":"a","\u00E0":"a","\u00E1":"a","\u00E2":"a","\u1EA7":"a","\u1EA5":"a","\u1EAB":"a","\u1EA9":"a","\u00E3":"a","\u0101":"a","\u0103":"a","\u1EB1":"a","\u1EAF":"a","\u1EB5":"a","\u1EB3":"a","\u0227":"a","\u01E1":"a","\u00E4":"a","\u01DF":"a","\u1EA3":"a","\u00E5":"a","\u01FB":"a","\u01CE":"a","\u0201":"a","\u0203":"a","\u1EA1":"a","\u1EAD":"a","\u1EB7":"a","\u1E01":"a","\u0105":"a","\u2C65":"a","\u0250":"a","\uA733":"aa","\u00E6":"ae","\u01FD":"ae","\u01E3":"ae","\uA735":"ao","\uA737":"au","\uA739":"av","\uA73B":"av","\uA73D":"ay","\u24D1":"b","\uFF42":"b","\u1E03":"b","\u1E05":"b","\u1E07":"b","\u0180":"b","\u0183":"b","\u0253":"b","\u24D2":"c","\uFF43":"c","\u0107":"c","\u0109":"c","\u010B":"c","\u010D":"c","\u00E7":"c","\u1E09":"c","\u0188":"c","\u023C":"c","\uA73F":"c","\u2184":"c","\u24D3":"d","\uFF44":"d","\u1E0B":"d","\u010F":"d","\u1E0D":"d","\u1E11":"d","\u1E13":"d","\u1E0F":"d","\u0111":"d","\u018C":"d","\u0256":"d","\u0257":"d","\uA77A":"d","\u01F3":"dz","\u01C6":"dz","\u24D4":"e","\uFF45":"e","\u00E8":"e","\u00E9":"e","\u00EA":"e","\u1EC1":"e","\u1EBF":"e","\u1EC5":"e","\u1EC3":"e","\u1EBD":"e","\u0113":"e","\u1E15":"e","\u1E17":"e","\u0115":"e","\u0117":"e","\u00EB":"e","\u1EBB":"e","\u011B":"e","\u0205":"e","\u0207":"e","\u1EB9":"e","\u1EC7":"e","\u0229":"e","\u1E1D":"e","\u0119":"e","\u1E19":"e","\u1E1B":"e","\u0247":"e","\u025B":"e","\u01DD":"e","\u24D5":"f","\uFF46":"f","\u1E1F":"f","\u0192":"f","\uA77C":"f","\u24D6":"g","\uFF47":"g","\u01F5":"g","\u011D":"g","\u1E21":"g","\u011F":"g","\u0121":"g","\u01E7":"g","\u0123":"g","\u01E5":"g","\u0260":"g","\uA7A1":"g","\u1D79":"g","\uA77F":"g","\u24D7":"h","\uFF48":"h","\u0125":"h","\u1E23":"h","\u1E27":"h","\u021F":"h","\u1E25":"h","\u1E29":"h","\u1E2B":"h","\u1E96":"h","\u0127":"h","\u2C68":"h","\u2C76":"h","\u0265":"h","\u0195":"hv","\u24D8":"i","\uFF49":"i","\u00EC":"i","\u00ED":"i","\u00EE":"i","\u0129":"i","\u012B":"i","\u012D":"i","\u00EF":"i","\u1E2F":"i","\u1EC9":"i","\u01D0":"i","\u0209":"i","\u020B":"i","\u1ECB":"i","\u012F":"i","\u1E2D":"i","\u0268":"i","\u0131":"i","\u24D9":"j","\uFF4A":"j","\u0135":"j","\u01F0":"j","\u0249":"j","\u24DA":"k","\uFF4B":"k","\u1E31":"k","\u01E9":"k","\u1E33":"k","\u0137":"k","\u1E35":"k","\u0199":"k","\u2C6A":"k","\uA741":"k","\uA743":"k","\uA745":"k","\uA7A3":"k","\u24DB":"l","\uFF4C":"l","\u0140":"l","\u013A":"l","\u013E":"l","\u1E37":"l","\u1E39":"l","\u013C":"l","\u1E3D":"l","\u1E3B":"l","\u017F":"l","\u0142":"l","\u019A":"l","\u026B":"l","\u2C61":"l","\uA749":"l","\uA781":"l","\uA747":"l","\u01C9":"lj","\u24DC":"m","\uFF4D":"m","\u1E3F":"m","\u1E41":"m","\u1E43":"m","\u0271":"m","\u026F":"m","\u24DD":"n","\uFF4E":"n","\u01F9":"n","\u0144":"n","\u00F1":"n","\u1E45":"n","\u0148":"n","\u1E47":"n","\u0146":"n","\u1E4B":"n","\u1E49":"n","\u019E":"n","\u0272":"n","\u0149":"n","\uA791":"n","\uA7A5":"n","\u01CC":"nj","\u24DE":"o","\uFF4F":"o","\u00F2":"o","\u00F3":"o","\u00F4":"o","\u1ED3":"o","\u1ED1":"o","\u1ED7":"o","\u1ED5":"o","\u00F5":"o","\u1E4D":"o","\u022D":"o","\u1E4F":"o","\u014D":"o","\u1E51":"o","\u1E53":"o","\u014F":"o","\u022F":"o","\u0231":"o","\u00F6":"o","\u022B":"o","\u1ECF":"o","\u0151":"o","\u01D2":"o","\u020D":"o","\u020F":"o","\u01A1":"o","\u1EDD":"o","\u1EDB":"o","\u1EE1":"o","\u1EDF":"o","\u1EE3":"o","\u1ECD":"o","\u1ED9":"o","\u01EB":"o","\u01ED":"o","\u00F8":"o","\u01FF":"o","\u0254":"o","\uA74B":"o","\uA74D":"o","\u0275":"o","\u01A3":"oi","\u0223":"ou","\uA74F":"oo","\u24DF":"p","\uFF50":"p","\u1E55":"p","\u1E57":"p","\u01A5":"p","\u1D7D":"p","\uA751":"p","\uA753":"p","\uA755":"p","\u24E0":"q","\uFF51":"q","\u024B":"q","\uA757":"q","\uA759":"q","\u24E1":"r","\uFF52":"r","\u0155":"r","\u1E59":"r","\u0159":"r","\u0211":"r","\u0213":"r","\u1E5B":"r","\u1E5D":"r","\u0157":"r","\u1E5F":"r","\u024D":"r","\u027D":"r","\uA75B":"r","\uA7A7":"r","\uA783":"r","\u24E2":"s","\uFF53":"s","\u00DF":"s","\u015B":"s","\u1E65":"s","\u015D":"s","\u1E61":"s","\u0161":"s","\u1E67":"s","\u1E63":"s","\u1E69":"s","\u0219":"s","\u015F":"s","\u023F":"s","\uA7A9":"s","\uA785":"s","\u1E9B":"s","\u24E3":"t","\uFF54":"t","\u1E6B":"t","\u1E97":"t","\u0165":"t","\u1E6D":"t","\u021B":"t","\u0163":"t","\u1E71":"t","\u1E6F":"t","\u0167":"t","\u01AD":"t","\u0288":"t","\u2C66":"t","\uA787":"t","\uA729":"tz","\u24E4":"u","\uFF55":"u","\u00F9":"u","\u00FA":"u","\u00FB":"u","\u0169":"u","\u1E79":"u","\u016B":"u","\u1E7B":"u","\u016D":"u","\u00FC":"u","\u01DC":"u","\u01D8":"u","\u01D6":"u","\u01DA":"u","\u1EE7":"u","\u016F":"u","\u0171":"u","\u01D4":"u","\u0215":"u","\u0217":"u","\u01B0":"u","\u1EEB":"u","\u1EE9":"u","\u1EEF":"u","\u1EED":"u","\u1EF1":"u","\u1EE5":"u","\u1E73":"u","\u0173":"u","\u1E77":"u","\u1E75":"u","\u0289":"u","\u24E5":"v","\uFF56":"v","\u1E7D":"v","\u1E7F":"v","\u028B":"v","\uA75F":"v","\u028C":"v","\uA761":"vy","\u24E6":"w","\uFF57":"w","\u1E81":"w","\u1E83":"w","\u0175":"w","\u1E87":"w","\u1E85":"w","\u1E98":"w","\u1E89":"w","\u2C73":"w","\u24E7":"x","\uFF58":"x","\u1E8B":"x","\u1E8D":"x","\u24E8":"y","\uFF59":"y","\u1EF3":"y","\u00FD":"y","\u0177":"y","\u1EF9":"y","\u0233":"y","\u1E8F":"y","\u00FF":"y","\u1EF7":"y","\u1E99":"y","\u1EF5":"y","\u01B4":"y","\u024F":"y","\u1EFF":"y","\u24E9":"z","\uFF5A":"z","\u017A":"z","\u1E91":"z","\u017C":"z","\u017E":"z","\u1E93":"z","\u1E95":"z","\u01B6":"z","\u0225":"z","\u0240":"z","\u2C6C":"z","\uA763":"z"};
v=D(document);a=(function(){var O=1;return function(){return O++}}());function e(R){var P,Q,O,S;if(!R||R.length<1){return R}P="";for(Q=0,O=R.length;Q<O;Q++){S=R.charAt(Q);P+=d[S]||S}return P}function q(Q,R){var P=0,O=R.length;for(;P<O;P=P+1){if(t(Q,R[P])){return P}}return -1}function M(){var O=D(B);O.appendTo("body");var P={width:O.width()-O[0].clientWidth,height:O.height()-O[0].clientHeight};O.remove();return P}function t(P,O){if(P===O){return true}if(P===m||O===m){return false}if(P===null||O===null){return false}if(P.constructor===String){return P+""===O+""}if(O.constructor===String){return O+""===P+""}return false}function i(P,R){var S,Q,O;if(P===null||P.length<1){return[]}S=P.split(R);for(Q=0,O=S.length;Q<O;Q=Q+1){S[Q]=D.trim(S[Q])}return S}function h(O){return O.outerWidth(false)-O.width()}function F(P){var O="keyup-change-value";P.on("keydown",function(){if(D.data(P,O)===m){D.data(P,O,P.val())}});P.on("keyup",function(){var Q=D.data(P,O);if(Q!==m&&P.val()!==Q){D.removeData(P,O);P.trigger("keyup-change")}})}v.on("mousemove",function(O){o.x=O.pageX;o.y=O.pageY});function J(O){O.on("mousemove",function(Q){var P=o;if(P===m||P.x!==Q.pageX||P.y!==Q.pageY){D(Q.target).trigger("mousemove-filtered",Q)}})}function k(R,P,O){O=O||m;var Q;return function(){var S=arguments;window.clearTimeout(Q);Q=window.setTimeout(function(){P.apply(O,S)},R)}}function s(Q){var O=false,P;return function(){if(O===false){P=Q();O=true}return P}}function l(O,Q){var P=k(O,function(R){Q.trigger("scroll-debounced",R)});Q.on("scroll",function(R){if(q(R.target,Q.get())>=0){P(R)}})}function I(O){if(O[0]===document.activeElement){return}window.setTimeout(function(){var Q=O[0],R=O.val().length,P;O.focus();if(O.is(":visible")&&Q===document.activeElement){if(Q.setSelectionRange){Q.setSelectionRange(R,R)}else{if(Q.createTextRange){P=Q.createTextRange();P.collapse(false);P.select()}}}},0)}function f(O){O=D(O)[0];var R=0;var P=0;if("selectionStart" in O){R=O.selectionStart;P=O.selectionEnd-R}else{if("selection" in document){O.focus();var Q=document.selection.createRange();
P=document.selection.createRange().text.length;Q.moveStart("character",-O.value.length);R=Q.text.length-P}}return{offset:R,length:P}}function A(O){O.preventDefault();O.stopPropagation()}function b(O){O.preventDefault();O.stopImmediatePropagation()}function n(P){if(!p){var O=P[0].currentStyle||window.getComputedStyle(P[0],null);p=D(document.createElement("div")).css({position:"absolute",left:"-10000px",top:"-10000px",display:"none",fontSize:O.fontSize,fontFamily:O.fontFamily,fontStyle:O.fontStyle,fontWeight:O.fontWeight,letterSpacing:O.letterSpacing,textTransform:O.textTransform,whiteSpace:"nowrap"});p.attr("class","select2-sizer");D("body").append(p)}p.text(P.val());return p.width()}function j(P,T,O){var R,S=[],Q;R=P.attr("class");if(R){R=""+R;D(R.split(" ")).each2(function(){if(this.indexOf("select2-")===0){S.push(this)}})}R=T.attr("class");if(R){R=""+R;D(R.split(" ")).each2(function(){if(this.indexOf("select2-")!==0){Q=O(this);if(Q){S.push(Q)}}})}P.attr("class",S.join(" "))}function u(T,S,Q,O){var R=e(T.toUpperCase()).indexOf(e(S.toUpperCase())),P=S.length;if(R<0){Q.push(O(T));return}Q.push(O(T.substring(0,R)));Q.push("<span class='select2-match'>");Q.push(O(T.substring(R,R+P)));Q.push("</span>");Q.push(O(T.substring(R+P,T.length)))}function G(O){var P={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return String(O).replace(/[&<>"'\/\\]/g,function(Q){return P[Q]})}function E(P){var S,Q=null,T=P.quietMillis||100,R=P.url,O=this;return function(U){window.clearTimeout(S);S=window.setTimeout(function(){var X=P.data,W=R,Z=P.transport||D.fn.select2.ajaxDefaults.transport,V={type:P.type||"GET",cache:P.cache||false,jsonpCallback:P.jsonpCallback||m,dataType:P.dataType||"json"},Y=D.extend({},D.fn.select2.ajaxDefaults.params,V);X=X?X.call(O,U.term,U.page,U.context):null;W=(typeof W==="function")?W.call(O,U.term,U.page,U.context):W;if(Q){Q.abort()}if(P.params){if(D.isFunction(P.params)){D.extend(Y,P.params.call(O))}else{D.extend(Y,P.params)}}D.extend(Y,{url:W,dataType:P.dataType,data:X,success:function(ab){var aa=P.results(ab,U.page);
U.callback(aa)}});Q=Z.call(O,Y)},T)}}function H(P){var S=P,R,Q,T=function(U){return""+U.text};if(D.isArray(S)){Q=S;S={results:Q}}if(D.isFunction(S)===false){Q=S;S=function(){return Q}}var O=S();if(O.text){T=O.text;if(!D.isFunction(T)){R=O.text;T=function(U){return U[R]}}}return function(W){var V=W.term,U={results:[]},X;if(V===""){W.callback(S());return}X=function(Z,ab){var aa,Y;Z=Z[0];if(Z.children){aa={};for(Y in Z){if(Z.hasOwnProperty(Y)){aa[Y]=Z[Y]}}aa.children=[];D(Z.children).each2(function(ac,ad){X(ad,aa.children)});if(aa.children.length||W.matcher(V,T(aa),Z)){ab.push(aa)}}else{if(W.matcher(V,T(Z),Z)){ab.push(Z)}}};D(S().results).each2(function(Z,Y){X(Y,U.results)});W.callback(U)}}function z(P){var O=D.isFunction(P);return function(S){var R=S.term,Q={results:[]};D(O?P():P).each(function(){var T=this.text!==m,U=T?this.text:this;if(R===""||S.matcher(R,U)){Q.results.push(T?this:{id:this,text:this})}});S.callback(Q)}}function y(O,P){if(D.isFunction(O)){return true}if(!O){return false}throw new Error(P+" must be a function or a falsy value")}function C(O){return D.isFunction(O)?O():O}function r(O){var P=0;D.each(O,function(Q,R){if(R.children){P+=r(R.children)}else{P++}});return P}function g(W,X,U,O){var P=W,Y=false,R,V,S,Q,T;if(!O.createSearchChoice||!O.tokenSeparators||O.tokenSeparators.length<1){return m}while(true){V=-1;for(S=0,Q=O.tokenSeparators.length;S<Q;S++){T=O.tokenSeparators[S];V=W.indexOf(T);if(V>=0){break}}if(V<0){break}R=W.substring(0,V);W=W.substring(V+T.length);if(R.length>0){R=O.createSearchChoice.call(this,R,X);if(R!==m&&R!==null&&O.id(R)!==m&&O.id(R)!==null){Y=false;for(S=0,Q=X.length;S<Q;S++){if(t(O.id(R),O.id(X[S]))){Y=true;break}}if(!Y){U(R)}}}}if(P!==W){return W}}function L(O,P){var Q=function(){};Q.prototype=new O;Q.prototype.constructor=Q;Q.prototype.parent=O.prototype;Q.prototype=D.extend(Q.prototype,P);return Q}N=L(Object,{bind:function(P){var O=this;return function(){P.apply(O,arguments)}},init:function(S){var Q,P,T=".select2-results";this.opts=S=this.prepareOpts(S);this.id=S.id;if(S.element.data("select2")!==m&&S.element.data("select2")!==null){S.element.data("select2").destroy()
}this.container=this.createContainer();this.containerId="s2id_"+(S.element.attr("id")||"autogen"+a());this.containerSelector="#"+this.containerId.replace(/([;&,\.\+\*\~':"\!\^#$%@\[\]\(\)=>\|])/g,"\\$1");this.container.attr("id",this.containerId);this.body=s(function(){return S.element.closest("body")});j(this.container,this.opts.element,this.opts.adaptContainerCssClass);this.container.attr("style",S.element.attr("style"));this.container.css(C(S.containerCss));this.container.addClass(C(S.containerCssClass));this.elementTabIndex=this.opts.element.attr("tabindex");this.opts.element.data("select2",this).attr("tabindex","-1").before(this.container).on("click.select2",A);this.container.data("select2",this);this.dropdown=this.container.find(".select2-drop");j(this.dropdown,this.opts.element,this.opts.adaptDropdownCssClass);this.dropdown.addClass(C(S.dropdownCssClass));this.dropdown.data("select2",this);this.dropdown.on("click",A);this.results=Q=this.container.find(T);this.search=P=this.container.find("input.select2-input");this.queryCount=0;this.resultsPage=0;this.context=null;this.initContainer();this.container.on("click",A);J(this.results);this.dropdown.on("mousemove-filtered touchstart touchmove touchend",T,this.bind(this.highlightUnderEvent));l(80,this.results);this.dropdown.on("scroll-debounced",T,this.bind(this.loadMoreIfNeeded));D(this.container).on("change",".select2-input",function(U){U.stopPropagation()});D(this.dropdown).on("change",".select2-input",function(U){U.stopPropagation()});if(D.fn.mousewheel){Q.mousewheel(function(X,Y,V,U){var W=Q.scrollTop();if(U>0&&W-U<=0){Q.scrollTop(0);A(X)}else{if(U<0&&Q.get(0).scrollHeight-Q.scrollTop()+U<=Q.height()){Q.scrollTop(Q.get(0).scrollHeight-Q.height());A(X)}}})}F(P);P.on("keyup-change input paste",this.bind(this.updateResults));P.on("focus",function(){P.addClass("select2-focused")});P.on("blur",function(){P.removeClass("select2-focused")});this.dropdown.on("mouseup",T,this.bind(function(U){if(D(U.target).closest(".select2-result-selectable").length>0){this.highlightUnderEvent(U);
this.selectHighlighted(U)}}));this.dropdown.on("click mouseup mousedown",function(U){U.stopPropagation()});if(D.isFunction(this.opts.initSelection)){this.initSelection();this.monitorSource()}if(S.maximumInputLength!==null){this.search.attr("maxlength",S.maximumInputLength)}var R=S.element.prop("disabled");if(R===m){R=false}this.enable(!R);var O=S.element.prop("readonly");if(O===m){O=false}this.readonly(O);w=w||M();this.autofocus=S.element.prop("autofocus");S.element.prop("autofocus",false);if(this.autofocus){this.focus()}this.nextSearchTerm=m},destroy:function(){var P=this.opts.element,O=P.data("select2");this.close();if(this.propertyObserver){delete this.propertyObserver;this.propertyObserver=null}if(O!==m){O.container.remove();O.dropdown.remove();P.removeClass("select2-offscreen").removeData("select2").off(".select2").prop("autofocus",this.autofocus||false);if(this.elementTabIndex){P.attr({tabindex:this.elementTabIndex})}else{P.removeAttr("tabindex")}P.show()}},optionToData:function(O){if(O.is("option")){return{id:O.prop("value"),text:O.text(),element:O.get(),css:O.attr("class"),disabled:O.prop("disabled"),locked:t(O.attr("locked"),"locked")||t(O.data("locked"),true)}}else{if(O.is("optgroup")){return{text:O.attr("label"),children:[],element:O.get(),css:O.attr("class")}}}},prepareOpts:function(T){var R,P,O,S,Q=this;R=T.element;if(R.get(0).tagName.toLowerCase()==="select"){this.select=P=T.element}if(P){D.each(["id","multiple","ajax","query","createSearchChoice","initSelection","data","tags"],function(){if(this in T){throw new Error("Option '"+this+"' is not allowed for Select2 when attached to a <select> element.")}})}T=D.extend({},{populateResults:function(U,V,X){var W,Y=this.opts.id;W=function(af,Z,ae){var ag,ab,al,ai,ac,ak,aa,aj,ah,ad;af=T.sortResults(af,Z,X);for(ag=0,ab=af.length;ag<ab;ag=ag+1){al=af[ag];ac=(al.disabled===true);ai=(!ac)&&(Y(al)!==m);ak=al.children&&al.children.length>0;aa=D("<li></li>");aa.addClass("select2-results-dept-"+ae);aa.addClass("select2-result");aa.addClass(ai?"select2-result-selectable":"select2-result-unselectable");
if(ac){aa.addClass("select2-disabled")}if(ak){aa.addClass("select2-result-with-children")}aa.addClass(Q.opts.formatResultCssClass(al));aj=D(document.createElement("div"));aj.addClass("select2-result-label");ad=T.formatResult(al,aj,X,Q.opts.escapeMarkup);if(ad!==m){aj.html(ad)}aa.append(aj);if(ak){ah=D("<ul></ul>");ah.addClass("select2-result-sub");W(al.children,ah,ae+1);aa.append(ah)}aa.data("select2-data",al);Z.append(aa)}};W(V,U,0)}},D.fn.select2.defaults,T);if(typeof(T.id)!=="function"){O=T.id;T.id=function(U){return U[O]}}if(D.isArray(T.element.data("select2Tags"))){if("tags" in T){throw"tags specified as both an attribute 'data-select2-tags' and in options of Select2 "+T.element.attr("id")}T.tags=T.element.data("select2Tags")}if(P){T.query=this.bind(function(Y){var X={results:[],more:false},W=Y.term,V,U,Z;Z=function(aa,ac){var ab;if(aa.is("option")){if(Y.matcher(W,aa.text(),aa)){ac.push(Q.optionToData(aa))}}else{if(aa.is("optgroup")){ab=Q.optionToData(aa);aa.children().each2(function(ad,ae){Z(ae,ab.children)});if(ab.children.length>0){ac.push(ab)}}}};V=R.children();if(this.getPlaceholder()!==m&&V.length>0){U=this.getPlaceholderOption();if(U){V=V.not(U)}}V.each2(function(aa,ab){Z(ab,X.results)});Y.callback(X)});T.id=function(U){return U.id};T.formatResultCssClass=function(U){return U.css}}else{if(!("query" in T)){if("ajax" in T){S=T.element.data("ajax-url");if(S&&S.length>0){T.ajax.url=S}T.query=E.call(T.element,T.ajax)}else{if("data" in T){T.query=H(T.data)}else{if("tags" in T){T.query=z(T.tags);if(T.createSearchChoice===m){T.createSearchChoice=function(U){return{id:D.trim(U),text:D.trim(U)}}}if(T.initSelection===m){T.initSelection=function(U,W){var V=[];D(i(U.val(),T.separator)).each(function(){var Y={id:this,text:this},X=T.tags;if(D.isFunction(X)){X=X()}D(X).each(function(){if(t(this.id,Y.id)){Y=this;return false}});V.push(Y)});W(V)}}}}}}}if(typeof(T.query)!=="function"){throw"query function not defined for Select2 "+T.element.attr("id")}return T},monitorSource:function(){var P=this.opts.element,Q,O;P.on("change.select2",this.bind(function(R){if(this.opts.element.data("select2-change-triggered")!==true){this.initSelection()
}}));Q=this.bind(function(){var S=P.prop("disabled");if(S===m){S=false}this.enable(!S);var R=P.prop("readonly");if(R===m){R=false}this.readonly(R);j(this.container,this.opts.element,this.opts.adaptContainerCssClass);this.container.addClass(C(this.opts.containerCssClass));j(this.dropdown,this.opts.element,this.opts.adaptDropdownCssClass);this.dropdown.addClass(C(this.opts.dropdownCssClass))});P.on("propertychange.select2",Q);if(this.mutationCallback===m){this.mutationCallback=function(R){R.forEach(Q)}}O=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;if(O!==m){if(this.propertyObserver){delete this.propertyObserver;this.propertyObserver=null}this.propertyObserver=new O(this.mutationCallback);this.propertyObserver.observe(P.get(0),{attributes:true,subtree:false})}},triggerSelect:function(P){var O=D.Event("select2-selecting",{val:this.id(P),object:P});this.opts.element.trigger(O);return !O.isDefaultPrevented()},triggerChange:function(O){O=O||{};O=D.extend({},O,{type:"change",val:this.val()});this.opts.element.data("select2-change-triggered",true);this.opts.element.trigger(O);this.opts.element.data("select2-change-triggered",false);this.opts.element.click();if(this.opts.blurOnChange){this.opts.element.blur()}},isInterfaceEnabled:function(){return this.enabledInterface===true},enableInterface:function(){var O=this._enabled&&!this._readonly,P=!O;if(O===this.enabledInterface){return false}this.container.toggleClass("select2-container-disabled",P);this.close();this.enabledInterface=O;return true},enable:function(O){if(O===m){O=true}if(this._enabled===O){return}this._enabled=O;this.opts.element.prop("disabled",!O);this.enableInterface()},disable:function(){this.enable(false)},readonly:function(O){if(O===m){O=false}if(this._readonly===O){return false}this._readonly=O;this.opts.element.prop("readonly",O);this.enableInterface();return true},opened:function(){return this.container.hasClass("select2-dropdown-open")},positionDropdown:function(){var Q=this.dropdown,T=this.container.offset(),ac=this.container.outerHeight(false),ad=this.container.outerWidth(false),Y=Q.outerHeight(false),aa=D(window),ah=aa.width(),W=aa.height(),P=aa.scrollLeft()+ah,ag=aa.scrollTop()+W,R=T.top+ac,ae=T.left,O=R+Y<=ag,V=(T.top-Y)>=this.body().scrollTop(),Z=Q.outerWidth(false),aj=ae+Z<=P,ai=Q.hasClass("select2-drop-above"),U,af,S,X,ab;
if(ai){af=true;if(!V&&O){S=true;af=false}}else{af=false;if(!O&&V){S=true;af=true}}if(S){Q.hide();T=this.container.offset();ac=this.container.outerHeight(false);ad=this.container.outerWidth(false);Y=Q.outerHeight(false);P=aa.scrollLeft()+ah;ag=aa.scrollTop()+W;R=T.top+ac;ae=T.left;Z=Q.outerWidth(false);aj=ae+Z<=P;Q.show()}if(this.opts.dropdownAutoWidth){ab=D(".select2-results",Q)[0];Q.addClass("select2-drop-auto-width");Q.css("width","");Z=Q.outerWidth(false)+(ab.scrollHeight===ab.clientHeight?0:w.width);Z>ad?ad=Z:Z=ad;aj=ae+Z<=P}else{this.container.removeClass("select2-drop-auto-width")}if(this.body().css("position")!=="static"){U=this.body().offset();R-=U.top;ae-=U.left}if(!aj){ae=T.left+ad-Z}X={left:ae,width:ad};if(af){X.bottom=W-T.top;X.top="auto";this.container.addClass("select2-drop-above");Q.addClass("select2-drop-above")}else{X.top=R;X.bottom="auto";this.container.removeClass("select2-drop-above");Q.removeClass("select2-drop-above")}X=D.extend(X,C(this.opts.dropdownCss));Q.css(X)},shouldOpen:function(){var O;if(this.opened()){return false}if(this._enabled===false||this._readonly===true){return false}O=D.Event("select2-opening");this.opts.element.trigger(O);return !O.isDefaultPrevented()},clearDropdownAlignmentPreference:function(){this.container.removeClass("select2-drop-above");this.dropdown.removeClass("select2-drop-above")},open:function(){if(!this.shouldOpen()){return false}this.opening();return true},opening:function(){var T=this.containerId,O="scroll."+T,R="resize."+T,Q="orientationchange."+T,P;this.container.addClass("select2-dropdown-open").addClass("select2-container-active");this.clearDropdownAlignmentPreference();if(this.dropdown[0]!==this.body().children().last()[0]){this.dropdown.detach().appendTo(this.body())}P=D("#select2-drop-mask");if(P.length==0){P=D(document.createElement("div"));P.attr("id","select2-drop-mask").attr("class","select2-drop-mask");P.hide();P.appendTo(this.body());P.on("mousedown touchstart click",function(V){var W=D("#select2-drop"),U;if(W.length>0){U=W.data("select2");if(U.opts.selectOnBlur){U.selectHighlighted({noFocus:true})
}U.close({focus:true});V.preventDefault();V.stopPropagation()}})}if(this.dropdown.prev()[0]!==P[0]){this.dropdown.before(P)}D("#select2-drop").removeAttr("id");this.dropdown.attr("id","select2-drop");P.show();this.positionDropdown();this.dropdown.show();this.positionDropdown();this.dropdown.addClass("select2-drop-active");var S=this;this.container.parents().add(window).each(function(){D(this).on(R+" "+O+" "+Q,function(U){S.positionDropdown()})})},close:function(){if(!this.opened()){return}var R=this.containerId,O="scroll."+R,Q="resize."+R,P="orientationchange."+R;this.container.parents().add(window).each(function(){D(this).off(O).off(Q).off(P)});this.clearDropdownAlignmentPreference();D("#select2-drop-mask").hide();this.dropdown.removeAttr("id");this.dropdown.hide();this.container.removeClass("select2-dropdown-open").removeClass("select2-container-active");this.results.empty();this.clearSearch();this.search.removeClass("select2-active");this.opts.element.trigger(D.Event("select2-close"))},externalSearch:function(O){this.open();this.search.val(O);this.updateResults(false)},clearSearch:function(){},getMaximumSelectionSize:function(){return C(this.opts.maximumSelectionSize)},ensureHighlightVisible:function(){var R=this.results,Q,O,V,U,S,T,P;O=this.highlight();if(O<0){return}if(O==0){R.scrollTop(0);return}Q=this.findHighlightableChoices().find(".select2-result-label");V=D(Q[O]);U=V.offset().top+V.outerHeight(true);if(O===Q.length-1){P=R.find("li.select2-more-results");if(P.length>0){U=P.offset().top+P.outerHeight(true)}}S=R.offset().top+R.outerHeight(true);if(U>S){R.scrollTop(R.scrollTop()+(U-S))}T=V.offset().top-R.offset().top;if(T<0&&V.css("display")!="none"){R.scrollTop(R.scrollTop()+T)}},findHighlightableChoices:function(){return this.results.find(".select2-result-selectable:not(.select2-disabled, .select2-selected)")},moveHighlight:function(R){var Q=this.findHighlightableChoices(),P=this.highlight();while(P>-1&&P<Q.length){P+=R;var O=D(Q[P]);if(O.hasClass("select2-result-selectable")&&!O.hasClass("select2-disabled")&&!O.hasClass("select2-selected")){this.highlight(P);
break}}},highlight:function(P){var R=this.findHighlightableChoices(),O,Q;if(arguments.length===0){return q(R.filter(".select2-highlighted")[0],R.get())}if(P>=R.length){P=R.length-1}if(P<0){P=0}this.removeHighlight();O=D(R[P]);O.addClass("select2-highlighted");this.ensureHighlightVisible();Q=O.data("select2-data");if(Q){this.opts.element.trigger({type:"select2-highlight",val:this.id(Q),choice:Q})}},removeHighlight:function(){this.results.find(".select2-highlighted").removeClass("select2-highlighted")},countSelectableResults:function(){return this.findHighlightableChoices().length},highlightUnderEvent:function(P){var O=D(P.target).closest(".select2-result-selectable");if(O.length>0&&!O.is(".select2-highlighted")){var Q=this.findHighlightableChoices();this.highlight(Q.index(O))}else{if(O.length==0){this.removeHighlight()}}},loadMoreIfNeeded:function(){var S=this.results,R=S.find("li.select2-more-results"),U,T=this.resultsPage+1,O=this,Q=this.search.val(),P=this.context;if(R.length===0){return}U=R.offset().top-S.offset().top-S.height();if(U<=this.opts.loadMorePadding){R.addClass("select2-active");this.opts.query({element:this.opts.element,term:Q,page:T,context:P,matcher:this.opts.matcher,callback:this.bind(function(V){if(!O.opened()){return}O.opts.populateResults.call(this,S,V.results,{term:Q,page:T,context:P});O.postprocessResults(V,false,false);if(V.more===true){R.detach().appendTo(S).text(O.opts.formatLoadMore(T+1));window.setTimeout(function(){O.loadMoreIfNeeded()},10)}else{R.remove()}O.positionDropdown();O.resultsPage=T;O.context=V.context;this.opts.element.trigger({type:"select2-loaded",items:V})})})}},tokenize:function(){},updateResults:function(W){var aa=this.search,U=this.results,O=this.opts,T,Z=this,X,S=aa.val(),Q=D.data(this.container,"select2-last-term"),Y;if(W!==true&&Q&&t(S,Q)){return}D.data(this.container,"select2-last-term",S);if(W!==true&&(this.showSearchInput===false||!this.opened())){return}function V(){aa.removeClass("select2-active");Z.positionDropdown()}function P(ab){U.html(ab);V()}Y=++this.queryCount;
var R=this.getMaximumSelectionSize();if(R>=1){T=this.data();if(D.isArray(T)&&T.length>=R&&y(O.formatSelectionTooBig,"formatSelectionTooBig")){P("<li class='select2-selection-limit'>"+O.formatSelectionTooBig(R)+"</li>");return}}if(aa.val().length<O.minimumInputLength){if(y(O.formatInputTooShort,"formatInputTooShort")){P("<li class='select2-no-results'>"+O.formatInputTooShort(aa.val(),O.minimumInputLength)+"</li>")}else{P("")}if(W&&this.showSearch){this.showSearch(true)}return}if(O.maximumInputLength&&aa.val().length>O.maximumInputLength){if(y(O.formatInputTooLong,"formatInputTooLong")){P("<li class='select2-no-results'>"+O.formatInputTooLong(aa.val(),O.maximumInputLength)+"</li>")}else{P("")}return}if(O.formatSearching&&this.findHighlightableChoices().length===0){P("<li class='select2-searching'>"+O.formatSearching()+"</li>")}aa.addClass("select2-active");this.removeHighlight();X=this.tokenize();if(X!=m&&X!=null){aa.val(X)}this.resultsPage=1;O.query({element:O.element,term:aa.val(),page:this.resultsPage,context:null,matcher:O.matcher,callback:this.bind(function(ac){var ab;if(Y!=this.queryCount){return}if(!this.opened()){this.search.removeClass("select2-active");return}this.context=(ac.context===m)?null:ac.context;if(this.opts.createSearchChoice&&aa.val()!==""){ab=this.opts.createSearchChoice.call(Z,aa.val(),ac.results);if(ab!==m&&ab!==null&&Z.id(ab)!==m&&Z.id(ab)!==null){if(D(ac.results).filter(function(){return t(Z.id(this),Z.id(ab))}).length===0){ac.results.unshift(ab)}}}if(ac.results.length===0&&y(O.formatNoMatches,"formatNoMatches")){P("<li class='select2-no-results'>"+O.formatNoMatches(aa.val())+"</li>");return}U.empty();Z.opts.populateResults.call(this,U,ac.results,{term:aa.val(),page:this.resultsPage,context:null});if(ac.more===true&&y(O.formatLoadMore,"formatLoadMore")){U.append("<li class='select2-more-results'>"+Z.opts.escapeMarkup(O.formatLoadMore(this.resultsPage))+"</li>");window.setTimeout(function(){Z.loadMoreIfNeeded()},10)}this.postprocessResults(ac,W);V();this.opts.element.trigger({type:"select2-loaded",items:ac})
})})},cancel:function(){this.close()},blur:function(){if(this.opts.selectOnBlur){this.selectHighlighted({noFocus:true})}this.close();this.container.removeClass("select2-container-active");if(this.search[0]===document.activeElement){this.search.blur()}this.clearSearch();this.selection.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus")},focusSearch:function(){I(this.search)},selectHighlighted:function(P){var O=this.highlight(),Q=this.results.find(".select2-highlighted"),R=Q.closest(".select2-result").data("select2-data");if(R){this.highlight(O);this.onSelect(R,P)}else{if(P&&P.noFocus){this.close()}}},getPlaceholder:function(){var O;return this.opts.element.attr("placeholder")||this.opts.element.attr("data-placeholder")||this.opts.element.data("placeholder")||this.opts.placeholder||((O=this.getPlaceholderOption())!==m?O.text():m)},getPlaceholderOption:function(){if(this.select){var O=this.select.children("option").first();if(this.opts.placeholderOption!==m){return(this.opts.placeholderOption==="first"&&O)||(typeof this.opts.placeholderOption==="function"&&this.opts.placeholderOption(this.select))}else{if(O.text()===""&&O.val()===""){return O}}}},initContainerWidth:function(){function P(){var U,S,V,T,R,Q;if(this.opts.width==="off"){return null}else{if(this.opts.width==="element"){return this.opts.element.outerWidth(false)===0?"auto":this.opts.element.outerWidth(false)+"px"}else{if(this.opts.width==="copy"||this.opts.width==="resolve"){U=this.opts.element.attr("style");if(U!==m){S=U.split(";");for(T=0,R=S.length;T<R;T=T+1){Q=S[T].replace(/\s/g,"");V=Q.match(/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i);if(V!==null&&V.length>=1){return V[1]}}}if(this.opts.width==="resolve"){U=this.opts.element.css("width");if(U.indexOf("%")>0){return U}return(this.opts.element.outerWidth(false)===0?"auto":this.opts.element.outerWidth(false)+"px")}return null}else{if(D.isFunction(this.opts.width)){return this.opts.width()}else{return this.opts.width}}}}}var O=P.call(this);if(O!==null){this.container.css("width",O)
}}});x=L(N,{createContainer:function(){var O=D(document.createElement("div")).attr({"class":"select2-container"}).html(["<a href='javascript:void(0)' onclick='return false;' class='select2-choice' tabindex='-1'>","   <span class='select2-chosen'>&nbsp;</span><abbr class='select2-search-choice-close'></abbr>","   <span class='select2-arrow'><b></b></span>","</a>","<input class='select2-focusser select2-offscreen' type='text'/>","<div class='select2-drop select2-display-none'>","   <div class='select2-search'>","       <input type='text' autocomplete='off' autocorrect='off' autocapitalize='off' spellcheck='false' class='select2-input'/>","   </div>","   <ul class='select2-results'>","   </ul>","</div>"].join(""));return O},enableInterface:function(){if(this.parent.enableInterface.apply(this,arguments)){this.focusser.prop("disabled",!this.isInterfaceEnabled())}},opening:function(){var Q,P,O;if(this.opts.minimumResultsForSearch>=0){this.showSearch(true)}this.parent.opening.apply(this,arguments);if(this.showSearchInput!==false){this.search.val(this.focusser.val())}this.search.focus();Q=this.search.get(0);if(Q.createTextRange){P=Q.createTextRange();P.collapse(false);P.select()}else{if(Q.setSelectionRange){O=this.search.val().length;Q.setSelectionRange(O,O)}}if(this.search.val()===""){if(this.nextSearchTerm!=m){this.search.val(this.nextSearchTerm);this.search.select()}}this.focusser.prop("disabled",true).val("");this.updateResults(true);this.opts.element.trigger(D.Event("select2-open"))},close:function(O){if(!this.opened()){return}this.parent.close.apply(this,arguments);O=O||{focus:true};this.focusser.removeAttr("disabled");if(O.focus){this.focusser.focus()}},focus:function(){if(this.opened()){this.close()}else{this.focusser.removeAttr("disabled");this.focusser.focus()}},isFocused:function(){return this.container.hasClass("select2-container-active")},cancel:function(){this.parent.cancel.apply(this,arguments);this.focusser.removeAttr("disabled");this.focusser.focus()},destroy:function(){D("label[for='"+this.focusser.attr("id")+"']").attr("for",this.opts.element.attr("id"));
this.parent.destroy.apply(this,arguments)},initContainer:function(){var P,O=this.container,Q=this.dropdown;if(this.opts.minimumResultsForSearch<0){this.showSearch(false)}else{this.showSearch(true)}this.selection=P=O.find(".select2-choice");this.focusser=O.find(".select2-focusser");this.focusser.attr("id","s2id_autogen"+a());D("label[for='"+this.opts.element.attr("id")+"']").attr("for",this.focusser.attr("id"));this.focusser.attr("tabindex",this.elementTabIndex);this.search.on("keydown",this.bind(function(R){if(!this.isInterfaceEnabled()){return}if(R.which===K.PAGE_UP||R.which===K.PAGE_DOWN){A(R);return}switch(R.which){case K.UP:case K.DOWN:this.moveHighlight((R.which===K.UP)?-1:1);A(R);return;case K.ENTER:this.selectHighlighted();A(R);return;case K.TAB:this.selectHighlighted({noFocus:true});return;case K.ESC:this.cancel(R);A(R);return}}));this.search.on("blur",this.bind(function(R){if(document.activeElement===this.body().get(0)){window.setTimeout(this.bind(function(){this.search.focus()}),0)}}));this.focusser.on("keydown",this.bind(function(R){if(!this.isInterfaceEnabled()){return}if(R.which===K.TAB||K.isControl(R)||K.isFunctionKey(R)||R.which===K.ESC){return}if(this.opts.openOnEnter===false&&R.which===K.ENTER){A(R);return}if(R.which==K.DOWN||R.which==K.UP||(R.which==K.ENTER&&this.opts.openOnEnter)){if(R.altKey||R.ctrlKey||R.shiftKey||R.metaKey){return}this.open();A(R);return}if(R.which==K.DELETE||R.which==K.BACKSPACE){if(this.opts.allowClear){this.clear()}A(R);return}}));F(this.focusser);this.focusser.on("keyup-change input",this.bind(function(R){if(this.opts.minimumResultsForSearch>=0){R.stopPropagation();if(this.opened()){return}this.open()}}));P.on("mousedown","abbr",this.bind(function(R){if(!this.isInterfaceEnabled()){return}this.clear();b(R);this.close();this.selection.focus()}));P.on("mousedown",this.bind(function(R){if(!this.container.hasClass("select2-container-active")){this.opts.element.trigger(D.Event("select2-focus"))}if(this.opened()){this.close()}else{if(this.isInterfaceEnabled()){this.open()}}A(R)}));
Q.on("mousedown",this.bind(function(){this.search.focus()}));P.on("focus",this.bind(function(R){A(R)}));this.focusser.on("focus",this.bind(function(){if(!this.container.hasClass("select2-container-active")){this.opts.element.trigger(D.Event("select2-focus"))}this.container.addClass("select2-container-active")})).on("blur",this.bind(function(){if(!this.opened()){this.container.removeClass("select2-container-active");this.opts.element.trigger(D.Event("select2-blur"))}}));this.search.on("focus",this.bind(function(){if(!this.container.hasClass("select2-container-active")){this.opts.element.trigger(D.Event("select2-focus"))}this.container.addClass("select2-container-active")}));this.initContainerWidth();this.opts.element.addClass("select2-offscreen");this.setPlaceholder()},clear:function(Q){var R=this.selection.data("select2-data");if(R){var P=D.Event("select2-clearing");this.opts.element.trigger(P);if(P.isDefaultPrevented()){return}var O=this.getPlaceholderOption();this.opts.element.val(O?O.val():"");this.selection.find(".select2-chosen").empty();this.selection.removeData("select2-data");this.setPlaceholder();if(Q!==false){this.opts.element.trigger({type:"select2-removed",val:this.id(R),choice:R});this.triggerChange({removed:R})}}},initSelection:function(){var P;if(this.isPlaceholderOptionSelected()){this.updateSelection(null);this.close();this.setPlaceholder()}else{var O=this;this.opts.initSelection.call(null,this.opts.element,function(Q){if(Q!==m&&Q!==null){O.updateSelection(Q);O.close();O.setPlaceholder()}})}},isPlaceholderOptionSelected:function(){var O;if(!this.getPlaceholder()){return false}return((O=this.getPlaceholderOption())!==m&&O.prop("selected"))||(this.opts.element.val()==="")||(this.opts.element.val()===m)||(this.opts.element.val()===null)},prepareOpts:function(){var P=this.parent.prepareOpts.apply(this,arguments),O=this;if(P.element.get(0).tagName.toLowerCase()==="select"){P.initSelection=function(Q,S){var R=Q.find("option").filter(function(){return this.selected});S(O.optionToData(R))}}else{if("data" in P){P.initSelection=P.initSelection||function(R,T){var S=R.val();
var Q=null;P.query({matcher:function(U,X,V){var W=t(S,P.id(V));if(W){Q=V}return W},callback:!D.isFunction(T)?D.noop:function(){T(Q)}})}}}return P},getPlaceholder:function(){if(this.select){if(this.getPlaceholderOption()===m){return m}}return this.parent.getPlaceholder.apply(this,arguments)},setPlaceholder:function(){var O=this.getPlaceholder();if(this.isPlaceholderOptionSelected()&&O!==m){if(this.select&&this.getPlaceholderOption()===m){return}this.selection.find(".select2-chosen").html(this.opts.escapeMarkup(O));this.selection.addClass("select2-default");this.container.removeClass("select2-allowclear")}},postprocessResults:function(T,P,S){var R=0,O=this,U=true;this.findHighlightableChoices().each2(function(V,W){if(t(O.id(W.data("select2-data")),O.opts.element.val())){R=V;return false}});if(S!==false){if(P===true&&R>=0){this.highlight(R)}else{this.highlight(0)}}if(P===true){var Q=this.opts.minimumResultsForSearch;if(Q>=0){this.showSearch(r(T.results)>=Q)}}},showSearch:function(O){if(this.showSearchInput===O){return}this.showSearchInput=O;this.dropdown.find(".select2-search").toggleClass("select2-search-hidden",!O);this.dropdown.find(".select2-search").toggleClass("select2-offscreen",!O);D(this.dropdown,this.container).toggleClass("select2-with-searchbox",O)},onSelect:function(Q,P){if(!this.triggerSelect(Q)){return}var O=this.opts.element.val(),R=this.data();this.opts.element.val(this.id(Q));this.updateSelection(Q);this.opts.element.trigger({type:"select2-selected",val:this.id(Q),choice:Q});this.nextSearchTerm=this.opts.nextSearchTerm(Q,this.search.val());this.close();if(!P||!P.noFocus){this.focusser.focus()}if(!t(O,this.id(Q))){this.triggerChange({added:Q,removed:R})}},updateSelection:function(R){var P=this.selection.find(".select2-chosen"),Q,O;this.selection.data("select2-data",R);P.empty();if(R!==null){Q=this.opts.formatSelection(R,P,this.opts.escapeMarkup)}if(Q!==m){P.append(Q)}O=this.opts.formatSelectionCssClass(R,P);if(O!==m){P.addClass(O)}this.selection.removeClass("select2-default");if(this.opts.allowClear&&this.getPlaceholder()!==m){this.container.addClass("select2-allowclear")
}},val:function(){var S,P=false,Q=null,O=this,R=this.data();if(arguments.length===0){return this.opts.element.val()}S=arguments[0];if(arguments.length>1){P=arguments[1]}if(this.select){this.select.val(S).find("option").filter(function(){return this.selected}).each2(function(T,U){Q=O.optionToData(U);return false});this.updateSelection(Q);this.setPlaceholder();if(P){this.triggerChange({added:Q,removed:R})}}else{if(!S&&S!==0){this.clear(P);return}if(this.opts.initSelection===m){throw new Error("cannot call val() if initSelection() is not defined")}this.opts.element.val(S);this.opts.initSelection(this.opts.element,function(T){O.opts.element.val(!T?"":O.id(T));O.updateSelection(T);O.setPlaceholder();if(P){O.triggerChange({added:T,removed:R})}})}},clearSearch:function(){this.search.val("");this.focusser.val("")},data:function(Q){var P,O=false;if(arguments.length===0){P=this.selection.data("select2-data");if(P==m){P=null}return P}else{if(arguments.length>1){O=arguments[1]}if(!Q){this.clear(O)}else{P=this.data();this.opts.element.val(!Q?"":this.id(Q));this.updateSelection(Q);if(O){this.triggerChange({added:Q,removed:P})}}}}});c=L(N,{createContainer:function(){var O=D(document.createElement("div")).attr({"class":"select2-container select2-container-multi"}).html(["<ul class='select2-choices'>","  <li class='select2-search-field'>","    <input type='text' autocomplete='off' autocorrect='off' autocapitalize='off' spellcheck='false' class='select2-input'>","  </li>","</ul>","<div class='select2-drop select2-drop-multi select2-display-none'>","   <ul class='select2-results'>","   </ul>","</div>"].join(""));return O},prepareOpts:function(){var P=this.parent.prepareOpts.apply(this,arguments),O=this;if(P.element.get(0).tagName.toLowerCase()==="select"){P.initSelection=function(Q,S){var R=[];Q.find("option").filter(function(){return this.selected}).each2(function(T,U){R.push(O.optionToData(U))});S(R)}}else{if("data" in P){P.initSelection=P.initSelection||function(Q,T){var R=i(Q.val(),P.separator);var S=[];P.query({matcher:function(U,X,V){var W=D.grep(R,function(Y){return t(Y,P.id(V))
}).length;if(W){S.push(V)}return W},callback:!D.isFunction(T)?D.noop:function(){var U=[];for(var X=0;X<R.length;X++){var Y=R[X];for(var W=0;W<S.length;W++){var V=S[W];if(t(Y,P.id(V))){U.push(V);S.splice(W,1);break}}}T(U)}})}}}return P},selectChoice:function(O){var P=this.container.find(".select2-search-choice-focus");if(P.length&&O&&O[0]==P[0]){}else{if(P.length){this.opts.element.trigger("choice-deselected",P)}P.removeClass("select2-search-choice-focus");if(O&&O.length){this.close();O.addClass("select2-search-choice-focus");this.opts.element.trigger("choice-selected",O)}}},destroy:function(){D("label[for='"+this.search.attr("id")+"']").attr("for",this.opts.element.attr("id"));this.parent.destroy.apply(this,arguments)},initContainer:function(){var O=".select2-choices",P;this.searchContainer=this.container.find(".select2-search-field");this.selection=P=this.container.find(O);var Q=this;this.selection.on("click",".select2-search-choice:not(.select2-locked)",function(R){Q.search[0].focus();Q.selectChoice(D(this))});this.search.attr("id","s2id_autogen"+a());D("label[for='"+this.opts.element.attr("id")+"']").attr("for",this.search.attr("id"));this.search.on("input paste",this.bind(function(){if(!this.isInterfaceEnabled()){return}if(!this.opened()){this.open()}}));this.search.attr("tabindex",this.elementTabIndex);this.keydowns=0;this.search.on("keydown",this.bind(function(V){if(!this.isInterfaceEnabled()){return}++this.keydowns;var T=P.find(".select2-search-choice-focus");var U=T.prev(".select2-search-choice:not(.select2-locked)");var S=T.next(".select2-search-choice:not(.select2-locked)");var W=f(this.search);if(T.length&&(V.which==K.LEFT||V.which==K.RIGHT||V.which==K.BACKSPACE||V.which==K.DELETE||V.which==K.ENTER)){var R=T;if(V.which==K.LEFT&&U.length){R=U}else{if(V.which==K.RIGHT){R=S.length?S:null}else{if(V.which===K.BACKSPACE){this.unselect(T.first());this.search.width(10);R=U.length?U:S}else{if(V.which==K.DELETE){this.unselect(T.first());this.search.width(10);R=S.length?S:null}else{if(V.which==K.ENTER){R=null}}}}}this.selectChoice(R);
A(V);if(!R||!R.length){this.open()}return}else{if(((V.which===K.BACKSPACE&&this.keydowns==1)||V.which==K.LEFT)&&(W.offset==0&&!W.length)){this.selectChoice(P.find(".select2-search-choice:not(.select2-locked)").last());A(V);return}else{this.selectChoice(null)}}if(this.opened()){switch(V.which){case K.UP:case K.DOWN:this.moveHighlight((V.which===K.UP)?-1:1);A(V);return;case K.ENTER:this.selectHighlighted();A(V);return;case K.TAB:this.selectHighlighted({noFocus:true});this.close();return;case K.ESC:this.cancel(V);A(V);return}}if(V.which===K.TAB||K.isControl(V)||K.isFunctionKey(V)||V.which===K.BACKSPACE||V.which===K.ESC){return}if(V.which===K.ENTER){if(this.opts.openOnEnter===false){return}else{if(V.altKey||V.ctrlKey||V.shiftKey||V.metaKey){return}}}this.open();if(V.which===K.PAGE_UP||V.which===K.PAGE_DOWN){A(V)}if(V.which===K.ENTER){A(V)}}));this.search.on("keyup",this.bind(function(R){this.keydowns=0;this.resizeSearch()}));this.search.on("blur",this.bind(function(R){this.container.removeClass("select2-container-active");this.search.removeClass("select2-focused");this.selectChoice(null);if(!this.opened()){this.clearSearch()}R.stopImmediatePropagation();this.opts.element.trigger(D.Event("select2-blur"))}));this.container.on("click",O,this.bind(function(R){if(!this.isInterfaceEnabled()){return}if(D(R.target).closest(".select2-search-choice").length>0){return}this.selectChoice(null);this.clearPlaceholder();if(!this.container.hasClass("select2-container-active")){this.opts.element.trigger(D.Event("select2-focus"))}this.open();this.focusSearch();R.preventDefault()}));this.container.on("focus",O,this.bind(function(){if(!this.isInterfaceEnabled()){return}if(!this.container.hasClass("select2-container-active")){this.opts.element.trigger(D.Event("select2-focus"))}this.container.addClass("select2-container-active");this.dropdown.addClass("select2-drop-active");this.clearPlaceholder()}));this.initContainerWidth();this.opts.element.addClass("select2-offscreen");this.clearSearch()},enableInterface:function(){if(this.parent.enableInterface.apply(this,arguments)){this.search.prop("disabled",!this.isInterfaceEnabled())
}},initSelection:function(){var P;if(this.opts.element.val()===""&&this.opts.element.text()===""){this.updateSelection([]);this.close();this.clearSearch()}if(this.select||this.opts.element.val()!==""){var O=this;this.opts.initSelection.call(null,this.opts.element,function(Q){if(Q!==m&&Q!==null){O.updateSelection(Q);O.close();O.clearSearch()}})}},clearSearch:function(){var P=this.getPlaceholder(),O=this.getMaxSearchWidth();if(P!==m&&this.getVal().length===0&&this.search.hasClass("select2-focused")===false){this.search.val(P).addClass("select2-default");this.search.width(O>0?O:this.container.css("width"))}else{this.search.val("").width(10)}},clearPlaceholder:function(){if(this.search.hasClass("select2-default")){this.search.val("").removeClass("select2-default")}},opening:function(){this.clearPlaceholder();this.resizeSearch();this.parent.opening.apply(this,arguments);this.focusSearch();this.updateResults(true);this.search.focus();this.opts.element.trigger(D.Event("select2-open"))},close:function(){if(!this.opened()){return}this.parent.close.apply(this,arguments)},focus:function(){this.close();this.search.focus()},isFocused:function(){return this.search.hasClass("select2-focused")},updateSelection:function(R){var Q=[],P=[],O=this;D(R).each(function(){if(q(O.id(this),Q)<0){Q.push(O.id(this));P.push(this)}});R=P;this.selection.find(".select2-search-choice").remove();D(R).each(function(){O.addSelectedChoice(this)});O.postprocessResults()},tokenize:function(){var O=this.search.val();O=this.opts.tokenizer.call(this,O,this.data(),this.bind(this.onSelect),this.opts);if(O!=null&&O!=m){this.search.val(O);if(O.length>0){this.open()}}},onSelect:function(P,O){if(!this.triggerSelect(P)){return}this.addSelectedChoice(P);this.opts.element.trigger({type:"selected",val:this.id(P),choice:P});if(this.select||!this.opts.closeOnSelect){this.postprocessResults(P,false,this.opts.closeOnSelect===true)}if(this.opts.closeOnSelect){this.close();this.search.width(10)}else{if(this.countSelectableResults()>0){this.search.width(10);this.resizeSearch();
if(this.getMaximumSelectionSize()>0&&this.val().length>=this.getMaximumSelectionSize()){this.updateResults(true)}this.positionDropdown()}else{this.close();this.search.width(10)}}this.triggerChange({added:P});if(!O||!O.noFocus){this.focusSearch()}},cancel:function(){this.close();this.focusSearch()},addSelectedChoice:function(S){var U=!S.locked,Q=D("<li class='select2-search-choice'>    <div></div>    <a href='#' onclick='return false;' class='select2-search-choice-close' tabindex='-1'></a></li>"),V=D("<li class='select2-search-choice select2-locked'><div></div></li>");var R=U?Q:V,O=this.id(S),P=this.getVal(),T,W;T=this.opts.formatSelection(S,R.find("div"),this.opts.escapeMarkup);if(T!=m){R.find("div").replaceWith("<div>"+T+"</div>")}W=this.opts.formatSelectionCssClass(S,R.find("div"));if(W!=m){R.addClass(W)}if(U){R.find(".select2-search-choice-close").on("mousedown",A).on("click dblclick",this.bind(function(X){if(!this.isInterfaceEnabled()){return}D(X.target).closest(".select2-search-choice").fadeOut("fast",this.bind(function(){this.unselect(D(X.target));this.selection.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus");this.close();this.focusSearch()})).dequeue();A(X)})).on("focus",this.bind(function(){if(!this.isInterfaceEnabled()){return}this.container.addClass("select2-container-active");this.dropdown.addClass("select2-drop-active")}))}R.data("select2-data",S);R.insertBefore(this.searchContainer);P.push(O);this.setVal(P)},unselect:function(Q){var S=this.getVal(),R,P;Q=Q.closest(".select2-search-choice");if(Q.length===0){throw"Invalid argument: "+Q+". Must be .select2-search-choice"}R=Q.data("select2-data");if(!R){return}while((P=q(this.id(R),S))>=0){S.splice(P,1);this.setVal(S);if(this.select){this.postprocessResults()}}var O=D.Event("select2-removing");O.val=this.id(R);O.choice=R;this.opts.element.trigger(O);if(O.isDefaultPrevented()){return}Q.remove();this.opts.element.trigger({type:"select2-removed",val:this.id(R),choice:R});this.triggerChange({removed:R})},postprocessResults:function(S,P,R){var T=this.getVal(),U=this.results.find(".select2-result"),Q=this.results.find(".select2-result-with-children"),O=this;
U.each2(function(W,V){var X=O.id(V.data("select2-data"));if(q(X,T)>=0){V.addClass("select2-selected");V.find(".select2-result-selectable").addClass("select2-selected")}});Q.each2(function(W,V){if(!V.is(".select2-result-selectable")&&V.find(".select2-result-selectable:not(.select2-selected)").length===0){V.addClass("select2-selected")}});if(this.highlight()==-1&&R!==false){O.highlight(0)}if(!this.opts.createSearchChoice&&!U.filter(".select2-result:not(.select2-selected)").length>0){if(!S||S&&!S.more&&this.results.find(".select2-no-results").length===0){if(y(O.opts.formatNoMatches,"formatNoMatches")){this.results.append("<li class='select2-no-results'>"+O.opts.formatNoMatches(O.search.val())+"</li>")}}}},getMaxSearchWidth:function(){return this.selection.width()-h(this.search)},resizeSearch:function(){var T,R,Q,O,P,S=h(this.search);T=n(this.search)+10;R=this.search.offset().left;Q=this.selection.width();O=this.selection.offset().left;P=Q-(R-O)-S;if(P<T){P=Q-S}if(P<40){P=Q-S}if(P<=0){P=T}this.search.width(Math.floor(P))},getVal:function(){var O;if(this.select){O=this.select.val();return O===null?[]:O}else{O=this.opts.element.val();return i(O,this.opts.separator)}},setVal:function(P){var O;if(this.select){this.select.val(P)}else{O=[];D(P).each(function(){if(q(this,O)<0){O.push(this)}});this.opts.element.val(O.length===0?"":O.join(this.opts.separator))}},buildChangeDetails:function(O,R){var R=R.slice(0),O=O.slice(0);for(var Q=0;Q<R.length;Q++){for(var P=0;P<O.length;P++){if(t(this.opts.id(R[Q]),this.opts.id(O[P]))){R.splice(Q,1);if(Q>0){Q--}O.splice(P,1);P--}}}return{added:R,removed:O}},val:function(R,P){var Q,O=this;if(arguments.length===0){return this.getVal()}Q=this.data();if(!Q.length){Q=[]}if(!R&&R!==0){this.opts.element.val("");this.updateSelection([]);this.clearSearch();if(P){this.triggerChange({added:this.data(),removed:Q})}return}this.setVal(R);if(this.select){this.opts.initSelection(this.select,this.bind(this.updateSelection));if(P){this.triggerChange(this.buildChangeDetails(Q,this.data()))}}else{if(this.opts.initSelection===m){throw new Error("val() cannot be called if initSelection() is not defined")
}this.opts.initSelection(this.opts.element,function(T){var S=D.map(T,O.id);O.setVal(S);O.updateSelection(T);O.clearSearch();if(P){O.triggerChange(O.buildChangeDetails(Q,O.data()))}})}this.clearSearch()},onSortStart:function(){if(this.select){throw new Error("Sorting of elements is not supported when attached to <select>. Attach to <input type='hidden'/> instead.")}this.search.width(0);this.searchContainer.hide()},onSortEnd:function(){var P=[],O=this;this.searchContainer.show();this.searchContainer.appendTo(this.searchContainer.parent());this.resizeSearch();this.selection.find(".select2-search-choice").each(function(){P.push(O.opts.id(D(this).data("select2-data")))});this.setVal(P);this.triggerChange()},data:function(Q,R){var P=this,S,O;if(arguments.length===0){return this.selection.find(".select2-search-choice").map(function(){return D(this).data("select2-data")}).get()}else{O=this.data();if(!Q){Q=[]}S=D.map(Q,function(T){return P.opts.id(T)});this.setVal(S);this.updateSelection(Q);this.clearSearch();if(R){this.triggerChange(this.buildChangeDetails(O,this.data()))}}}});D.fn.select2=function(){var T=Array.prototype.slice.call(arguments,0),P,S,O,V,X,W=["val","destroy","opened","open","close","focus","isFocused","container","dropdown","onSortStart","onSortEnd","enable","disable","readonly","positionDropdown","data","search"],U=["opened","isFocused","container","dropdown"],Q=["val","data"],R={search:"externalSearch"};this.each(function(){if(T.length===0||typeof(T[0])==="object"){P=T.length===0?{}:D.extend({},T[0]);P.element=D(this);if(P.element.get(0).tagName.toLowerCase()==="select"){X=P.element.prop("multiple")}else{X=P.multiple||false;if("tags" in P){P.multiple=X=true}}S=X?new c():new x();S.init(P)}else{if(typeof(T[0])==="string"){if(q(T[0],W)<0){throw"Unknown method: "+T[0]}V=m;S=D(this).data("select2");if(S===m){return}O=T[0];if(O==="container"){V=S.container}else{if(O==="dropdown"){V=S.dropdown}else{if(R[O]){O=R[O]}V=S[O].apply(S,T.slice(1))}}if(q(T[0],U)>=0||(q(T[0],Q)&&T.length==1)){return false}}else{throw"Invalid arguments to select2 plugin: "+T
}}});return(V===m)?this:V};D.fn.select2.defaults={width:"copy",loadMorePadding:0,closeOnSelect:true,openOnEnter:true,containerCss:{},dropdownCss:{},containerCssClass:"",dropdownCssClass:"",formatResult:function(P,Q,S,O){var R=[];u(P.text,S.term,R,O);return R.join("")},formatSelection:function(Q,P,O){return Q?O(Q.text):m},sortResults:function(P,O,Q){return P},formatResultCssClass:function(O){return m},formatSelectionCssClass:function(P,O){return m},formatNoMatches:function(){return"No matches found"},formatInputTooShort:function(O,P){var Q=P-O.length;return"Please enter "+Q+" more character"+(Q==1?"":"s")},formatInputTooLong:function(P,O){var Q=P.length-O;return"Please delete "+Q+" character"+(Q==1?"":"s")},formatSelectionTooBig:function(O){return"You can only select "+O+" item"+(O==1?"":"s")},formatLoadMore:function(O){return"Loading more results..."},formatSearching:function(){return"Searching..."},minimumResultsForSearch:0,minimumInputLength:0,maximumInputLength:null,maximumSelectionSize:0,id:function(O){return O.id},matcher:function(O,P){return e(""+P).toUpperCase().indexOf(e(""+O).toUpperCase())>=0},separator:",",tokenSeparators:[],tokenizer:g,escapeMarkup:G,blurOnChange:false,selectOnBlur:false,adaptContainerCssClass:function(O){return O},adaptDropdownCssClass:function(O){return null},nextSearchTerm:function(O,P){return m}};D.fn.select2.ajaxDefaults={transport:D.ajax,params:{type:"GET",cache:false,dataType:"json"}};window.Select2={query:{ajax:E,local:H,tags:z},util:{debounce:k,markMatch:u,escapeMarkup:G,stripDiacritics:e},"class":{"abstract":N,single:x,multi:c}}}(jQuery));(function(a){a.extend(a.fn.select2.defaults,{formatNoMatches:function(){return"没有找到匹配项"},formatInputTooShort:function(b,c){var d=c-b.length;return"请再输入"+d+"个字符"},formatInputTooLong:function(c,b){var d=c.length-b;return"请删掉"+d+"个字符"},formatSelectionTooBig:function(b){return"你只能选择最多"+b+"项"},formatLoadMore:function(b){return"加载结果中..."},formatSearching:function(){return"搜索中..."}})})(jQuery);
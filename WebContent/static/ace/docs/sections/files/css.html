<section>
	<h1 class="blue" data-id="#files/css"><i class="ace-icon fa fa-file-o grey"></i> CSS/LESS Files</h1>
	<hr />

	<!-- #section:files/css -->
	<div class="help-content">
		<h3 class="info-title smaller" data-id="#files/css.bootstrap">1. Bootstrap</h3>
		<!-- #section:files/css.bootstrap -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				Bootstrap's LESS files are not included in the package except for the following:
				<br />
				<code><span class="brief-show">assets/css/less/bootstrap/</span>variables.less</code> 
				<code><span class="brief-show">assets/css/less/bootstrap/</span>mixins.less</code>
			</li>
			<li>
				I have modified Bootstrap's CSS file slightly so it's different
				from original files you download from official site.
			</li>
			<li>
				But you can use original Bootstrap CSS file without problem.
				<br />
				Just remember to replace Ace's version of Bootstrap variables file located at
				<code>assets/css/less/bootstrap/variables.less</code>
				with original Bootstrap variables that you download,
				and re-compile Ace's LESS files into CSS again.
			</li>
				
			<li>
				Specifically, I have changed <code>@gutterWidth</code> from <b>30</b> to <b>24</b>
				and changed <code>@grid-float-breakpoint-max</code> from <b>@screen-sm-min</b><i>(768px)</i> to <b>@screen-md-min</b><i>(992px)</i>.
			</li>
			
			<li>
				The result is that with default Bootstrap, mobile menu will be visible when window width is less than <code>768px</code>
				but now it's been modified to <code>992px</code>.
				<br />
				And it's fine as most desktop browsers are wider than <code>1000px</code>.
			</li>

			<!--
			<li>
				<div class="space-8"></div>
				I have also modified <code>mixins.less</code> to reduce the number of floating point digits.
				<br />
				So for example:
<pre data-language="css">
.col-xs-10 {
  width: 83.33333333%;
}</pre>
has been changed to:
<pre data-language="css">
.col-xs-10 {
  width: 83.3333%;
}</pre>
			</li>
-->
			

		 </ul>
		</div>
		<!-- /section:files/css.bootstrap -->


		<h3 class="info-title smaller" data-id="#files/css.third">2. Third party</h3>
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				Third party plugin CSS files are inside
				<code>assets/css/</code> folder.
			</li>
			<li>
				Minified version is find inside <code>dist/css/</code> folder.
			</li>
		 </ul>
		</div>


		
		<h3 class="info-title smaller" data-id="#files/css.less">3. Ace LESS</h3>
		<!-- #section:files/css.less -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				Ace's main CSS file (<code>ace.css</code> or <code>ace.min.css</code>) is generated by compiling LESS files.
			</li>
			<li>
				You can find LESS files here <code>assets/css/less</code>
				and compile them into CSS using <b>Node.js</b> and  <b>LESS</b> compiler using the following command:
				<br />
				<code>lessc <span class="green">ace.less</span> ../ace.css</code>
				<br />
				or
				<br />
				<code>lessc <span class="green">ace.less</span> <b class="blue">--clean-css</b> ../ace<b class="blue">.min</b>.css</code>
			</li>
			
			<li>
				Main less file is:
				<br />
				<code data-open-file="css" class="open-file"><span class="brief-show">assets/css/less/</span>ace.less</code>
			</li>
			<li>
				Variable files are:
				<br />
				<code data-open-file="css" class="open-file"><span class="brief-show">assets/css/less/</span>variables.less</code>
				<br />
				<code data-open-file="css" class="open-file"><span class="brief-show">assets/css/less/</span>fonts.less</code>
				<br />
				and
				<br />
				<code data-open-file="css" class="open-file"><span class="brief-show">assets/css/less/</span>ace-features.less</code>
				<br />
				which enables feature selection.				
			</li>
			
			<li>
				<div class="alert alert-info">
					If you don't have Node.js installed, you can download it here:
					<a href="http://nodejs.org/download/">http://nodejs.org/download/</a>
					<br />
					Then you should install less compiler using the following command:
					<br />
					<code>npm install -g less</code>
				</div>
			</li>
		 </ul>
		</div>
		<!-- /section:files/css.less -->


		
		<h3 class="info-title smaller" data-id="#files/css.css">4. Ace CSS</h3>
		<!-- #section:files/css.css -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				Ace's main CSS file is generated by compiling LESS files into CSS:
				<br />
				<code>lessc ace.less ../ace.css</code>
				<br />
			</li>
			<li>
				But there is also:
				<ul class="list-unstyled info-list">
					<li>
						<code>ace-ie.css</code> which has some rules for IE9 and below and should be included for IE only using conditional comments:
<pre class="light-green">
&lt;!--[if lte IE <span class="light-red bolder">9</span>&gt;
&lt;link rel="stylesheet" href="<span class="purple bolder">path/to/assets/<span class="light-blue">ace-ie.css</span></span>" /&gt;
&lt;![endif]--&gt;
</pre>
					</li>
					
					<li>
						<code>ace-part2.css</code> which is used when main CSS file (ace.css) is too large for IE9 and below (the 4096 selector limit).
<pre class="light-green">
&lt;!--[if lte IE <span class="light-red bolder">9</span>&gt;
&lt;link rel="stylesheet" href="<span class="purple bolder">path/to/assets/<span class="light-blue">ace-part2.css</span></span>" /&gt;
&lt;![endif]--&gt;
</pre>
						See the following links for more info about this IE limitation:
						<br />
						<a href="http://support.microsoft.com/kb/262161">Link 1</a>
						&
						<a href="http://blogs.msdn.com/b/ieinternals/archive/2011/05/14/10164546.aspx">Link 2</a>
						
					</li>
					
					<li>
						<code>ace-rtl.css</code> file which is used for RTL (right to left) direction suitable for languages such as Arabic, Hebrew and Persian.
						<br />
						For more info about using RTL, please see <a href="#settings.rtl">RTL section</a>
					</li>
					
					<li>
						<code>ace-fonts.css</code> which is the font definition file.
						<br />
						Used only when you want to serve local fonts rather using fonts directly from google.
						<br />
						You can also put content of <code>ace-fonts.css</code>
						on top of your main CSS file to reduce number of files.
					</li>
				</ol>
			</li>
		 </ul>
		</div>
		<!-- /section:files/css.css -->
		
		
		
		<h3 class="info-title smaller" data-id="#files/css.skins">5. Ace Skins</h3>
		<!-- #section:files/css.skins -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				<code>ace-skins.css</code> file is generated by compiling
				<br />
				<code>assets/css/less/skins/skins.less</code>
				<br />
				using the following command:
				<br />
				<code>lessc <span class="green">skins/skins.less</span> ../ace-skins.css</code>
				<br />
				or
				<br />
				<code>lessc <span class="green">skins/skins.less</span> <b class="blue">--clean-css</b> ../ace-skins<b class="blue">.min</b>.css</code>
			</li>
			
			<li>
				If you don't want to use a separate skin file,
				you can compile <code>ace.less</code> using your skin of choice.

				<br />
				To do that, edit
				<br />
				<code>assets/css/less/sidebar.less</code>
				<br />
				and replace
				<br />
				<code>@import "skins/no-skin.less"</code>
				<br />
				with
				<br />
				<code>@import "skins/skin-1.less</code>
				<br />
				or any other skin.
			</li>
			
			<li>
				For more info about skins you may want to see <a href="#settings.skins">skins section</a> as well.
			</li>
		 </ul>
		</div>
		<!-- /section:files/css.skins -->
		
		
		<h3 class="info-title smaller" data-id="#files/css.order">6. CSS file order</h3>
		<!-- #section:files/css.order -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				The correct order of including CSS files is as follows:
				<ol>
					<li>
						<b>bootstrap.css</b>
					</li>
					<li>
						<b>font-awesome.css</b> if you want to use Fontawesome icons.
					</li>
					<li>
						<b>ace-fonts.css</b>
						<br />
						You may want to serve fonts from your own server or hosted by Google.
						<br />
						View this file for an example:
						<br />
						<code data-open-file="html" class="open-file"><span class="brief-show">mustache/app/views/layouts/partials/_shared/_template/</span>fonts.mustache</code>
					</li>
					
					<li>
						Third party css files such as <b>chosen.css</b> or <b>ui.jqgrid.css</b>
					</li>
					
					<li>
						<b>ace.css</b>. Ace's main CSS file.
					</li>
					<li>
						<b>ace-part2.css</b>. IE 9 and below have a 4096 selector limit.
						<br />
						See the following links for more info about this IE limitation:
						<br />
						<a href="http://support.microsoft.com/kb/262161">Link 1</a>
						&
						<a href="http://blogs.msdn.com/b/ieinternals/archive/2011/05/14/10164546.aspx">Link 2</a>
						
						<div class="space-4"></div>
						I recommend building a custom, smaller CSS file according to your needs, using <a href="../build/css.html">CSS builder</a> and you may not need a second CSS file for IE.
					</li>
					
					<li>
						<b>ace-skins.css</b>.
						Ace skin CSS file, if you want to use a skin other that default one.
						<br />
						You can also compile default Ace styles (ace.css) using a different skin.
						Please refer to the above "Ace Skins" section for more info.
					</li>
					<li>
						<b>ace-rtl.css</b>. Ace's RTL direction CSS file, if you want to use Arabic, Hebrew or Persian.
					</li>
					
					<li>
						<b>ace-ie.css</b> which is used by IE9 and below.
					</li>
					
					<li>
						Inline styles which is not recommend and you should always try to separate your CSS from your HTML and application code.
					</li>
				</ol>
			</li>
			
			<li>
<pre data-language="html">
 &lt;link href="path/to/bootstrap.min.css" rel="stylesheet" /&gt;
 &lt;link href="path/to/fontawesome.min.css" rel="stylesheet" /&gt;&lt;!-- only if needed --&gt;

 &lt;link href="path/to/ace-fonts.css" rel="stylesheet" /&gt;&lt;!-- you can also use google hosted fonts --&gt;

 &lt;link href="path/to/ace.min.css" rel="stylesheet" class="ace-main-stylesheet" /&gt;
 &lt;!--[if lte IE 9]&gt;
  &lt;link href="path/to/ace-part2.min.css" rel="stylesheet" class="ace-main-stylesheet" /&gt;
 &lt;![endif]--&gt;

 &lt;link href="path/to/ace-skins.min.css" rel="stylesheet" /&gt;&lt;!-- only if needed --&gt;
 &lt;link href="path/to/ace-rtl.min.css" rel="stylesheet" /&gt;&lt;!-- only if needed --&gt;

 &lt;!--[if lte IE 9]&gt;
 &lt;link href="path/to/ace-ie.min.css" rel="stylesheet" /&gt;
 &lt;![endif]--&gt;

 &lt;style&gt;
  /* not recommended to define new CSS rules inside your HTML page */
  .some-selector {
    color: red;
  }
 &lt;/style&gt;
</pre>
			</li>
		 </ul>
		</div>
		<!-- #section:files/css.order -->

		
		<h3 class="info-title smaller" data-id="#files/css.fonts">7. Fonts</h3>
		<!-- #section:files/css.fonts -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				Ace uses "Open Sans" font from Google as its a popular font with good character support.
				<br />
				Only "normal" and "light" font weights are included to make the page load faster:
				<code>http://fonts.googleapis.com/css?family=Open+Sans:<u>400</u>,<u>300</u></code>
				<br />
				See this for more info: <a href="http://www.google.com/fonts#UsePlace:use/Collection:Open+Sans">Google Open Sans</a>

				<div class="space-6"></div>
				You can use google hosted fonts:
				<pre data-language="html">
<!-- serve fonts from google -->
&lt;link rel="stylesheet" href="//fonts.googleapis.com/css?family=Open+Sans:400,300" /&gt;</pre>
				
				Or serve from your own server: (especially good during development, so that your pages load faster)
				<pre data-language="html">
&lt;link rel="stylesheet" href="path/to/ace-fonts.css" /&gt;</pre>
				You can also embed the contents of "ace-fonts.css" files in your main CSS file to reduce number of files:
				<pre data-language="css">
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 300;
  src: local('Open Sans Light'), local('OpenSans-Light'), url(path/to/font/DXI1ORHCpsQm3Vp6mXoaTXhCUOGz7vYGh680lGh-uXM.woff) format('woff');
}
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src: local('Open Sans'), local('OpenSans'), url(path/to/font/cJZKeOuBrn4kERxqtaUH3T8E0i7KZn-EPnyo3HZu7kw.woff) format('woff');
}</pre>
				Font files are located inside: <code>assets/font</code>
			</li>
			<li>
				For icons you can use Fontawesome, Glyphicons or any other font library.
				<br />
				Icon font files are located inside: <code>assets/fonts</code>
				and  you can also serve CDN hosted fonts, for example:
				<pre data-language="html">
&lt;link rel="stylesheet" href="//netdna.bootstrapcdn.com/font-awesome/4.2.0/css/font-awesome.min.css" /&gt;</pre>

				<div class="space-4"></div>
				Please also see <a href="#elements.icon" class="help-more">Icons section</a> for more info.
			</li>
		 </ul>
		</div>
		<!-- /section:files/css.fonts -->



		<h3 class="info-title smaller" data-id="#files/css.build">8. CSS Builder</h3>
		<!-- #section:files/css.build -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				There is also the in-browser CSS builder you can use to build a custom CSS output, using only the parts you need.
			</li>
			<li>
				You can find it here: <a href="../build/css.html">path/to/ace/build/css.html</a>
			</li>
		 </ul>
		</div>
		<!-- /section:files/css.build -->
		
	</div>
	<!-- /section:files/css -->
</section>
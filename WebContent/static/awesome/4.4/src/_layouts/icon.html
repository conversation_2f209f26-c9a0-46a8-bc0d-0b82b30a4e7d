---
layout: base
title_suffix: Font Awesome Icons
relative_path: ../../
---
<div class="jumbotron jumbotron-icon">
  <div class="container">
    <div class="info-icons">
      <i class="fa fa-{{ page.icon.id }} fa-6"></i>&nbsp;&nbsp;
      <span class="hide-xs">
        <i class="fa fa-{{ page.icon.id }} fa-5"></i>&nbsp;&nbsp;
        <span class="hide-sm"><i class="fa fa-{{ page.icon.id }} fa-4"></i>&nbsp;&nbsp;</span>
        <i class="fa fa-{{ page.icon.id }} fa-3"></i>&nbsp;&nbsp;
        <i class="fa fa-{{ page.icon.id }} fa-2"></i>&nbsp;
      </span>
      <i class="fa fa-{{ page.icon.id }} fa-1"></i>
    </div>
    <h1 class="info-class">
      fa-{{ page.icon.id }}
      <small>
        <i class="fa fa-{{ page.icon.id }}"></i> &middot;
        Unicode: <span class="upper">{{ page.icon.unicode }}</span> &middot;
        Created: v{{ page.icon.created }} &middot;
        Categories:
        {% for category in page.icon.categories %}
          {{ category }}{% unless forloop.last %},{% endunless %}
        {% endfor %}
        {% assign icon_alias_count = page.icon.aliases | size %}
        {% if icon_alias_count > 0 %}
          &middot; Aliases:
          {% for alias in page.icon.aliases %}
            fa-{{ alias }}{% unless forloop.last %},{% endunless %}
          {% endfor %}
        {% endif %}
        {% if page.icon.url %}&middot; <a target="_blank" href="http://{{ page.icon.url }}">{{ page.icon.url }}<i class="fa fa-external-link-square margin-left-sm"></i></a>{% endif %}
      </small>
    </h1>
  </div>
</div>

<div class="container">
  <section>
    <div class="row">
      <div class="col-md-9 col-sm-9">
        <p>After you get <a href="{{ page.relative_path }}get-started/">up and running</a>, you can place Font Awesome icons just about anywhere with the <code>&lt;i&gt;</code> tag:</p>
        <div class="well well-transparent">
          <div style="font-size: 24px; line-height: 1.5em;">
            <i class="fa fa-{{ page.icon.id }}"></i> fa-{{ page.icon.id }}
          </div>
        </div>
{% highlight html %}
<i class="fa fa-{{ page.icon.id }}"></i>
{% endhighlight %}
        <br>
        <div class="lead"><i class="fa fa-info-sign"></i> Looking for more? Check out the <a href="{{ page.relative_path }}examples/">examples</a>.</div>
      </div>
      <div class="col-md-3 col-sm-3">
        <div class="vertical-ad">{% include ads/fusion.html %}</div>
      </div>
    </div>
  </section>
</div>

.padding-none         { padding: 0 !important; }
.padding              { padding: 10px !important; }
.padding-small        { padding: 5px !important; }
.padding-large        { padding: 22px !important; }

.padding-top-none     { padding-top: 0 !important; }
.padding-top          { padding-top: 10px !important; }
.padding-top-small    { padding-top: 5px !important; }
.padding-top-large    { padding-top: 22px !important; }

.padding-right-none   { padding-right: 0 !important; }
.padding-right        { padding-right: 10px !important; }
.padding-right-small  { padding-right: 5px !important; }
.padding-right-large  { padding-right: 22px !important; }

.padding-bottom-none  { padding-bottom: 0 !important; }
.padding-bottom       { padding-bottom: 10px !important; }
.padding-bottom-small { padding-bottom: 5px !important; }
.padding-bottom-large { padding-bottom: 22px !important; }

.padding-left-none    { padding-left: 0 !important; }
.padding-left         { padding-left: 10px !important; }
.padding-left-small   { padding-left: 5px !important; }
.padding-left-large   { padding-left: 22px !important; }

.margin-none          { margin: 0 !important; }
.margin               { margin: 10px !important; }
.margin-small         { margin: 5px !important; }
.margin-large         { margin: 22px !important; }

.margin-top-none      { margin-top: 0 !important; }
.margin-top           { margin-top: 10px !important; }
.margin-top-large     { margin-top: 22px !important; }
.margin-top-small     { margin-top: 5px !important; }

.margin-right-none    { margin-right: 0 !important; }
.margin-right         { margin-right: 10px !important; }
.margin-right-large   { margin-right: 22px !important; }
.margin-right-small   { margin-right: 5px !important; }

.margin-bottom-none   { margin-bottom: 0 !important; }
.margin-bottom        { margin-bottom: 10px !important; }
.margin-bottom-large  { margin-bottom: 22px !important; }
.margin-bottom-small  { margin-bottom: 5px !important; }

.margin-left-none     { margin-left: 0 !important; }
.margin-left          { margin-left: 10px !important; }
.margin-left-large    { margin-left: 22px !important; }
.margin-left-small    { margin-left: 5px !important; }

.border-left-none     { border-left: none !important; }
.border-right-none    { border-right: none !important; }
.border-bottom-none   { border-bottom: none !important; }
.border-top-none      { border-top: none !important; }

.display-block { display: block; } // use to swap an anchor tag to span a whole row to make click target larger
.no-underline:hover { text-decoration: none; }
.clickable { cursor: pointer; }
.strong { font-weight: bold; }
.em { font-style: italic; }
.small { font-size: 85%; } // Ex: 14px base font * 85% = about 12px
.no-link { color: @grayDark; }
.text-sans-serif { font-family: @sansFontFamily; }
.text-ellipsis { .text-overflow(); } // truncates text to a single line with an ellipsis at the end
.text-default {
  font-family: @sansFontFamily;
  font-size: @baseFontSize;
  line-height: @baseLineHeight;
  font-weight: normal;
}
.hilite {
  color: @linkColor;
  &:hover {
    color: @linkColorHover;
    text-decoration: underline;
  }
  [class^="icon-"], [class*=" icon-"] {
    &:before {
      display: inline;
    }
  }
}
.strike { text-decoration: line-through; }
.upper { text-transform: uppercase; }
.lower { text-transform: lowercase; }

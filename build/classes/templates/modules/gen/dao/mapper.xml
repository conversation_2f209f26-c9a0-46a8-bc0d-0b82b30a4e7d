<?xml version="1.0" encoding="utf-8"?>
<template>
	<name>mapper</name>
	<filePath>src/main/resources/mappings/${lastPackageName}/${moduleName}/${subModuleName}</filePath>
	<fileName>${ClassName}Dao.xml</fileName>
	<content><![CDATA[
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${packageName}.${moduleName}.dao<#if subModuleName != "">.${subModuleName}</#if>.${ClassName}Dao">
    
    <#-- 输出字段列 -->
	<sql id="${className}Columns">
		<#assign columnField>
			<#list table.columnList as c>
		a.${c.name} AS "${c.javaFieldId}",
			</#list>
			<#list table.columnList as c>
				<#if c.showType?? && c.showType == "userselect">
					<#list c.javaFieldAttrs as a>
		${c.simpleJavaField}.${a[1]} AS "${c.simpleJavaField}.${a[0]}",
					</#list>
				<#elseif c.showType?? && c.showType == "officeselect">
					<#list c.javaFieldAttrs as a>
		${c.simpleJavaField}.${a[1]} AS "${c.simpleJavaField}.${a[0]}",
					</#list>
				<#elseif c.showType?? && c.showType == "areaselect">
					<#list c.javaFieldAttrs as a>
		${c.simpleJavaField}.${a[1]} AS "${c.simpleJavaField}.${a[0]}",
					</#list>
				</#if>
				<#-- 父表关联字段 -->
				<#if table.parentExists && table.parentTableFk == c.name>
					<#list c.javaFieldAttrs as a>
		b.${a[1]} AS "${c.simpleJavaField}.${a[0]}",
					</#list>
				</#if>
			</#list>
		</#assign>
${columnField?substring(0, columnField?last_index_of(","))}
	</sql>
	
	<#-- 输出字段关联表 -->
	<sql id="${className}Joins">
		<#-- 关联父表 -->
		<#if table.parentExists>
		LEFT JOIN ${table.parent.name} b ON b.id = a.${table.parentTableFk}
		</#if>
		<#-- 关联系统表 -->
		<#list table.columnList as c>
			<#if c.showType?? && c.showType == "userselect">
		LEFT JOIN sys_user ${c.simpleJavaField} ON ${c.simpleJavaField}.id = a.${c.name}
			<#elseif c.showType?? && c.showType == "officeselect">
		LEFT JOIN sys_office ${c.simpleJavaField} ON ${c.simpleJavaField}.id = a.${c.name}
			<#elseif c.showType?? && c.showType == "areaselect">
		LEFT JOIN sys_area ${c.simpleJavaField} ON ${c.simpleJavaField}.id = a.${c.name}
			</#if>
		</#list>
	</sql>
    
	<select id="get" resultType="${ClassName}">
		SELECT 
			<include refid="${className}Columns"/>
		FROM ${table.name} a
		<include refid="${className}Joins"/>
		WHERE a.id = ${"#"}{id}
	</select>
	
	<select id="findList" resultType="${ClassName}">
		SELECT 
			<include refid="${className}Columns"/>
		FROM ${table.name} a
		<include refid="${className}Joins"/>
		<where>
			<#if table.delFlagExists>a.del_flag = ${"#"}{DEL_FLAG_NORMAL}</#if>
			<#list table.columnList as c>
				<#if (c.isQuery?? && c.isQuery == "1") || (table.parentExists && table.parentTableFk == c.name)>
					<#if c.queryType ?? && c.queryType == 'between'>
			<if test="begin${c.simpleJavaField?cap_first} != null and end${c.simpleJavaField?cap_first} != null <#if c.simpleJavaField != c.javaFieldId>and begin${c.javaFieldId?cap_first} != null and end${c.javaFieldId?cap_first} != null </#if>and begin${c.javaFieldId?cap_first} != '' and end${c.javaFieldId?cap_first} != ''">
					<#else>
			<if test="${c.simpleJavaField} != null<#if c.simpleJavaField != c.javaFieldId> and ${c.javaFieldId} != null</#if> and ${c.javaFieldId} != ''">
					</#if>
					<#if c.queryType ?? && c.queryType == 'between'>
				AND a.${c.name} BETWEEN ${"#"}{begin${c.simpleJavaField?cap_first}} AND ${"#"}{end${c.simpleJavaField?cap_first}}
					<#elseif c.queryType ?? && c.queryType == 'like'>
				AND a.${c.name} LIKE 
					<if test="dbName == 'oracle'">'%'||${"#"}{${c.javaFieldId}}||'%'</if>
					<if test="dbName == 'mssql'">'%'+${"#"}{${c.javaFieldId}}+'%'</if>
					<if test="dbName == 'mysql'">concat('%',${"#"}{${c.javaFieldId}},'%')</if>
					<#elseif c.queryType ?? && c.queryType == 'left_like'>
				AND a.${c.name} LIKE 
					<if test="dbName == 'oracle'">'%'||${"#"}{${c.javaFieldId}}</if>
					<if test="dbName == 'mssql'">'%'+${"#"}{${c.javaFieldId}}</if>
					<if test="dbName == 'mysql'">concat('%',${"#"}{${c.javaFieldId}})</if>
					<#elseif c.queryType ?? && c.queryType == 'right_like'>
				AND a.${c.name} LIKE 
					<if test="dbName == 'oracle'">${"#"}{${c.javaFieldId}}||'%'</if>
					<if test="dbName == 'mssql'">${"#"}{${c.javaFieldId}}+'%'</if>
					<if test="dbName == 'mysql'">concat(${"#"}{${c.javaFieldId}},'%')</if>
					<#else>
				AND a.${c.name} ${c.queryType} ${"#"}{${c.javaFieldId}}
					</#if>
			</if>
				</#if>
			</#list>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${"$"}{page.orderBy}
			</when>
			<otherwise>
				<#if table.parentExists>
					<#if table.createDateExists>
				ORDER BY a.create_date ASC
					</#if>
				<#else>
					<#if table.updateDateExists>
				ORDER BY a.update_date DESC
					</#if>
				</#if>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="${ClassName}">
		SELECT 
			<include refid="${className}Columns"/>
		FROM ${table.name} a
		<include refid="${className}Joins"/>
		<where>
			<#if table.delFlagExists>a.del_flag = ${"#"}{DEL_FLAG_NORMAL}</#if>
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${"$"}{page.orderBy}
			</when>
			<otherwise>
				<#if table.parentExists>
					<#if table.createDateExists>
				ORDER BY a.create_date ASC
					</#if>
				<#else>
					<#if table.updateDateExists>
				ORDER BY a.update_date DESC
					</#if>
				</#if>
			</otherwise>
		</choose>
	</select>
	
	<insert id="insert">
		INSERT INTO ${table.name}(
		<#assign insertField>
			<#list table.columnList as c>
				<#if c.isInsert?? && c.isInsert == "1">
			${c.name},
				</#if>
			</#list>
		</#assign>
${insertField?substring(0, insertField?last_index_of(","))}
		) VALUES (
		<#assign insertJavaField>
			<#list table.columnList as c>
				<#if c.isInsert?? && c.isInsert == "1">
			${"#"}{${c.javaFieldId}},
				</#if>
			</#list>
		</#assign>
${insertJavaField?substring(0, insertJavaField?last_index_of(","))}
		)
	</insert>
	
	<update id="update">
		UPDATE ${table.name} SET 	
			<#assign updateField>		
				<#list table.columnList as c>
					<#if c.isEdit?? && c.isEdit == "1">
			${c.name} = ${"#"}{${c.javaFieldId}},
					</#if>
				</#list>
			</#assign>
${updateField?substring(0, updateField?last_index_of(","))}
		WHERE id = ${"#"}{id}
	</update>
	
	
	<!--物理删除-->
	<update id="delete">
		DELETE FROM ${table.name}
	<#if table.parentExists>
		<#list table.columnList as c>
			<#if table.parentTableFk == c.name>
		<choose>
			<when test="id !=null and id != ''">
				WHERE id = ${"#"}{id}
			</when>
			<otherwise>
				WHERE ${table.parentTableFk} = ${"#"}{${c.javaFieldId}}
			</otherwise>
		</choose>
			</#if>
		</#list>
	<#else>
		WHERE id = ${"#"}{id}
	</#if>
	</update>
	
	<!--逻辑删除-->
	<update id="deleteByLogic">
		UPDATE ${table.name} SET 
			del_flag = ${"#"}{DEL_FLAG_DELETE}
	<#if table.parentExists>
		<#list table.columnList as c>
			<#if table.parentTableFk == c.name>
		<choose>
			<when test="id !=null and id != ''">
				WHERE id = ${"#"}{id}
			</when>
			<otherwise>
				WHERE ${table.parentTableFk} = ${"#"}{${c.javaFieldId}}
			</otherwise>
		</choose>
			</#if>
		</#list>
	<#else>
		WHERE id = ${"#"}{id}
	</#if>
	</update>
	
	
	<!-- 根据实体名称和字段名称和字段值获取唯一记录 -->
	<select id="findUniqueByProperty" resultType="${ClassName}" statementType="STATEMENT">
		select * FROM ${table.name}  where ${"$"}{propertyName} = #{value}
	</select>
</mapper>]]>
	</content>
</template>
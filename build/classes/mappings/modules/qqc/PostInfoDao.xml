<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.qqc.dao.PostInfoDao">
    
	<sql id="postInfoColumns">
		p.id as "id",
		p.post_no as "postNo",
		p.company_id as "companyId",
		c.company_name as "companyName",
		p.post_name as "postName",
		p.post_type as "postType",
		p.place_id as "placeId",
		s.name as "placeName",
		p.post_demand as "postDemand",
		p.people_num as "peopleNum",
		p.salary_min as "salaryMin",
		p.salary_max as "salaryMax",
		p.welfare as "welfare",
		p.more_welfare as moreWelfare,
		p.status as "status",
		p.remarks,
		p.post_group as "postGroup"
	</sql>
	
	<sql id="postInfoJoins">
		p.id as "id",
		p.post_no as "postNo",
		p.company_id as "companyId",
		c.company_name as "companyName",
		p.post_name as "postName",
		p.post_type as "postType",
		s.name as "placeName",
		p.place_id as "placeId",
		p.post_demand as "postDemand",
		p.people_num as "peopleNum",
		p.salary_min as "salaryMin",
		p.salary_max as "salaryMax",
		p.welfare as "welfare",
		p.more_welfare as moreWelfare,
		p.status as "status",
		p.company_name 'companyName2',
		p.contact_name 'contactName',
		p.contact_pho 'contactPho',
		p.hr_email 'hrEmail',
		p.post_group as "postGroup"
	</sql>
	
	<select id="get" resultType="PostInfo">
		SELECT 
			<include refid="postInfoJoins"/>
		FROM qqc_post_info p 
		left join qqc_company_info c
		on p.company_id = c.id
		left join sys_area s on p.place_id = s.id
		WHERE p.id = #{id}
	</select>
	
	<select id="findPostList" resultType="PostInfo">
		SELECT 
			<include refid="postInfoJoins"/>
		FROM qqc_post_info p 
		left join qqc_company_info c on p.company_id = c.id
		left join sys_area s on p.place_id = s.id
		where p.del_flag = 0
		<if test = "postName != null and postName != ''">
			AND p.post_name like CONCAT('%', #{postName}, '%')
		</if>
		<if test = "companyName != null and companyName != ''">
			AND c.company_name like CONCAT('%', #{companyName}, '%')
		</if>
		<if test="placeId != null and placeId != ''">
			AND  p.place_id LIKE CONCAT('%', #{placeId}, '%')
		</if>
		<if test = "postGroup != null and postGroup != ''">
			AND p.post_group like CONCAT('%', #{postGroup}, '%')
		</if>
		<if test="salaryMin != null and salaryMin != ''">
			<if test="salaryMax != null and salaryMax != ''">
				AND CAST(#{salaryMin} AS SIGNED) BETWEEN CAST(p.salary_min AS SIGNED) AND CAST(p.salary_max AS SIGNED)
				OR CAST(#{salaryMax} AS SIGNED) BETWEEN CAST(p.salary_min AS SIGNED) AND CAST(p.salary_max AS SIGNED)
			</if>
			<if test="salaryMax == null or salaryMax == ''">
				AND CAST(#{salaryMin} AS SIGNED) BETWEEN CAST(p.salary_min AS SIGNED) AND CAST(p.salary_max AS SIGNED)
			</if>
		</if>
		<if test="salaryMin == null or salaryMin == ''">
			<if test="salaryMax != null and salaryMax != ''">
				AND CAST(#{salaryMax} AS SIGNED) BETWEEN CAST(p.salary_min AS SIGNED) AND CAST(p.salary_max AS SIGNED)
			</if>
		</if>
		ORDER BY p.create_date desc
	</select>
	
	<insert id="insert">
		insert into qqc_post_info(
			id,
			post_no,
			company_id,
			post_name,
			post_type,
			place_id,
			post_demand,
			people_num,
			salary_min,
			salary_max,
			welfare,
			more_welfare,
			status,
			create_by,
			create_date,
			remarks,
			del_flag,
			post_group
		)VALUES(
			#{id},
			#{postNo},
			#{companyId},
			#{postName},
			#{postType},
			#{placeId},
			#{postDemand},
			#{peopleNum},
			#{salaryMin},
			#{salaryMax},
			#{welfare},
			#{moreWelfare},
			#{status},
			#{createBy.id},
			#{createDate},
			#{remarks},
			#{delFlag},
			#{postGroup}
		)
	</insert>

	<update id="update">
		update qqc_post_info p
			set
			p.post_name = #{postName},
			p.post_type = #{postType},
			p.place_id = #{placeId},
			p.post_demand = #{postDemand},
			p.people_num = #{peopleNum},
			p.salary_min = #{salaryMin},
			p.salary_max = #{salaryMax},
			p.welfare = #{welfare},
			p.more_welfare = #{moreWelfare},
			p.update_by = #{updateBy.id},
			p.update_date = #{updateDate},
			p.remarks = #{remarks},
			p.post_group = #{postGroup}
		where p.id = #{Id}
	</update>

	<update id="deleteByCompanyId">
		update qqc_post_info p
		set p.del_flag = #{DEL_FLAG_DELETE}
		where p.company_id = #{id}
	</update>
	
	<delete id="delete">
		update qqc_post_info p
		set p.del_flag = #{DEL_FLAG_DELETE}
		where p.id = #{id}
	</delete>

	<insert id="saveApplication">
		insert into qqc_post_application(
			id,
			company_id,
			post_id,
			application_id,
			resume_id,
			status,
			create_by,
			create_date,
			del_flag
		)value (
			#{id},
			#{companyId},
			#{postId},
			#{applicationId},
		    #{resumeId},
			#{status},
			#{createBy.id},
			#{createDate},
			0
		)
	</insert>

	<update id="updateStatus">
		update qqc_post_info
		set status = #{status}
		where id = #{id}
	</update>

    <select id="findMyPosts" resultType="PostInfo">
        select
            <include refid="postInfoColumns"/>
        from qqc_post_info p
        left join qqc_company_info c on p.company_id = c.id
        left join sys_area s on p.place_id = s.id
        where p.del_flag = 0
        and c.certified_sponsor = #{userId}
        <if test="postName != null and postName != ''">
            AND p.post_name like CONCAT('%', #{postName}, '%')
        </if>
        <if test="postType != null and postType != ''">
            and p.post_type = #{postType}
        </if>
        ORDER BY p.create_date desc
    </select>

	<update id="updateCompanyId">
		update qqc_post_info
			set company_id = #{nid}
			where company_id = #{oid}
	</update>

	<select id="findPostListV2" resultType="PostInfo">
		SELECT
		<include refid="postInfoJoins"/>
		FROM qqc_post_info p
		left join qqc_company_info c on p.company_id = c.id
		left join sys_area s on p.place_id = s.id
		where p.del_flag = 0
		<if test = "postName != null and postName != ''">
			AND p.post_name like CONCAT('%', #{postName}, '%')
		</if>
		<if test = "companyName != null and companyName != ''">
			AND c.company_name like CONCAT('%', #{companyName}, '%')
		</if>
		<if test="placeId != null and placeId != ''">
			AND  p.place_id LIKE CONCAT('%', #{placeId}, '%')
		</if>
		<if test="salaryMin != null and salaryMin != ''">
			<if test="salaryMax != null and salaryMax != ''">
				AND CAST(#{salaryMin} AS SIGNED) BETWEEN CAST(p.salary_min AS SIGNED) AND CAST(p.salary_max AS SIGNED)
				OR CAST(#{salaryMax} AS SIGNED) BETWEEN CAST(p.salary_min AS SIGNED) AND CAST(p.salary_max AS SIGNED)
			</if>
			<if test="salaryMax == null or salaryMax == ''">
				AND CAST(#{salaryMin} AS SIGNED) BETWEEN CAST(p.salary_min AS SIGNED) AND CAST(p.salary_max AS SIGNED)
			</if>
		</if>
		<if test="salaryMin == null or salaryMin == ''">
			<if test="salaryMax != null and salaryMax != ''">
				AND CAST(#{salaryMax} AS SIGNED) BETWEEN CAST(p.salary_min AS SIGNED) AND CAST(p.salary_max AS SIGNED)
			</if>
		</if>
		<choose>
			<when test="postType!=null and postType!='' ">
					AND p.post_type=#{postType}
			</when>
			<when test="postType==7">
				AND p.post_type=7
			</when>
			<otherwise>
					AND (p.post_type=5 OR p.post_type=6)
			</otherwise>
		</choose>
		ORDER BY p.post_no DESC,p.create_date DESC
	</select>



	<insert id="insertBatch">
		insert into qqc_post_info(
			id,
			post_no,
			company_id,
			post_name,
			post_type,
			place_id,
			post_demand,
			people_num,
			salary_min,
			salary_max,
			welfare,
			more_welfare,
			status,
			create_by,
			create_date,
			remarks,
			del_flag,
		contact_name,
		contact_pho,
		hr_email,
		company_name,
		post_group
		)VALUES

		<foreach collection="list" separator="," item="item">
			(
			#{item.id},
			#{item.postNo},
			#{item.companyId},
			#{item.postName},
			#{item.postType},
			#{item.placeId},
			#{item.postDemand},
			#{item.peopleNum},
			#{item.salaryMin},
			#{item.salaryMax},
			#{item.welfare},
			#{item.moreWelfare},
			#{item.status},
			#{item.createBy.id},
			#{item.createDate},
			#{item.remarks},
			#{item.delFlag},
			#{item.contactName},
			#{item.contactPho},
			#{item.hrEmail},
			#{item.companyName},
			#{item.postGroup}
			)
		</foreach>


	</insert>


	<select id="getCountByCompanyNameAndPostName" resultType="java.lang.Integer">
	 	SELECT
	COUNT(*)
FROM
	qqc_post_info t1
	LEFT JOIN qqc_company_info t2 ON t1.company_id = t2.id
WHERE
	(
	t1.company_name = #{companyName}
	OR t2.company_name =#{companyName})
	AND t1.post_name=#{postName}
	AND t1.del_flag='0'
	</select>
	
	<select id="getPostGroup" resultType="Map">
	 	SELECT
			post_group as "postGroup",
			sum( people_num + 0 ) as "num"
		FROM
			qqc_post_info 
		WHERE
			del_flag = 0 
			AND post_type = #{postType}
		GROUP BY
			post_group 
		ORDER BY
			create_date
	</select>
	
	
	<select id="getOpenPost" resultType="Map">
	 	SELECT 
		a.id as id,
		a.post_no as  job_code,
		a.salary_min as salary_min,
		a.salary_max as salary_max,
		a.status as active,
		a.create_date as create_date,
		a.company_name as company,
		a.post_name as position,
		a.place_id as city_id,
		c.address  as address,
		a.contact_name  as name,
		a.update_date as update_date
		FROM qqc_post_info a
		left join qqc_company_info c
		on a.company_id = c.id
		WHERE a.del_flag = 0
	</select>
	
	<select id="getOpenPostGW" resultType="Map">
	 		SELECT 
				id,
				update_date,
				update_by,
				create_by,
				create_date,
				status,
				more_welfare,
				welfare,
				salary_min,
				salary_max,
				people_num,
				post_demand,
				place_id,
				post_type,
				post_name,
				company_id,
				post_no,
				remarks
		FROM qqc_post_info 
		WHERE del_flag = 0
	</select>
	

</mapper>
<?xml version="1.0" encoding="utf-8"?>
<template>
	<name>viewForm</name>
	<filePath>src/main/webapp/webpage/${lastPackageName}/${moduleName}/${subModuleName}</filePath>
	<fileName>${className}Form.jsp</fileName>
	<content><![CDATA[
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>${functionNameSimple}管理</title>
	<meta name="decorator" content="default"/>
	<script type="text/javascript">
		var validateForm;
		function doSubmit(){//回调函数，在编辑和保存动作时，供openDialog调用提交表单。
		  if(validateForm.form()){
			  $("#inputForm").submit();
			  return true;
		  }
	
		  return false;
		}
		$(document).ready(function() {
			validateForm = $("#inputForm").validate({
				submitHandler: function(form){
					loading('正在提交，请稍等...');
					form.submit();
				},
				errorContainer: "#messageBox",
				errorPlacement: function(error, element) {
					$("#messageBox").text("输入有误，请先更正。");
					if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
						error.appendTo(element.parent().parent());
					} else {
						error.insertAfter(element);
					}
				}
			});
			
			<#list table.columnList as c>
				<#if c.isForm?? && c.isForm == "1" && (c.isNotBaseField || c.simpleJavaField == 'remarks')>
				<#if c.showType == "dateselect">
					laydate({
			            elem: '#${c.javaFieldId}', //目标元素。由于laydate.js封装了一个轻量级的选择器引擎，因此elem还允许你传入class、tag但必须按照这种方式 '#id .class'
			            event: 'focus' //响应事件。如果没有传入event，则按照默认的click
			        });
				</#if>
				</#if>
			</#list>
		});
	<#list table.childList as c>
		function addRow(list, idx, tpl, row){
			$(list).append(Mustache.render(tpl, {
				idx: idx, delBtn: true, row: row
			}));
			$(list+idx).find("select").each(function(){
				$(this).val($(this).attr("data-value"));
			});
			$(list+idx).find("input[type='checkbox'], input[type='radio']").each(function(){
				var ss = $(this).attr("data-value").split(',');
				for (var i=0; i<ss.length; i++){
					if($(this).val() == ss[i]){
						$(this).attr("checked","checked");
					}
				}
			});
		}
		function delRow(obj, prefix){
			var id = $(prefix+"_id");
			var delFlag = $(prefix+"_delFlag");
			if (id.val() == ""){
				$(obj).parent().parent().remove();
			}else if(delFlag.val() == "0"){
				delFlag.val("1");
				$(obj).html("&divide;").attr("title", "撤销删除");
				$(obj).parent().parent().addClass("error");
			}else if(delFlag.val() == "1"){
				delFlag.val("0");
				$(obj).html("&times;").attr("title", "删除");
				$(obj).parent().parent().removeClass("error");
			}
		}
		<#break/>
	</#list>
	</script>
</head>
<body>
	<form:form id="inputForm" modelAttribute="${className}" action="${r"${ctx}"}/${urlPrefix}/save" method="post" class="form-horizontal">
		<form:hidden path="id"/>
		<sys:message content="${r"${message}"}"/>	
		<table class="table table-bordered  table-condensed dataTables-example dataTable no-footer">
		   <tbody>
		<#assign x=0 />
		<#list table.columnList as c>
			<#if c.isForm?? && c.isForm == "1" && (c.isNotBaseField || c.simpleJavaField == 'remarks')>
			 <#assign x=x+1 />
			<#if x%2==1> 
				<tr>
			</#if>
					<td class="width-15 active"><label class="pull-right"><#if c.isNull != "1"><font color="red">*</font></#if>${c.comments}：</label></td>
					<td class="width-35">
			<#if c.showType == "input">
						<form:input path="${c.javaFieldId}" htmlEscape="false"<#if c.dataLength != "0"> maxlength="${c.dataLength}"</#if> class="form-control <#if c.isNull != "1">required</#if><#if c.javaType == "Long" || c.javaType == "Integer"> digits</#if><#if c.javaType == "Double"> number</#if>"/>
			<#elseif c.showType == "textarea">
						<form:textarea path="${c.javaFieldId}" htmlEscape="false" rows="4"<#if c.dataLength != "0"> maxlength="${c.dataLength}"</#if> class="form-control <#if c.isNull != "1">required</#if>"/>
			<#elseif c.showType == "select">
						<form:select path="${c.javaFieldId}" class="form-control <#if c.isNull != "1">required</#if>">
							<form:option value="" label=""/>
							<form:options items="${"$"}{fns:getDictList('${c.dictType}')}" itemLabel="label" itemValue="value" htmlEscape="false"/>
						</form:select>
			<#elseif c.showType == "checkbox">
						<form:checkboxes path="${c.javaFieldId}" items="${"$"}{fns:getDictList('${c.dictType}')}" itemLabel="label" itemValue="value" htmlEscape="false" class="i-checks <#if c.isNull != "1">required</#if>"/>
			<#elseif c.showType == "radiobox">
						<form:radiobuttons path="${c.javaFieldId}" items="${"$"}{fns:getDictList('${c.dictType}')}" itemLabel="label" itemValue="value" htmlEscape="false" class="i-checks <#if c.isNull != "1">required</#if>"/>
			<#elseif c.showType == "dateselect">
						<input id="${c.javaFieldId}" name="${c.javaFieldId}" type="text" maxlength="20" class="laydate-icon form-control layer-date <#if c.isNull != "1">required</#if>"
							value="<fmt:formatDate value="${"$"}{${className}.${c.javaFieldId}}" pattern="yyyy-MM-dd HH:mm:ss"/>"/>
			<#elseif c.showType == "userselect">
						<sys:treeselect id="${c.simpleJavaField}" name="${c.javaFieldId}" value="${"$"}{${className}.${c.javaFieldId}}" labelName="${c.javaFieldName}" labelValue="${"$"}{${className}.${c.javaFieldName}}"
							title="用户" url="/sys/office/treeData?type=3" cssClass="form-control <#if c.isNull != "1">required</#if>" allowClear="true" notAllowSelectParent="true"/>
			<#elseif c.showType == "officeselect">
						<sys:treeselect id="${c.simpleJavaField}" name="${c.javaFieldId}" value="${"$"}{${className}.${c.javaFieldId}}" labelName="${c.javaFieldName}" labelValue="${"$"}{${className}.${c.javaFieldName}}"
							title="部门" url="/sys/office/treeData?type=2" cssClass="form-control <#if c.isNull != "1">required</#if>" allowClear="true" notAllowSelectParent="true"/>
			<#elseif c.showType == "areaselect">
						<sys:treeselect id="${c.simpleJavaField}" name="${c.javaFieldId}" value="${"$"}{${className}.${c.javaFieldId}}" labelName="${c.javaFieldName}" labelValue="${"$"}{${className}.${c.javaFieldName}}"
							title="区域" url="/sys/area/treeData" cssClass="form-control <#if c.isNull != "1">required</#if>" allowClear="true" notAllowSelectParent="true"/>
			<#elseif c.showType == "fileselect">
						<form:hidden id="${c.simpleJavaField}" path="${c.javaFieldId}" htmlEscape="false"<#if c.dataLength != "0"> maxlength="${c.dataLength}"</#if> class="form-control"/>
						<sys:ckfinder input="${c.simpleJavaField}" type="files" uploadPath="/${moduleName}<#if subModuleName != "">/${subModuleName}</#if>/${className}" selectMultiple="true"/>
			</#if>
					</td>
			<#if x%2==0> 
				</tr>
			</#if>
			</#if>
		</#list>
		<#if x%2==1 > 
					<td class="width-15 active"></td>
		   			<td class="width-35" ></td>
		  		</tr>
		</#if>
		 	</tbody>
		</table>
		
		<#assign y=0 />
		<div class="tabs-container">
            <ul class="nav nav-tabs">
        <#list table.childList as child>
         <#assign y=y+1 />
			<#if y==1> 
				<li class="active"><a data-toggle="tab" href="#tab-${y}" aria-expanded="true">${child.comments}：</a>
                </li>
			</#if>
			<#if y!=1> 
				<li class=""><a data-toggle="tab" href="#tab-${y}" aria-expanded="false">${child.comments}：</a>
                </li>
            </#if>    
         </#list>
            </ul>
            <div class="tab-content">
        <#assign z=0 />
		<#list table.childList as child>
			<#assign z=z+1 />
			<#if z==1> 
				<div id="tab-${z}" class="tab-pane active">
			</#if>
			<#if z!=1> 
				<div id="tab-${z}" class="tab-pane">
			</#if>
			<a class="btn btn-white btn-sm" onclick="addRow('#${child.className?uncap_first}List', ${child.className?uncap_first}RowIdx, ${child.className?uncap_first}Tpl);${child.className?uncap_first}RowIdx = ${child.className?uncap_first}RowIdx + 1;" title="新增"><i class="fa fa-plus"></i> 新增</a>
			<table id="contentTable" class="table table-striped table-bordered table-condensed">
				<thead>
					<tr>
						<th class="hide"></th>
						<#assign childColumnCount = 0 /><#list child.columnList as c>
							<#if c.isForm?? && c.isForm == "1" && (c.isNotBaseField || c.simpleJavaField == 'remarks') && c.name != c.genTable.parentTableFk>
						<th>${c.comments}</th><#assign childColumnCount = childColumnCount + 1 />
							</#if>
						</#list>
						<th width="10">&nbsp;</th>
					</tr>
				</thead>
				<tbody id="${child.className?uncap_first}List">
				</tbody>
			</table>
			<script type="text/template" id="${child.className?uncap_first}Tpl">//<!--
				<tr id="${child.className?uncap_first}List{{idx}}">
					<td class="hide">
						<input id="${child.className?uncap_first}List{{idx}}_id" name="${child.className?uncap_first}List[{{idx}}].id" type="hidden" value="{{row.id}}"/>
						<input id="${child.className?uncap_first}List{{idx}}_delFlag" name="${child.className?uncap_first}List[{{idx}}].delFlag" type="hidden" value="0"/>
					</td>
					<#list child.columnList as c>
						<#if c.isForm?? && c.isForm == "1" && (c.isNotBaseField || c.simpleJavaField == 'remarks') && c.name != c.genTable.parentTableFk>
					
					<#if c.showType == "input">
					<td>
						<input id="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}" name="${child.className?uncap_first}List[{{idx}}].${c.javaFieldId}" type="text" value="{{row.${c.javaFieldId}}}"<#if c.dataLength != "0"> maxlength="${c.dataLength}"</#if> class="form-control <#if c.isNull != "1">required</#if><#if c.javaType == "Long" || c.javaType == "Integer"> digits</#if><#if c.javaType == "Double"> number</#if>"/>
					</td>
					<#elseif c.showType == "textarea">
					<td>
						<textarea id="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}" name="${child.className?uncap_first}List[{{idx}}].${c.javaFieldId}" rows="4"<#if c.dataLength != "0"> maxlength="${c.dataLength}"</#if> class="form-control <#if c.isNull != "1">required</#if>">{{row.${c.javaFieldId}}}</textarea>
					</td>
					<#elseif c.showType == "select">
					<td>
						<select id="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}" name="${child.className?uncap_first}List[{{idx}}].${c.javaFieldId}" data-value="{{row.${c.javaFieldId}}}" class="form-control m-b  <#if c.isNull != "1">required</#if>">
							<option value=""></option>
							<c:forEach items="${"$"}{fns:getDictList('${c.dictType}')}" var="dict">
								<option value="${"$"}{dict.value}">${"$"}{dict.label}</option>
							</c:forEach>
						</select>
					</td>
					<#elseif c.showType == "checkbox">
					<td>
						<c:forEach items="${"$"}{fns:getDictList('${c.dictType}')}" var="dict" varStatus="dictStatus">
							<span><input id="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}${"$"}{dictStatus.index}" name="${child.className?uncap_first}List[{{idx}}].${c.javaFieldId}" type="checkbox" class="i-checks" value="${"$"}{dict.value}" data-value="{{row.${c.javaFieldId}}}"><label for="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}${"$"}{dictStatus.index}">${"$"}{dict.label}</label></span>
						</c:forEach>
					</td>
					<#elseif c.showType == "radiobox">
					<td>
						<c:forEach items="${"$"}{fns:getDictList('${c.dictType}')}" var="dict" varStatus="dictStatus">
							<span><input id="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}${"$"}{dictStatus.index}" name="${child.className?uncap_first}List[{{idx}}].${c.javaFieldId}" type="radio" class="i-checks" value="${"$"}{dict.value}" data-value="{{row.${c.javaFieldId}}}"><label for="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}${"$"}{dictStatus.index}">${"$"}{dict.label}</label></span>
						</c:forEach>
					</td>
					<#elseif c.showType == "dateselect">
					<td>
						<input id="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}" name="${child.className?uncap_first}List[{{idx}}].${c.javaFieldId}" type="text" readonly="readonly" maxlength="20" class="laydate-icon form-control layer-date  <#if c.isNull != "1">required</#if>"
							value="{{row.${c.javaFieldId}}}" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',isShowClear:false});"/>
					</td>
					<#elseif c.showType == "userselect">
					<td  class="max-width-250">
						<sys:treeselect id="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}" name="${child.className?uncap_first}List[{{idx}}].${c.javaFieldId}" value="{{row.${c.javaFieldId}}}" labelName="${child.className?uncap_first}List{{idx}}.${c.javaFieldName}" labelValue="{{row.${c.javaFieldName}}}"
							title="用户" url="/sys/office/treeData?type=3" cssClass="form-control  <#if c.isNull != "1">required</#if>" allowClear="true" notAllowSelectParent="true"/>
					</td>
					<#elseif c.showType == "officeselect">
					<td  class="max-width-250">
						<sys:treeselect id="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}" name="${child.className?uncap_first}List[{{idx}}].${c.javaFieldId}" value="{{row.${c.javaFieldId}}}" labelName="${child.className?uncap_first}List{{idx}}.${c.javaFieldName}" labelValue="{{row.${c.javaFieldName}}}"
							title="部门" url="/sys/office/treeData?type=2" cssClass="form-control  <#if c.isNull != "1">required</#if>" allowClear="true" notAllowSelectParent="true"/>
					</td>
					<#elseif c.showType == "areaselect">
					<td  class="max-width-250">
						<sys:treeselect id="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}" name="${child.className?uncap_first}List[{{idx}}].${c.javaFieldId}" value="{{row.${c.javaFieldId}}}" labelName="${child.className?uncap_first}List{{idx}}.${c.javaFieldName}" labelValue="{{row.${c.javaFieldName}}}"
							title="区域" url="/sys/area/treeData" cssClass="form-control  <#if c.isNull != "1">required</#if>" allowClear="true" notAllowSelectParent="true"/>
					</td>
					<#elseif c.showType == "fileselect">
					<td>
						<input id="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}" name="${child.className?uncap_first}List[{{idx}}].${c.simpleJavaField}" type="hidden" value="{{row.${c.javaFieldId}}}"<#if c.dataLength != "0"> maxlength="${c.dataLength}"</#if> class="form-control"/>
						<sys:ckfinder input="${child.className?uncap_first}List{{idx}}_${c.simpleJavaField}" type="files" uploadPath="/${moduleName}<#if subModuleName != "">/${subModuleName}</#if>/${className}" selectMultiple="true"/>
					</td>
					</#if>
					
						</#if>
					</#list>
					<td class="text-center" width="10">
						{{#delBtn}}<span class="close" onclick="delRow(this, '#${child.className?uncap_first}List{{idx}}')" title="删除">&times;</span>{{/delBtn}}
					</td>
				</tr>//-->
			</script>
			<script type="text/javascript">
				var ${child.className?uncap_first}RowIdx = 0, ${child.className?uncap_first}Tpl = $("#${child.className?uncap_first}Tpl").html().replace(/(\/\/\<!\-\-)|(\/\/\-\->)/g,"");
				$(document).ready(function() {
					var data = ${"$"}{fns:toJson(${className}.${child.className?uncap_first}List)};
					for (var i=0; i<data.length; i++){
						addRow('#${child.className?uncap_first}List', ${child.className?uncap_first}RowIdx, ${child.className?uncap_first}Tpl, data[i]);
						${child.className?uncap_first}RowIdx = ${child.className?uncap_first}RowIdx + 1;
					}
				});
			</script>
			</div>
		</#list>
		</div>
		</div>
	</form:form>
</body>
</html>]]>
	</content>
</template>
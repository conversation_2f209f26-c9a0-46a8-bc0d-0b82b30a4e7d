<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.sys.dao.MenuDao">
	
     <resultMap id="menuResult" type="Menu">
		<id property="id" column="id" />
		<result property="uid" column="u_id" />
		<result property="parentIds" column="parentIds" />
		<result property="name" column="name" />
		<result property="href" column="href" />
		<result property="target" column="target" />
		<result property="icon" column="icon" />
		<result property="sort" column="sort" />
		<result property="isShow" column="isShow" />
		<result property="permission" column="permission" />
		<!-- 查询父模块-->
        <association property="parent" column="parent_id" select="getParent" />   
          
        <!-- 查询子模块 -->  
        <collection property="children" column="id" select="getChildren" />  
	</resultMap>
    
	<sql id="menuColumns">
		a.id,
		a.parent_id AS "parent.id",
		a.parent_ids,
		a.name,
		a.href,
		a.target,
		a.icon,
		a.sort,
		a.is_show,
		a.permission,
		a.remarks,
		a.create_by AS "createBy.id",
		a.create_date,
		a.update_by AS "updateBy.id",
		a.update_date,
		a.del_flag,
		p.name AS "parent.name",
		a.u_id AS "uid"
	</sql>
	
	<sql id="menuJoins">
		LEFT JOIN sys_menu p ON p.id = a.parent_id
    </sql>
	
	<select id="get"  resultMap="menuResult">
		SELECT
			<include refid="menuColumns"/>
		FROM sys_menu a
		<include refid="menuJoins"/>
		WHERE a.id = #{id}
	</select>
	<select id="findUniqueByProperty"  resultMap="menuResult" statementType="STATEMENT">
		select * from sys_menu where ${propertyName} = ${value}
	</select>
	
	<select id="getChildren" parameterType="String" resultMap="menuResult">  
        select * from sys_menu where parent_id = #{id} ORDER BY sort 
    </select>  
    <select id="getParent" parameterType="String" resultMap="menuResult">  
        select * from sys_menu where id = #{id}  
    </select>  
	<select id="findAllList" resultMap="menuResult">
		SELECT
			<include refid="menuColumns"/>
		FROM sys_menu a
		<include refid="menuJoins"/>
		WHERE a.del_flag = #{DEL_FLAG_NORMAL}
		ORDER BY a.sort
	</select>
	
	<select id="findByParentIdsLike"  resultMap="menuResult">
		SELECT
			a.id,
			a.parent_id AS "parent.id",
			a.parent_ids
		FROM sys_menu a
		WHERE a.del_flag = #{DEL_FLAG_NORMAL} AND a.parent_ids LIKE #{parentIds}
		ORDER BY a.sort
	</select>
	
	<select id="findByUserId"  resultMap="menuResult">
		SELECT DISTINCT
			<include refid="menuColumns"/>
		FROM sys_menu a
		LEFT JOIN sys_menu p ON p.id = a.parent_id
		JOIN sys_role_menu rm ON rm.menu_id = a.id
		JOIN sys_role r ON r.id = rm.role_id AND r.useable='1'
		JOIN sys_user_role ur ON ur.role_id = r.id
		JOIN sys_user u ON u.id = ur.user_id AND u.id = #{userId}
		WHERE a.del_flag = #{DEL_FLAG_NORMAL} AND r.del_flag = #{DEL_FLAG_NORMAL} AND u.del_flag = #{DEL_FLAG_NORMAL} 
		ORDER BY a.sort
	</select>
	
	<insert id="insert">
		INSERT INTO sys_menu(
			id, 
			parent_id, 
			parent_ids, 
			name, 
			href, 
			target, 
			icon, 
			sort, 
			is_show, 
			permission, 
			create_by, 
			create_date, 
			update_by, 
			update_date, 
			remarks, 
			del_flag,
			u_id
		) VALUES (
			#{id}, 
			#{parent.id}, 
			#{parentIds}, 
			#{name}, 
			#{href}, 
			#{target}, 
			#{icon}, 
			#{sort}, 
			#{isShow}, 
			#{permission}, 
			#{createBy.id}, 
			#{createDate}, 
			#{updateBy.id}, 
			#{updateDate}, 
			#{remarks}, 
			#{delFlag},
			u_id
		)
	</insert>
	
	<update id="update">
		UPDATE sys_menu SET 
			parent_id = #{parent.id}, 
			parent_ids = #{parentIds}, 
			name = #{name}, 
			href = #{href}, 
			target = #{target}, 
			icon = #{icon}, 
			sort = #{sort}, 
			is_show = #{isShow}, 
			permission = #{permission}, 
			update_by = #{updateBy.id}, 
			update_date = #{updateDate}, 
			remarks = #{remarks}
		WHERE id = #{id}
	</update>
	
	<update id="updateParentIds">
		UPDATE sys_menu SET 
			parent_id = #{parent.id}, 
			parent_ids = #{parentIds}
		WHERE id = #{id}
	</update>
	
	<update id="updateSort">
		UPDATE sys_menu SET 
			sort = #{sort}
		WHERE id = #{id}
	</update>
	
	<update id="delete">
		DELETE FROM sys_menu 
		WHERE id = #{id} OR parent_ids LIKE 
					<if test="dbName == 'oracle'">'%,'||#{id}||',%'</if>
					<if test="dbName == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
	</update>
	
	<update id="deleteByLogic">
		UPDATE sys_menu SET 
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id} OR parent_ids LIKE 
					<if test="dbName == 'oracle'">'%,'||#{id}||',%'</if>
					<if test="dbName == 'mysql'">CONCAT('%,', #{id}, ',%')</if>
	</update>

	<select id="getByuId"  resultMap="menuResult">
		SELECT
		<include refid="menuColumns"/>
		FROM sys_menu a
		<include refid="menuJoins"/>
		WHERE a.u_id = #{uid} LIMIT 1
	</select>

	<update id="updateUidById">
		update sys_menu
		set u_id = #{uid}
		where id = #{id}
	</update>
</mapper>
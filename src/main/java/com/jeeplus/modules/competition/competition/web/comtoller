package com.jeeplus.modules.competition.competition.web;

import com.aliyun.oss.OSS;
import com.jeeplus.common.config.Global;
import com.jeeplus.common.fileUpload.FileUploadController;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.persistence.Page;
import com.jeeplus.common.utils.FileUtils;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.web.BaseController;
import com.jeeplus.modules.YcCollege.uitls.FileUploadTool;
import com.jeeplus.modules.competition.competition.entity.CompetitionInfo;
import com.jeeplus.modules.competition.competition.service.CompetitionService;
import com.jeeplus.modules.competition.registration.entity.CompetitionRegistration;
import com.jeeplus.modules.competition.registration.service.CompetitionRegistrationService;
import com.jeeplus.modules.competition.registration.util.OssUploadUtil;
import com.jeeplus.modules.sys.utils.UserUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

@Controller
@RequestMapping("${adminPath}/qqc/competition")
public class CompetitionController extends BaseController {

    @Autowired
    private CompetitionService competitionService;

    @Autowired
    private CompetitionRegistrationService competitionRegistrationService;

    @ModelAttribute
    public CompetitionInfo get(@RequestParam(required=false) String id) {
        CompetitionInfo entity = null;
        if (StringUtils.isNotBlank(id)){
            entity = competitionService.get(id);
        }
        if (entity == null){
            entity = new CompetitionInfo();
        }
        return entity;
    }

    /**
     * 赛事列表页面
     */
    @RequiresPermissions("qqc:competition:list")
    @RequestMapping("list")
    public String list(CompetitionInfo competitionInfo, HttpServletResponse response, HttpServletRequest request, Model model) {
        Page<CompetitionInfo> page = competitionService.findPage(new Page<CompetitionInfo>(request, response), competitionInfo);
        model.addAttribute("page", page);
        model.addAttribute("competitionInfo", competitionInfo);
        return "modules/competition/competitionList";
    }

    /**
     * 赛事报名详情列表
     */
    @RequiresPermissions("qqc:competition:view")
    @RequestMapping(value = "registrationList")
    public String registrationList(@RequestParam String competitionId, CompetitionRegistration competitionRegistration,
                                  HttpServletRequest request, HttpServletResponse response, Model model) {
        // 设置活动ID查询条件
        competitionRegistration.setHdId(competitionId);

        // 获取赛事信息
        CompetitionInfo competitionInfo = competitionService.get(competitionId);

        // 分页查询报名信息
        Page<CompetitionRegistration> page = competitionRegistrationService.findPage(
            new Page<CompetitionRegistration>(request, response), competitionRegistration);

        model.addAttribute("page", page);
        model.addAttribute("competitionInfo", competitionInfo);
        model.addAttribute("competitionRegistration", competitionRegistration);
        return "modules/competition/registrationList";
    }

    /**
     * 查看，增加，编辑赛事表单页面
     */
    @RequiresPermissions(value={"qqc:competition:view","qqc:competition:add","qqc:competition:edit"}, logical=Logical.OR)
    @RequestMapping(value = "form")
    public String form(@RequestParam(value = "id", required = false) String id, Model model) {
        CompetitionInfo competitionInfo = new CompetitionInfo();
        if (StringUtils.isNotBlank(id)){
            competitionInfo = competitionService.get(id);
        }
        model.addAttribute("competitionInfo", competitionInfo);
        return "modules/competition/competitionForm";
    }

    /**
     * 保存赛事
     */
//    @RequiresPermissions(value={"qqc:competition:add","qqc:competition:edit"}, logical=Logical.OR)
//    @RequestMapping(value = "save")
//    @ResponseBody
//    public Map<String, Object> save(CompetitionInfo competitionInfo,
//                       @RequestParam(value = "thumbnailFile", required = false) MultipartFile thumbnailFile,
//                       @RequestParam(value = "posterFile", required = false) MultipartFile posterFile,
//                       Model model) throws IOException {
//        Map<String, Object> result = new HashMap<>();
//        OSS oss = null;
//        try {
//            if (!beanValidator(model, competitionInfo)){
//                result.put("success", false);
//                result.put("msg", "数据验证失败，请检查输入。");
//                return result;
//            }
//
//            // 处理地区名称，将地区名称保存到competitionRegion字段
//            if (StringUtils.isNotBlank(competitionInfo.getRegionName())) {
//                competitionInfo.setCompetitionRegion(competitionInfo.getRegionName());
//            }
//
//            // 处理缩略图上传
//            if (thumbnailFile != null && !thumbnailFile.isEmpty()) {
//                oss = FileUploadController.init();
//                if(FileUploadTool.checkFileType(FileUploadTool.getFileExtension(thumbnailFile))) {
//                    //上传图片
//                    String fileRealPath = "competition/thumbnail/" + System.currentTimeMillis() + FileUploadTool.getFileExtension(thumbnailFile);
//                    InputStream fileInputStream = thumbnailFile.getInputStream();
//                    // 上传文件
//                    oss.putObject("gqt-qql", fileRealPath, fileInputStream);
//                    competitionInfo.setThumbnailImg("http://gqt-qql.oss-cn-hangzhou.aliyuncs.com/" + fileRealPath);
//                } else {
//                    result.put("success", false);
//                    result.put("msg", "缩略图格式不正确,请上传.gif、.png、.jpg、.jpeg格式图片");
//                    return result;
//                }
//            }
//
//            // 处理海报图上传
//            if (posterFile != null && !posterFile.isEmpty()) {
//                if (oss == null) {
//                    oss = FileUploadController.init();
//                }
//                if(FileUploadTool.checkFileType(FileUploadTool.getFileExtension(posterFile))) {
//                    //上传图片
//                    String fileRealPath = "competition/poster/" + System.currentTimeMillis() + FileUploadTool.getFileExtension(posterFile);
//                    InputStream fileInputStream = posterFile.getInputStream();
//                    // 上传文件
//                    oss.putObject("gqt-qql", fileRealPath, fileInputStream);
//                    competitionInfo.setPosterImg("http://gqt-qql.oss-cn-hangzhou.aliyuncs.com/" + fileRealPath);
//                } else {
//                    result.put("success", false);
//                    result.put("msg", "海报图格式不正确,请上传.gif、.png、.jpg、.jpeg格式图片");
//                    return result;
//                }
//            }
//
//            // 处理HTML转义
//            competitionInfo.setTitle(StringEscapeUtils.unescapeHtml4(competitionInfo.getTitle()));
//            competitionInfo.setIntroduction(StringEscapeUtils.unescapeHtml4(competitionInfo.getIntroduction()));
//            competitionInfo.setDetails(StringEscapeUtils.unescapeHtml4(competitionInfo.getDetails()));
//
//            competitionService.save(competitionInfo);
//            result.put("success", true);
//            result.put("msg", "保存赛事信息成功");
//        } catch (Exception e) {
//            logger.error("保存赛事异常", e);
//            result.put("success", false);
//            result.put("msg", "保存赛事信息失败: " + e.getMessage());
//        } finally {
//            if (oss != null) {
//                oss.shutdown();
//            }
//        }
//        return result;
//    }
    @RequiresPermissions(value={"qqc:competition:add","qqc:competition:edit"}, logical=Logical.OR)
    @RequestMapping(value = "save")
    @ResponseBody
    public Map<String, Object> save(CompetitionInfo competitionInfo, Model model) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (!beanValidator(model, competitionInfo)){
                result.put("success", false);
                result.put("msg", "数据验证失败，请检查输入。");
                return result;
            }
            if (StringUtils.isNotBlank(competitionInfo.getRegionName())) {
                competitionInfo.setCompetitionRegion(competitionInfo.getRegionName());
            }
            competitionInfo.setTitle(StringEscapeUtils.unescapeHtml4(competitionInfo.getTitle()));
            competitionInfo.setIntroduction(StringEscapeUtils.unescapeHtml4(competitionInfo.getIntroduction()));
            competitionInfo.setDetails(StringEscapeUtils.unescapeHtml4(competitionInfo.getDetails()));

            competitionService.save(competitionInfo);
            result.put("success", true);
            result.put("msg", "保存赛事信息成功");

        } catch (Exception e) {
            logger.error("保存赛事异常", e);
            result.put("success", false);
            result.put("msg", "保存赛事信息失败: " + e.getMessage());
        }
        return result;
    }
    /**
     * 通用文件上传接口
     * 参考 ApiController 的实现，使用 OssUploadUtil 工具类
     */
    @RequiresPermissions(value={"qqc:competition:add","qqc:competition:edit"}, logical=Logical.OR)
    @RequestMapping(value = "uploadFile", method = RequestMethod.POST)
    @ResponseBody
    public AjaxJson uploadFile(@RequestParam("file") MultipartFile file,
                               @RequestParam("fileType") String fileType) {
        AjaxJson j = new AjaxJson();
        try {
            // 将字符串转换为枚举类型
            OssUploadUtil.FileType type = OssUploadUtil.FileType.valueOf(fileType);

            // 调用新的、更简洁的上传方法
            String ossUrl = OssUploadUtil.uploadFile(file, type);

            j.setSuccess(true);
            j.setMsg("文件上传成功");
            j.put("ossUrl", ossUrl);

        } catch (IllegalArgumentException e) {
            j.setSuccess(false);
            j.setMsg("文件类型 '" + fileType + "' 无效，请检查。");
            logger.error("文件上传失败，无效的文件类型", e);
        } catch (Exception e) {
            j.setSuccess(false);
            j.setMsg("文件上传失败：" + e.getMessage());
            logger.error("文件上传失败", e);
        }
        return j;
    }

    /**
     * 删除赛事
     */
    @RequiresPermissions("qqc:competition:del")
    @RequestMapping(value = "delete")
    public String delete(CompetitionInfo competitionInfo, RedirectAttributes redirectAttributes) {
        competitionService.delete(competitionInfo);
        addMessage(redirectAttributes, "删除赛事成功");
        return "redirect:" + adminPath + "/qqc/competition/list?repage";
    }

    /**
     * 批量删除赛事
     */
    @RequiresPermissions("qqc:competition:del")
    @RequestMapping(value = "deleteAll")
    public String deleteAll(String ids, RedirectAttributes redirectAttributes) {
        String idArray[] = ids.split(",");
        for(String id : idArray) {
            competitionService.delete(competitionService.get(id));
        }
        addMessage(redirectAttributes, "删除赛事成功");
        return "redirect:" + adminPath + "/qqc/competition/list?repage";
    }

    /**
     * 上传图片
     */
    @RequestMapping(value = "imageUpload")
    @ResponseBody
    public String imageUpload(HttpServletRequest request,
                              HttpServletResponse response,
                              MultipartFile file) throws IllegalStateException, IOException {
        String path = "";
        // 判断文件是否为空
        if (!file.isEmpty()) {
            // 文件保存路径
            String realPath = Global.COMPETITION_BASE_URL + "images/";
            // 转存文件
            FileUtils.createDirectory(Global.getUserfilesBaseDir() + realPath);
            path = realPath + new Date().getTime() + "_" + new Random().nextInt(1000) + file.getOriginalFilename();
            file.transferTo(new File(Global.getUserfilesBaseDir() + path));
        }
        return request.getContextPath() + path;
    }
}
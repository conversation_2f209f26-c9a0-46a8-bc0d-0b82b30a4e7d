package com.jeeplus.modules.judge.review.web;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.web.BaseController;
import com.jeeplus.modules.competition.registration.entity.ZhejiangAdministrative;
import com.jeeplus.modules.competition.registration.service.ZheJiangAdministrativeService;
import com.jeeplus.modules.judge.review.entity.*;
import com.jeeplus.modules.judge.review.service.CompetitionReviewService;
import com.jeeplus.modules.sys.entity.Role;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.jeeplus.common.persistence.Page;
import com.jeeplus.modules.judge.judge.entity.CompetitionJudge;
import com.jeeplus.modules.judge.judge.service.CompetitionJudgeService;
import com.jeeplus.modules.competition.registration.entity.CompetitionRegistration;
import com.jeeplus.modules.competition.registration.service.CompetitionRegistrationService;

import java.util.ArrayList;
import java.util.Map;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.Logical;
import com.jeeplus.modules.judge.review.service.ReviewProjectService;
import com.jeeplus.modules.judge.review.service.JudgeReviewService;
import com.jeeplus.modules.judge.review.service.ReviewScoreService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.stream.Collectors;
import java.util.Collections;
import java.util.List;
import java.util.HashMap;

import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.ServletOutputStream;


/**
 * 评审信息Controller
 */
@Controller
@RequestMapping(value = "${adminPath}/judge/review")
public class CompetitionReviewController extends BaseController {

    @Autowired
    private CompetitionReviewService competitionReviewService;

    @Autowired
    private CompetitionJudgeService competitionJudgeService;

    @Autowired
    private CompetitionRegistrationService competitionRegistrationService;

    @Autowired
    private ReviewProjectService reviewProjectService;

    @Autowired
    private JudgeReviewService judgeReviewService;

    @Autowired
    private ReviewScoreService reviewScoreService;

    @Autowired
    private ZheJiangAdministrativeService administrativeService;
    

    @ModelAttribute
    public CompetitionReview get(@RequestParam(required=false) String id) {
        CompetitionReview entity = null;
        if (StringUtils.isNotBlank(id)){
            entity = competitionReviewService.get(id);

            // 加载已关联的裁判ID
            JudgeReview judgeFilter = new JudgeReview();
            judgeFilter.setReviewId(entity.getId());
            List<String> judgeIds = judgeReviewService.findList(judgeFilter).stream()
                    .map(JudgeReview::getJudgeId)
                    .collect(Collectors.toList());
            entity.setJudgeIds(judgeIds);

            // 加载已关联的项目ID
            ReviewProject projectFilter = new ReviewProject();
            projectFilter.setReviewId(entity.getId());
            List<String> projectIds = reviewProjectService.findList(projectFilter).stream()
                    .map(ReviewProject::getProjectId)
                    .collect(Collectors.toList());
            entity.setProjectIds(projectIds);
        }
        if (entity == null){
            entity = new CompetitionReview();
        }
        return entity;
    }

    /**
     * 评审列表页面
     */
    @RequiresPermissions("qqc:review:view")
    @RequestMapping(value = {"list", ""})
    public String list(CompetitionReview competitionReview, HttpServletRequest request, HttpServletResponse response, Model model) {
        Page<CompetitionReview> page = competitionReviewService.findPage(new Page<CompetitionReview>(request, response), competitionReview);
        model.addAttribute("reviewList", page);
        return "modules/judge/review/competitionReviewList";
    }

    /**
     * 查看，增加，编辑评审表单页面
     */
    @RequiresPermissions(value={"qqc:review:add","qqc:review:edit"},logical=Logical.OR)
    @RequestMapping(value = "form")
    public String form(CompetitionReview competitionReview, 
                         CompetitionJudge judgeFilter, 
                         @RequestParam(value = "projectIds", required = false) String[] projectIds,
                         @RequestParam(value = "judgeIds", required = false) String[] judgeIds,
                         @RequestParam(value = "isRefresh", required = false) boolean isRefresh,
                         Model model, HttpServletRequest request, HttpServletResponse response) {
        
        if (!isRefresh && !"".equals(competitionReview.getReviewName())) { // 在非刷新操作且评审名称不为空时才进行验证
            beanValidator(model, competitionReview);
        }
        
        User currentUser = UserUtils.getUser();
        boolean isTuanweiUser = false;
        if (currentUser.getRoleList() != null) {
            for (Role role : currentUser.getRoleList()) {
                if ("municipal_committee".equals(role.getEnname())) {
                    isTuanweiUser = true;
                    break;
                }
            }
        }

        // 当翻页或筛选时，projectIds/judgeIds会作为参数传来，需要将它们合并到competitionReview对象中，以在页面上保持勾选状态
        if (projectIds != null) {
            java.util.Set<String> allProjectIds = new java.util.HashSet<>(competitionReview.getProjectIds());
            allProjectIds.addAll(java.util.Arrays.asList(projectIds));
            competitionReview.setProjectIds(new java.util.ArrayList<>(allProjectIds));
        }
        if (judgeIds != null) {
            java.util.Set<String> allJudgeIds = new java.util.HashSet<>(competitionReview.getJudgeIds());
            allJudgeIds.addAll(java.util.Arrays.asList(judgeIds));
            competitionReview.setJudgeIds(new java.util.ArrayList<>(allJudgeIds));
        }

        model.addAttribute("competitionReview", competitionReview);

        // -- 裁判分页 --
        // 手动处理裁判分页参数
        int judgePageNo = 1;
        int judgePageSize = 10; // or some default
        try {
            String pageNoStr = request.getParameter("judgePage.pageNo");
            if (StringUtils.isNotBlank(pageNoStr)) {
                judgePageNo = Integer.parseInt(pageNoStr);
            }
            String pageSizeStr = request.getParameter("judgePage.pageSize");
            if (StringUtils.isNotBlank(pageSizeStr)) {
                judgePageSize = Integer.parseInt(pageSizeStr);
            }
        } catch (NumberFormatException e) {
            // Log the error or handle it gracefully
            logger.warn("Invalid pagination parameter for judgePage.", e);
        }
        Page<CompetitionJudge> judgePageRequest = new Page<>(judgePageNo, judgePageSize);

        if (isTuanweiUser) {
            String areaCode = currentUser.getAreaCode();
            if (StringUtils.isNotBlank(areaCode)) {
                ZhejiangAdministrative city = administrativeService.findByCode(areaCode);
                if (city != null) {
                    judgeFilter.setCity(city.getName());
                }
            }
        }

        Page<CompetitionJudge> judgePage = competitionJudgeService.findPage(judgePageRequest, judgeFilter);
        judgePage.setFuncName("pageJudges"); //为裁判分页设置独立的js函数
        model.addAttribute("judgePage", judgePage);


        // -- 项目分页 --
        // 手动处理项目分页参数
        int regPageNo = 1;
        int regPageSize = 10; // or some default
        try {
            String pageNoStr = request.getParameter("registrationPage.pageNo");
            if (StringUtils.isNotBlank(pageNoStr)) {
                regPageNo = Integer.parseInt(pageNoStr);
            }
            String pageSizeStr = request.getParameter("registrationPage.pageSize");
            if (StringUtils.isNotBlank(pageSizeStr)) {
                regPageSize = Integer.parseInt(pageSizeStr);
            }
        } catch (NumberFormatException e) {
            logger.warn("Invalid pagination parameter for registrationPage.", e);
        }
        CompetitionRegistration registrationFilter = new CompetitionRegistration();
        registrationFilter.setParticipantProject(request.getParameter("participantProject"));
        registrationFilter.setCompetitionGroup(request.getParameter("competitionGroup"));

        // 根据评审类型设置项目状态筛选条件
        if (competitionReview.getReviewType() != null && competitionReview.getReviewType() == 1) {
            registrationFilter.setStatus(5); // 复赛评审，筛选“初审晋级”的项目
        } else {
            registrationFilter.setStatus(3); // 默认为初赛评审，筛选“审核通过”的项目
            competitionReview.setReviewType(0); // 确保在未勾选时值为0
        }

        if (isTuanweiUser) {
            String areaCode = currentUser.getAreaCode();
            if (StringUtils.isNotBlank(areaCode)) {
                registrationFilter.setCompetitionCity(areaCode);
            }
        }

        Page<CompetitionRegistration> registrationPageRequest = new Page<>(regPageNo, regPageSize);
        Page<CompetitionRegistration> registrationPage = competitionRegistrationService.findPage(registrationPageRequest, registrationFilter);
        registrationPage.setFuncName("pageRegistrations"); //为项目分页设置独立的js函数
        model.addAttribute("registrationPage", registrationPage);
        
        // 将筛选条件传回前端，以便在输入框和下拉菜单中保持显示
        model.addAttribute("participantProject", registrationFilter.getParticipantProject());
        model.addAttribute("competitionGroup", registrationFilter.getCompetitionGroup());
        model.addAttribute("judgeName", judgeFilter.getName());

        return "modules/judge/review/competitionReviewForm";
    }

    /**
     * 查看评审详情
     */
    @RequiresPermissions("qqc:review:view")
    @RequestMapping(value = "view")
    public String view(CompetitionReview competitionReview, Model model, HttpServletRequest request, HttpServletResponse response) {

        // 加载已关联的裁判ID
        JudgeReview judgeFilter = new JudgeReview();
        judgeFilter.setReviewId(competitionReview.getId());
        List<String> judgeIds = judgeReviewService.findList(judgeFilter).stream()
                .map(JudgeReview::getJudgeId)
                .collect(Collectors.toList());
        competitionReview.setJudgeIds(judgeIds);

        model.addAttribute("competitionReview", competitionReview);

        // 获取所有裁判（分页）
        Page<CompetitionJudge> judgePage = competitionJudgeService.findPage(new Page<CompetitionJudge>(request, response), new CompetitionJudge());
        model.addAttribute("judgePage", judgePage);

        //根据reviewId获取所有关联的项目ID
        ReviewProject reviewProjectFilter = new ReviewProject();
        reviewProjectFilter.setReviewId(competitionReview.getId());
        List<String> projectIds = reviewProjectService.findList(reviewProjectFilter).stream()
                                                      .map(ReviewProject::getProjectId)
                                                      .collect(Collectors.toList());

        //根据项目ID列表查询项目详细信息
        List<CompetitionRegistration> projectList = new ArrayList<>();
        if (!projectIds.isEmpty()) {
            CompetitionRegistration filter = new CompetitionRegistration();
            filter.setIds(projectIds.toArray(new String[0]));
            projectList = competitionRegistrationService.findList(filter);
        }

        //为每个项目计算平均分
        for (CompetitionRegistration project : projectList) {
            List<ReviewScore> scores = reviewScoreService.findByReviewAndProject(competitionReview.getId(), project.getId());
            if (scores != null && !scores.isEmpty()) {
                BigDecimal sum = scores.stream()
                                       .map(ReviewScore::getScore)
                                       .filter(java.util.Objects::nonNull)
                                       .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                long count = scores.stream().map(ReviewScore::getScore).filter(java.util.Objects::nonNull).count();

                if (count > 0) {
                    BigDecimal average = sum.divide(new BigDecimal(count), 2, RoundingMode.HALF_UP);
                    project.setAverageScore(average);
                } else {
                    project.setAverageScore(null);
                }
            } else {
                project.setAverageScore(null);
            }
        }
        
        model.addAttribute("projectList", projectList);

        return "modules/judge/review/competitionReviewView";
    }

    /**
     * 查看评审关联的项目列表
     */
    @RequiresPermissions("qqc:review:view")
    @RequestMapping(value = "projects")
    public String projects(@RequestParam(required=true) String reviewId, Model model) {
        CompetitionReview review = competitionReviewService.get(reviewId);
        if (review == null) {
            return "error/404";
        }
        model.addAttribute("review", review);

        //根据reviewId获取所有关联的项目
        ReviewProject reviewProjectFilter = new ReviewProject();
        reviewProjectFilter.setReviewId(reviewId);
        List<ReviewProject> reviewProjects = reviewProjectService.findList(reviewProjectFilter);
        List<String> projectIds = reviewProjects.stream()
                                                .map(ReviewProject::getProjectId)
                                                .collect(Collectors.toList());
        
        Map<String, ReviewProject> reviewProjectMap = reviewProjects.stream()
                .collect(Collectors.toMap(ReviewProject::getProjectId, rp -> rp));

        // 获取该评审活动的所有裁判
        JudgeReview judgeFilter = new JudgeReview();
        judgeFilter.setReviewId(reviewId);
        List<String> judgeIdList = judgeReviewService.findList(judgeFilter).stream()
                .map(JudgeReview::getJudgeId)
                .collect(Collectors.toList());
        java.util.Set<String> judgeIdSet = new java.util.HashSet<>(judgeIdList); // Use a Set for efficient lookups
        int totalJudges = judgeIdSet.size();

        //根据项目ID列表查询项目详细信息
        List<CompetitionRegistration> projectList = new ArrayList<>();
        if (!projectIds.isEmpty()) {
            CompetitionRegistration filter = new CompetitionRegistration();
            filter.setIds(projectIds.toArray(new String[0]));
            projectList = competitionRegistrationService.findList(filter);
        }

        //为每个项目计算平均分和设置最终分数
        for (CompetitionRegistration project : projectList) {
            ReviewProject rp = reviewProjectMap.get(project.getId());
            if (rp != null) {
                project.setFinalScore(rp.getFinalScore());
                project.setReviewId(rp.getReviewId()); // Pass reviewId to the project entity
            }

            // 计算总裁判数和未评分裁判数
            project.setTotalJudges(totalJudges);
            List<ReviewScore> scores = reviewScoreService.findByReviewAndProject(reviewId, project.getId());
            long scoredJudgesCount = scores.stream()
                                           .filter(s -> s.getScore() != null) // 核心修正：只统计分数不为空的记录
                                           .map(ReviewScore::getJudgeId)
                                           .filter(judgeIdSet::contains) // 只计算当前已分配裁判的得分
                                           .distinct()
                                           .count();
            project.setUnscoredJudges((int) (totalJudges - scoredJudgesCount));

            if (scores != null && !scores.isEmpty()) {
                BigDecimal sum = scores.stream()
                                       .map(ReviewScore::getScore)
                                       .filter(java.util.Objects::nonNull)
                                       .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                long count = scores.stream().map(ReviewScore::getScore).filter(java.util.Objects::nonNull).count();

                if (count > 0) {
                    BigDecimal average = sum.divide(new BigDecimal(count), 2, RoundingMode.HALF_UP);
                    project.setAverageScore(average);
                } else {
                    project.setAverageScore(null);
                }
            } else {
                project.setAverageScore(null);
            }
        }

        //将项目列表传递给JSP
        model.addAttribute("projectList", projectList);
        model.addAttribute("reviewId", reviewId);
        model.addAttribute("reviewType", review.getReviewType()); // 传递评审类型

        return "modules/judge/review/competitionReviewProjects";
    }

    @ResponseBody
    @RequiresPermissions("qqc:review:view")
    @RequestMapping(value = "getUnscoredJudges")
    public AjaxJson getUnscoredJudges(@RequestParam("projectId") String projectId, @RequestParam("reviewId") String reviewId) {
        AjaxJson j = new AjaxJson();
        try {
            // 1. 获取所有已分配的裁判ID
            JudgeReview judgeFilter = new JudgeReview();
            judgeFilter.setReviewId(reviewId);
            List<String> allJudgeIds = judgeReviewService.findList(judgeFilter).stream()
                    .map(JudgeReview::getJudgeId)
                    .collect(Collectors.toList());
            java.util.Set<String> assignedJudgeIdSet = new java.util.HashSet<>(allJudgeIds);

            // 2. 获取所有已评分的裁判ID（核心修正：同时检查分数不为空且裁判仍然被分配）
            List<String> scoredJudgeIds = reviewScoreService.findByReviewAndProject(reviewId, projectId).stream()
                    .filter(s -> s.getScore() != null && assignedJudgeIdSet.contains(s.getJudgeId()))
                    .map(ReviewScore::getJudgeId)
                    .distinct()
                    .collect(Collectors.toList());

            // 3. 找出未评分的裁判ID
            allJudgeIds.removeAll(scoredJudgeIds);

            List<CompetitionJudge> unscoredJudges = new ArrayList<>();
            if (!allJudgeIds.isEmpty()) {
                unscoredJudges = competitionJudgeService.findList(new CompetitionJudge(allJudgeIds.toArray(new String[0])));
            }

            j.setSuccess(true);
            j.put("unscoredJudges", unscoredJudges);

        } catch (Exception e) {
            logger.error("获取未评分裁判列表失败, reviewId=" + reviewId + ", projectId=" + projectId, e);
            j.setSuccess(false);
            j.setMsg("获取列表失败，请联系管理员！");
        }
        return j;
    }

    @ResponseBody
    @RequiresPermissions("qqc:review:edit")
    @RequestMapping(value = "updateScore")
    public AjaxJson updateScore(@RequestParam("projectId") String projectId, 
                                @RequestParam("reviewId") String reviewId, 
                                @RequestParam("score") BigDecimal score) {
        AjaxJson j = new AjaxJson();
        try {
            if (score.compareTo(BigDecimal.ZERO) < 0 || score.compareTo(new BigDecimal("100")) > 0) {
                j.setSuccess(false);
                j.setMsg("分数必须在0到100之间！");
                return j;
            }
            
            reviewProjectService.updateFinalScore(reviewId, projectId, score);
            j.setSuccess(true);
            j.setMsg("分数更新成功！");
        } catch (Exception e) {
            logger.error("更新最终分数失败, reviewId=" + reviewId + ", projectId=" + projectId, e);
            j.setSuccess(false);
            j.setMsg("更新分数失败，请联系管理员！");
        }
        return j;
    }

    @ResponseBody
    @RequiresPermissions("qqc:review:view")
    @RequestMapping(value = "getProjectScores")
    public AjaxJson getProjectScores(@RequestParam(required=true) String projectId, @RequestParam(required=true) String reviewId) {
        AjaxJson j = new AjaxJson();
        logger.info("开始获取项目分数，reviewId: {}, projectId: {}", reviewId, projectId);
        try {
            // 1. 获取该评审活动关联的所有裁判
            JudgeReview judgeFilter = new JudgeReview();
            judgeFilter.setReviewId(reviewId);
            List<String> judgeIds = judgeReviewService.findList(judgeFilter).stream()
                    .map(JudgeReview::getJudgeId)
                    .collect(Collectors.toList());
            
            logger.info("根据 reviewId: {} 找到 {} 个关联的裁判ID.", reviewId, judgeIds.size());

            if (judgeIds.isEmpty()) {
                j.setSuccess(true);
                j.put("scoresInfo", Collections.emptyList());
                logger.warn("未找到任何关联裁判，将返回空列表。");
                return j;
            }

            List<CompetitionJudge> judges = competitionJudgeService.findList(new CompetitionJudge(judgeIds.toArray(new String[0])));
            logger.info("成功获取 {} 位裁判的详细信息。", judges.size());

            // 2. 获取该项目已有的分数
            ReviewScore scoreFilter = new ReviewScore();
            scoreFilter.setReviewId(reviewId);
            scoreFilter.setProjectId(projectId);
            List<ReviewScore> scores = reviewScoreService.findList(scoreFilter);
            Map<String, ReviewScore> scoreMap = scores.stream()
                    .collect(Collectors.toMap(ReviewScore::getJudgeId, s -> s, (s1, s2) -> s1)); // In case of duplicates, take first
            logger.info("找到 {} 条与该项目相关的分数记录。", scores.size());

            // 3. 组装返回数据
            List<Map<String, Object>> scoresInfo = new ArrayList<>();
            for (CompetitionJudge judge : judges) {
                Map<String, Object> info = new HashMap<>();
                info.put("judgeId", judge.getId());
                info.put("judgeName", judge.getName());

                ReviewScore score = scoreMap.get(judge.getId());
                if (score != null) {
                    info.put("score", score.getScore());
                    info.put("scoreId", score.getId());
                } else {
                    info.put("score", null);
                    info.put("scoreId", null);
                }
                scoresInfo.add(info);
            }

            j.setSuccess(true);
            j.put("scoresInfo", scoresInfo);
            logger.info("成功组装 {} 位裁判的分数信息并返回。", scoresInfo.size());
        } catch (Exception e) {
            logger.error("获取项目分数失败", e);
            j.setSuccess(false);
            j.setMsg("获取项目分数失败，请联系管理员！");
        }
        return j;
    }

    @ResponseBody
    @RequiresPermissions("qqc:review:edit")
    @RequestMapping(value = "saveProjectScores")
    public AjaxJson saveProjectScores(HttpServletRequest request) {
        AjaxJson j = new AjaxJson();
        String reviewId = null;
        String projectId = null;
        try {
            projectId = request.getParameter("projectId");
             reviewId = request.getParameter("reviewId");
            if (StringUtils.isBlank(reviewId)) {
            }


            Map<String, String[]> parameterMap = request.getParameterMap();
            List<ReviewScore> scoresToSave = new ArrayList<>();

            int maxIndex = -1;
            for (String paramName : parameterMap.keySet()) {
                if (paramName.startsWith("scores[")) {
                    String indexStr = paramName.substring(paramName.indexOf('[') + 1, paramName.indexOf(']'));
                    int index = Integer.parseInt(indexStr);
                    if (index > maxIndex) {
                        maxIndex = index;
                    }
                }
            }
            
            for (int i = 0; i <= maxIndex; i++) {
                 String scoreStr = request.getParameter("scores[" + i + "].score");
                 String judgeId = request.getParameter("scores[" + i + "].judgeId");
                 String scoreId = request.getParameter("scores[" + i + "].scoreId");

                 if(StringUtils.isBlank(scoreStr) || StringUtils.isBlank(judgeId)){
                     continue;
                 }

                 ReviewScore rs = new ReviewScore();
                 try {
                    rs.setScore(new BigDecimal(scoreStr));
                 } catch (NumberFormatException ex) {
                    continue;
                 }
                 rs.setJudgeId(judgeId);
                 rs.setProjectId(projectId);
                 rs.setReviewId(reviewId);

                 if (StringUtils.isNotBlank(scoreId) && !"null".equalsIgnoreCase(scoreId)) {
                     rs.setId(scoreId);
                 } else {
                     rs.preInsert();
                 }
                 scoresToSave.add(rs);
            }

            if (!scoresToSave.isEmpty()) {
                reviewScoreService.saveAll(scoresToSave);
            }
            
            BigDecimal newAverageScore = calculateAverageScore(projectId, reviewId, reviewScoreService.findByReviewAndProject(reviewId, projectId));

            j.setSuccess(true);
            j.setMsg("分数保存成功！");
            j.put("averageScore", newAverageScore);

        } catch (Exception e) {
            logger.error("保存项目分数失败. reviewId=" + reviewId + ", projectId=" + projectId, e);
            j.setSuccess(false);
            j.setMsg("保存失败，请联系管理员！");
        }
        return j;
    }


    /**
     * 查看评审关联的裁判列表
     */
    @RequiresPermissions("qqc:review:view")
    @RequestMapping(value = "judges")
    public String judges(@RequestParam(required=true) String reviewId, Model model) {
        JudgeReview judgeFilter = new JudgeReview();
        judgeFilter.setReviewId(reviewId);
        List<String> judgeIds = judgeReviewService.findList(judgeFilter).stream()
                                                  .map(JudgeReview::getJudgeId)
                                                  .collect(Collectors.toList());

        List<CompetitionJudge> judgeList = judgeIds.isEmpty() ?
                Collections.emptyList() :
                competitionJudgeService.findList(new CompetitionJudge(judgeIds.toArray(new String[0])));

        model.addAttribute("judgeList", judgeList);
        model.addAttribute("reviewId", reviewId);

        return "modules/judge/review/competitionReviewJudges";
    }

    /**
     * 淘汰项目
     */
    @RequiresPermissions("qqc:review:edit")
    @ResponseBody
    @RequestMapping(value = "eliminateProject")
    public AjaxJson eliminateProject(@RequestParam("projectId") String projectId) {
        AjaxJson j = new AjaxJson();
        try {
            CompetitionRegistration project = competitionRegistrationService.get(projectId);
            if (project != null) {
                project.setStatus(4); // 4 for 'eliminated'
                competitionRegistrationService.save(project);
                j.setSuccess(true);
                j.setMsg("项目已成功淘汰!");
            } else {
                j.setSuccess(false);
                j.setMsg("项目不存在!");
            }
        } catch (Exception e) {
            logger.error("淘汰项目失败, projectId: " + projectId, e);
            j.setSuccess(false);
            j.setMsg("操作失败: " + e.getMessage());
        }
        return j;
    }

    /**
     * 晋级项目
     */
    @RequiresPermissions("qqc:review:edit")
    @ResponseBody
    @RequestMapping(value = "promoteProject")
    public AjaxJson promoteProject(@RequestParam("projectId") String projectId) {
        AjaxJson j = new AjaxJson();
        try {
            CompetitionRegistration project = competitionRegistrationService.get(projectId);
            if (project != null) {
                project.setStatus(5);
                competitionRegistrationService.save(project);
                j.setSuccess(true);
                j.setMsg("项目已成功晋级!");
            } else {
                j.setSuccess(false);
                j.setMsg("项目不存在!");
            }
        } catch (Exception e) {
            logger.error("晋级项目失败, projectId: " + projectId, e);
            j.setSuccess(false);
            j.setMsg("操作失败: " + e.getMessage());
        }
        return j;
    }

    /**
     * 复审晋级项目
     */
    @RequiresPermissions("qqc:review:edit")
    @ResponseBody
    @RequestMapping(value = "promoteToFinal")
    public AjaxJson promoteToFinal(@RequestParam("projectId") String projectId) {
        AjaxJson j = new AjaxJson();
        try {
            CompetitionRegistration project = competitionRegistrationService.get(projectId);
            if (project != null) {
                project.setStatus(6); // 6 for 'final review promoted'
                competitionRegistrationService.save(project);
                j.setSuccess(true);
                j.setMsg("项目已成功晋级复审!");
            } else {
                j.setSuccess(false);
                j.setMsg("项目不存在!");
            }
        } catch (Exception e) {
            logger.error("复审晋级项目失败, projectId: " + projectId, e);
            j.setSuccess(false);
            j.setMsg("操作失败: " + e.getMessage());
        }
        return j;
    }

    /**
     * 退回初审晋级项目
     */
    @RequiresPermissions("qqc:review:edit")
    @ResponseBody
    @RequestMapping(value = "revertFromPromotion")
    public AjaxJson revertFromPromotion(@RequestParam("projectId") String projectId) {
        AjaxJson j = new AjaxJson();
        try {
            CompetitionRegistration project = competitionRegistrationService.get(projectId);
            if (project != null) {
                if (project.getStatus() == 5) { // 只有初审晋级状态才能退回
                    project.setStatus(3); // 3 for 'audit passed'
                    competitionRegistrationService.save(project);
                    j.setSuccess(true);
                    j.setMsg("项目已成功退回到审核通过状态!");
                } else {
                    j.setSuccess(false);
                    j.setMsg("只有初审晋级状态的项目才能退回!");
                }
            } else {
                j.setSuccess(false);
                j.setMsg("项目不存在!");
            }
        } catch (Exception e) {
            logger.error("退回初审晋级项目失败, projectId: " + projectId, e);
            j.setSuccess(false);
            j.setMsg("操作失败: " + e.getMessage());
        }
        return j;
    }

    /**
     * 导出评审评分
     */
    @RequiresPermissions("qqc:review:view")
    @ResponseBody
    @RequestMapping(value = "exportScores")
    public void exportScores(String reviewId, HttpServletResponse response) {
        logger.info("开始导出评分数据，评审ID: {}", reviewId);
        try {
            //数据准备 (这部分逻辑已经验证是正确的)
            ReviewProject projectFilter = new ReviewProject();
            projectFilter.setReviewId(reviewId);
            List<String> projectIds = reviewProjectService.findList(projectFilter).stream()
                    .map(ReviewProject::getProjectId).collect(Collectors.toList());
            List<CompetitionRegistration> projects = new ArrayList<>();
            if (!projectIds.isEmpty()) {
                CompetitionRegistration filter = new CompetitionRegistration();
                filter.setIds(projectIds.toArray(new String[0]));
                projects = competitionRegistrationService.findList(filter);
            }

            JudgeReview judgeFilter = new JudgeReview();
            judgeFilter.setReviewId(reviewId);
            List<String> judgeIds = judgeReviewService.findList(judgeFilter).stream()
                    .map(JudgeReview::getJudgeId).collect(Collectors.toList());
            List<CompetitionJudge> judges = judgeIds.isEmpty() ? Collections.emptyList() :
                    competitionJudgeService.findList(new CompetitionJudge(judgeIds.toArray(new String[0])));

            ReviewScore scoreFilter = new ReviewScore();
            scoreFilter.setReviewId(reviewId);
            List<ReviewScore> scores = reviewScoreService.findList(scoreFilter);
            Map<String, BigDecimal> scoreMap = scores.stream()
                    .filter(s -> s.getScore() != null) // 过滤掉分数为null的记录
                    .collect(Collectors.toMap(
                        s -> s.getProjectId() + "-" + s.getJudgeId(),
                        ReviewScore::getScore,
                        (oldValue, newValue) -> newValue
                    ));
            projects.forEach(p -> p.setAverageScore(calculateAverageScore(p.getId(), reviewId, scores)));

            //构建动态表头
            List<String> headerList = new java.util.ArrayList<>(java.util.Arrays.asList("项目名称", "参赛分组", "平均分", "状态"));
            judges.forEach(judge -> headerList.add(judge.getName()));

            //手动使用 Apache POI 构建 Excel
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("评分数据");

            // 创建标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
            titleFont.setFontHeightInPoints((short) 16);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);

            // 创建表头样式
            XSSFCellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
            headerStyle.setFont(headerFont);

            // 创建标题行
            com.jeeplus.modules.judge.review.entity.CompetitionReview review = competitionReviewService.get(reviewId);
            String title = review.getReviewName() + " - 评分数据";
            XSSFRow titleRow = sheet.createRow(0);
            XSSFCell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(title);
            titleCell.setCellStyle(titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headerList.size() - 1));
            
            // 创建表头行
            XSSFRow headerRow = sheet.createRow(1);
            for (int i = 0; i < headerList.size(); i++) {
                XSSFCell cell = headerRow.createCell(i);
                cell.setCellValue(headerList.get(i));
                cell.setCellStyle(headerStyle);
            }

            // 创建数据行
            int rowNum = 2;
            for (CompetitionRegistration project : projects) {
                XSSFRow row = sheet.createRow(rowNum++);
                int cellNum = 0;
                
                row.createCell(cellNum++).setCellValue(project.getParticipantProject());
                row.createCell(cellNum++).setCellValue(com.jeeplus.modules.sys.utils.DictUtils.getDictLabel(project.getCompetitionGroup(), "competition_group", ""));
                
                BigDecimal avgScore = project.getAverageScore();
                row.createCell(cellNum++).setCellValue(avgScore != null ? avgScore.doubleValue() : 0.0);
                
                String statusText;
                switch (project.getStatus()) {
                    case 4: statusText = "已淘汰"; break;
                    case 5: statusText = "初审晋级"; break;
                    case 6: statusText = "复审晋级"; break;
                    default: statusText = "未淘汰"; break;
                }
                row.createCell(cellNum++).setCellValue(statusText);

                for (CompetitionJudge judge : judges) {
                    BigDecimal score = scoreMap.get(project.getId() + "-" + judge.getId());
                    XSSFCell cell = row.createCell(cellNum++);
                    if (score != null) {
                        cell.setCellValue(score.doubleValue());
                    } else {
                        cell.setCellValue(""); // 或者0.0，根据需求
                    }
                }
            }

            // 设置响应头并写出文件
            String fileName = review.getReviewName() + "-评分数据" + com.jeeplus.common.utils.DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-disposition", "attachment; filename=\"" + encodedFileName + "\"");
            
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
            out.flush();

        } catch (Exception e) {
            logger.error("导出评分数据失败", e);
        }
    }

    private BigDecimal calculateAverageScore(String projectId, String reviewId, List<ReviewScore> allScores) {
        List<BigDecimal> projectScores = allScores.stream()
                .filter(s -> s.getProjectId().equals(projectId) && s.getScore() != null)
                .map(ReviewScore::getScore)
                .collect(Collectors.toList());

        if (projectScores.isEmpty()) {
            return null;
        }

        BigDecimal sum = projectScores.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        return sum.divide(new BigDecimal(projectScores.size()), 2, RoundingMode.HALF_UP);
    }


    /**
     * 保存评审
     */
    @RequiresPermissions(value={"qqc:review:add", "qqc:review:edit"}, logical=Logical.OR)
    @ResponseBody
    @RequestMapping(value = "save")
    public AjaxJson save(CompetitionReview competitionReview, Model model,
                         @RequestParam(required = false) String[] projectIds,
                         @RequestParam(required = false) String[] judgeIds) throws Exception{
        AjaxJson j = new AjaxJson();
        if (!beanValidator(model, competitionReview)){
            j.setSuccess(false);
            j.setMsg("非法数据！");
            return j;
        }
        if(projectIds != null){
            competitionReview.setProjectIds(java.util.Arrays.asList(projectIds));
        }
        if(judgeIds != null){
            competitionReview.setJudgeIds(java.util.Arrays.asList(judgeIds));
        }
        competitionReviewService.save(competitionReview);
        j.setSuccess(true);
        j.setMsg("保存评审活动成功");
        return j;
    }

    /**
     * 删除评审
     */
    @RequiresPermissions("qqc:review:del")
    @ResponseBody
    @RequestMapping(value = "delete")
    public AjaxJson delete(CompetitionReview competitionReview) {
        AjaxJson j = new AjaxJson();
        competitionReviewService.delete(competitionReview);
        j.setMsg("删除评审成功");
        return j;
    }

}
package com.jeeplus.modules.judge.judge.service;

import com.jeeplus.common.persistence.Page;
import com.jeeplus.common.service.CrudService;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.judge.judge.dao.CompetitionJudgeDao;
import com.jeeplus.modules.judge.judge.entity.CompetitionJudge;
import com.jeeplus.modules.sys.dao.RoleDao;
import com.jeeplus.modules.sys.entity.Role;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.service.SystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 裁判Service
 */
@Service
@Transactional(readOnly = true)
public class CompetitionJudgeService extends CrudService<CompetitionJudgeDao, CompetitionJudge> {
    @Autowired
    private SystemService systemService;
    @Autowired
    private RoleDao roleDao;
    public CompetitionJudge get(String id) {
        return super.get(id);
    }

    public List<CompetitionJudge> findList(CompetitionJudge competitionJudge) {
        return super.findList(competitionJudge);
    }

    public Page<CompetitionJudge> findPage(Page<CompetitionJudge> page, CompetitionJudge competitionJudge) {
        Page<CompetitionJudge> resultPage = super.findPage(page, competitionJudge);
        if (resultPage.getList() != null && !resultPage.getList().isEmpty()) {
            for (int i = 0; i < resultPage.getList().size(); i++) {
                CompetitionJudge judge = resultPage.getList().get(i);
            }
        }
        return resultPage;
    }

    @Transactional(readOnly = false)
    public void save(CompetitionJudge competitionJudge) {
        if (isPhoneExist(competitionJudge.getPhone(), competitionJudge.getId())) {
            throw new RuntimeException("手机号 " + competitionJudge.getPhone() + " 已存在");
        }
        if (competitionJudge.getIsNewRecord()){
            User user = new User();
            user.setName(competitionJudge.getName());
            user.setLoginName(competitionJudge.getPhone());
            user.setPhone(competitionJudge.getPhone());
            user.setPassword(SystemService.entryptPassword(competitionJudge.getPhone()));
            user.setUserType("zj");
            List<Role> roleList = new ArrayList<>();
            roleList.add(roleDao.get("e211eb92b38b40f1bad69a8e53eb1ac4"));
            user.setRoleList(roleList);
            systemService.saveUser(user);
            competitionJudge.setUserId(user.getId());
        }
        super.save(competitionJudge);
    }

    @Transactional(readOnly = false)
    public void delete(CompetitionJudge competitionJudge) {
        super.delete(competitionJudge);
    }

    /**
     * 根据用户ID查询裁判信息
     */
    public CompetitionJudge getByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return null;
        }
        return dao.getByUserId(userId);
    }

    /**
     * 根据手机号查询裁判信息
     */
    public CompetitionJudge getByPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return null;
        }
        return dao.getByPhone(phone, CompetitionJudge.DEL_FLAG_NORMAL);
    }

    /**
     * 根据姓名模糊查询裁判列表
     */
    public List<CompetitionJudge> findByNameLike(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return dao.findByNameLike(name);
    }

    /**
     * 根据省市区查询裁判列表
     */
    public List<CompetitionJudge> findByArea(String province, String city, String area) {
        return dao.findByArea(province, city, area);
    }

    /**
     * 获取所有可用的裁判列表
     */
    public List<CompetitionJudge> findAllAvailable() {
        return dao.findAllAvailable();
    }

    /**
     * 验证手机号是否已存在
     */
    public boolean isPhoneExist(String phone, String id) {
        if (StringUtils.isBlank(phone)) {
            return false;
        }
        CompetitionJudge judge = getByPhone(phone);
        if (judge == null) {
            return false;
        }
        // 编辑时，如果是当前记录，则不算重复
        if (StringUtils.isNotBlank(id) && id.equals(judge.getId())) {
            return false;
        }
        return true;
    }

}
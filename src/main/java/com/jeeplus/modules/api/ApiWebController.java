package com.jeeplus.modules.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.jeeplus.common.config.Global;
import com.jeeplus.common.fileUpload.FileUploadController;
import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.persistence.Page;
import com.jeeplus.common.utils.FileUtils;
import com.jeeplus.common.utils.IdGen;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.web.BaseController;
import com.jeeplus.modules.YcCollege.entity.YcCollege;
import com.jeeplus.modules.YcCollege.service.YcCollegeService;
import com.jeeplus.modules.YcCollege.uitls.FileUploadTool;
import com.jeeplus.modules.active.entity.Active;
import com.jeeplus.modules.active.entity.Entrepreneurship;
import com.jeeplus.modules.active.service.ActivityService;
import com.jeeplus.modules.active.service.EntrepreneurshipService;
import com.jeeplus.modules.customer.service.CustomerFormService;
import com.jeeplus.modules.news.entity.NewsInfo;
import com.jeeplus.modules.news.service.NewsService;
import com.jeeplus.modules.organization.entity.Organization;
import com.jeeplus.modules.organization.entity.OrganizationApplicant;
import com.jeeplus.modules.organization.entity.OrganizationUser;
import com.jeeplus.modules.organization.service.OrganizationApplicantService;
import com.jeeplus.modules.organization.service.OrganizationService;
import com.jeeplus.modules.organization.service.OrganizationUserService;
import com.jeeplus.modules.qqc.dao.PostApplicationDao;
import com.jeeplus.modules.qqc.entity.CompanyInfo;
import com.jeeplus.modules.qqc.entity.PostApplication;
import com.jeeplus.modules.qqc.entity.PostInfo;
import com.jeeplus.modules.qqc.service.CompanyInfoService;
import com.jeeplus.modules.qqc.service.PostInfoService;
import com.jeeplus.modules.roomcheckin.entity.ApplicationCheckIn;
import com.jeeplus.modules.roomcheckin.entity.RoomCheckIn;
import com.jeeplus.modules.roomcheckin.service.RoomCheckInService;
import com.jeeplus.modules.sys.entity.Area;
import com.jeeplus.modules.sys.entity.Dict;
import com.jeeplus.modules.sys.service.AreaService;
import com.jeeplus.modules.sys.utils.DictUtils;
import com.jeeplus.modules.teacher.entity.Answer;
import com.jeeplus.modules.teacher.entity.Issues;
import com.jeeplus.modules.teacher.entity.Teacher;
import com.jeeplus.modules.teacher.service.IssuesService;
import com.jeeplus.modules.teacher.service.TeacherService;
import com.jeeplus.modules.weCatApp.dao.ApiDao;
import com.jeeplus.modules.weCatApp.entity.RecruitInfo;
import com.jeeplus.modules.weCatApp.entity.WechatCapital;
import com.jeeplus.modules.weCatApp.entity.WechatUserinfo;
import com.jeeplus.modules.weCatApp.service.ApiService;
import com.jeeplus.modules.weCatApp.service.WeChatUserService;
import com.jeeplus.modules.weCatApp.service.WechatCapitalService;
import org.apache.avalon.framework.ExceptionUtil;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.*;

@RestController
    @RequestMapping("api/web")
public class ApiWebController extends BaseController {

    @Autowired
    private ApiService apiService;
    @Autowired
    private AreaService areaService;
    @Autowired
    private PostInfoService postInfoService;
    @Autowired
    private CompanyInfoService companyInfoService;
    @Autowired
    private WechatCapitalService wechatCapitalService;
    @Autowired
    private NewsService newsService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private TeacherService teacherService;
    @Autowired
    private IssuesService issuesService;
    @Autowired
    private RoomCheckInService roomCheckInService;
    @Autowired
    private CustomerFormService customerFormService;
    @Autowired
    private YcCollegeService ycCollegeService;
    @Autowired
	private OrganizationService organizationService;
    @Autowired
	private OrganizationUserService organizationUserService;
    @Autowired
    private OrganizationApplicantService organizationApplicantService;
    @Autowired
    private EntrepreneurshipService entrepreneurshipService;
    @Autowired
    private WeChatUserService weChatUserService;
    @Autowired
    private ApiDao apiDao;
    @Autowired
    private PostApplicationDao postApplicationDao;

    /**
     * 获取地区
     * @return AjaxJson
     */
    @RequestMapping("getArea")
    public AjaxJson getArea(String type,String parent){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            if (StringUtils.isBlank(type)){
                ajaxJson.setMsg("参数type不能为空");
                ajaxJson.setSuccess(false);
                return ajaxJson;
            }
            Area area = new Area();
            area.setType(type);
            area.setParent(areaService.get(parent));
            List<Map<String,String>> map  = areaService.findCityLevelArea(area);
            ajaxJson.put("areas", map);
            ajaxJson.setMsg("success");
        }catch (Exception e){
           logger.error("getArea异常",e);
            ajaxJson.setMsg("error");
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }
    
    /**
     * 获取地区
     * @return AjaxJson
     */
    @RequestMapping("getAreaAndPostInfoNum")
    public AjaxJson getAreaAndPostInfoNum(String type,String postType){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            if (StringUtils.isBlank(type)){
                ajaxJson.setMsg("参数type不能为空");
                ajaxJson.setSuccess(false);
                return ajaxJson;
            }
            List<Map<String,Object>> map =  apiService.findCityLevelAreaByType(type, postType);
            ajaxJson.put("areas", map);
            ajaxJson.setMsg("success");
        }catch (Exception e){
           logger.error("getArea异常",e);
            ajaxJson.setMsg("error");
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    /**
     * 根据类型获取字典
     * @param type
     * @return
     */
    @RequestMapping("/getDict")
    public AjaxJson getDict(String type){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<Dict> dictList = DictUtils.getDictList(type);
            Map<Object,Object> map = new HashMap<Object,Object>();
            for (Dict dict : dictList) {
                map.put(dict.getValue(),dict.getLabel());
            }
            ajaxJson.setSuccess(true);
            ajaxJson.put("list",map);
        }catch (Exception e){
            logger.error("------------------getDict异常",e);
            ajaxJson.setMsg("error");
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    /**
     * 根据地区获取岗位列表
     * @param area 地区
     * @return AjaxJson
     */
    @RequestMapping("getPostByArea")
    public AjaxJson getPostByArea(String area){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<String> list = apiService.getPostByArea(area);
            ajaxJson.put("posts", list);
            ajaxJson.setMsg("success");
        }catch (Exception e){
           logger.error("getPostByArea异常",e);
            ajaxJson.setMsg("error");
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    /**
     * 获取岗位性质下拉列表
     * @return
     */
    @RequestMapping("findPostTypeList")
    public AjaxJson findPostTypeList(){
        AjaxJson ajaxJson = new AjaxJson();
        try{
            List<Dict> dicts = DictUtils.getDictList("qqc_post_nature");
            Map<Object,Object> map = new HashMap<Object,Object>();
            for (Dict dict : dicts) {
                map.put(dict.getValue(),dict.getLabel());
            }
            ajaxJson.put("list", map);
            ajaxJson.setMsg("success");
        }catch (Exception e){
           logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 企业认证
     * @param json
     * @param file
     * @param request
     * @return
     */
    @RequestMapping("/companyCertification")
    @ResponseBody
    public AjaxJson companyCertification(String json,MultipartFile file,HttpServletRequest request){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            if(null == json){
                throw new Exception("json信息为空");
            }
            json = json.replaceAll("&quot;", "\"");
            JSONObject jsonObject = JSON.parseObject(json);
            CompanyInfo companyInfo = companyInfoService.get(jsonObject.getString("id"));
            if (companyInfo == null){
                companyInfo = new CompanyInfo();
            }
            companyInfo.setCompanyName(jsonObject.getString("name"));
            companyInfo.setCompanyWeb(jsonObject.getString("web"));
            companyInfo.setCompanyIntroduce(jsonObject.getString("introduce"));
            String path = "";
            if(file != null) {
                // 判断文件是否为空
                if (!file.isEmpty()) {
                    String fileName = file.getOriginalFilename();
                    String suffixName = fileName.substring(fileName.lastIndexOf("."));
                    if (!(suffixName.endsWith("gif")||suffixName.endsWith("jpg")||suffixName.endsWith("pdf")||suffixName.endsWith("doc")||suffixName.endsWith("png"))){
                        logger.info("导入文件格式错误，请导入gif/jpg/pdf/doc/png格式的文件！");
                    }else {
                        // 文件保存路径
                        String realPath = Global.COMPANYFILES_BASE_URL + "images/";
                        // 转存文件
                        FileUtils.createDirectory(Global.getUserfilesBaseDir() + realPath);
                        path = realPath + new Date().getTime() + "_" + new Random().nextInt(1000) + file.getOriginalFilename();
                        file.transferTo(new File(Global.getUserfilesBaseDir() + path));
                        companyInfo.setBusinessLicense(request.getContextPath() + path);
                    }
                }
            }
            //公司地址 存入数据库优先级未 区>市>省
            String province = jsonObject.getString("province");
            String city = jsonObject.getString("city");
            String area = jsonObject.getString("area");
            if (StringUtils.isNotBlank(province)){
                companyInfo.setWorkplaceId(province);
            }
            if (StringUtils.isNotBlank(city)){
                companyInfo.setWorkplaceId(city);
            }
            if (StringUtils.isNotBlank(area)){
                companyInfo.setWorkplaceId(area);
            }
            companyInfo.setAddress(jsonObject.getString("address"));
            companyInfo.setContactsName(jsonObject.getString("contactsName"));
            companyInfo.setContactsPhone(jsonObject.getString("contactsPhone"));
            companyInfo.setContactsEmail(jsonObject.getString("contactsEmail"));
            companyInfo.setStatus("2");
            companyInfo.setCertifiedSponsor(jsonObject.getString("certifiedSponsor"));
            companyInfoService.save(companyInfo);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("操作成功");
        }catch (Exception e){
            logger.error("-----------------异常",e);
            ajaxJson.setMsg("操作失败");
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    /**
     * 岗位信息列表
     * @param workSpace 地点
     * @param postName  岗位名称
     * @param postType  岗位性质
     * @param status 开关状态 人才服务status=all,我的岗位status=mine
     * @param userId 用户id
     * @return AjaxJson
     */
    @RequestMapping(value = "findRecruitInfoList")
    public AjaxJson findRecruitInfoList(@RequestParam(value = "workSpace",required = false,defaultValue = "") String workSpace,
                                        @RequestParam(value = "postName",required = false,defaultValue = "") String postName,
                                        @RequestParam(value = "postType",required = false,defaultValue = "") String postType,
                                        @RequestParam(value = "status", defaultValue = "") String status,
                                        @RequestParam(value = "userId", defaultValue = "") String userId,
                                        @RequestParam(value = "companyId",required = false,defaultValue = "") String companyId,
                                        @RequestParam(value = "pageNo",required = false,defaultValue = "1") Integer pageNo,
                                        @RequestParam(value = "pageSize",required = false,defaultValue = "10") Integer pageSize){
        AjaxJson ajaxJson = new AjaxJson();
        try{
            RecruitInfo recruitInfo = new RecruitInfo();
            recruitInfo.setWorkSpace(workSpace);
            recruitInfo.setPostName(postName);
            //TODO 这个地方很奇怪，无论postType传多少 最后数据库都查postType=6
            recruitInfo.setPostType(postType);
            recruitInfo.setStatus(status);
            recruitInfo.setUserId(userId);
            recruitInfo.setCompanyId(companyId);
            Page<RecruitInfo> pg = new Page<RecruitInfo>(pageNo, pageSize);
            Page<RecruitInfo> recruitInfos = apiService.findRecruitInfoList(pg,recruitInfo);
            ajaxJson.put("list",recruitInfos);
            ajaxJson.setMsg("success");
            }catch(Exception e){
               logger.error("-------------------------异常",e);
                ajaxJson.setErrorCode(e.getClass().getName());
                ajaxJson.setMsg("error");
                ajaxJson.setSuccess(false);
            }
        return ajaxJson;
    }

    /**
     * 获取岗位详细信息
     * @param postId 岗位id
     * @return AjaxJson
     */
    @RequestMapping("getRecruitInfoList")
    public AjaxJson getRecruitInfoList(String postId){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            RecruitInfo recruitInfo = apiService.getRecruitInfo(postId);
            ajaxJson.put("recruitInfo", recruitInfo);
            ajaxJson.setMsg("success");
        }catch (Exception e){
           logger.error("-------------------------异常",e);
            ajaxJson.setMsg("error");
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    /**
     * 添加岗位信息
     * @param postName
     * @param postType
     * @param postDemand
     * @param peopleNum
     * @param salaryMin
     * @param salaryMax
     * @return
     */
    @RequestMapping(value = "addPostInfo")
    public AjaxJson addPostInfo(@RequestParam("postName") String postName,
                                @RequestParam("postType") String postType,
                                @RequestParam("postDemand") String postDemand,
                                @RequestParam("peopleNum") String peopleNum,
                                @RequestParam("salaryMin") String salaryMin,
                                @RequestParam("salaryMax") String salaryMax){
        AjaxJson ajaxJson = new AjaxJson();
        PostInfo postInfo = new PostInfo();
        try {
            String[] names = postName.split(",");
            String[] types = postType.split(",");
            String[] demands = postDemand.split(",");
            String[] nums = peopleNum.split(",");
            String[] mins = salaryMin.split(",");
            String[] maxs = salaryMax.split(",");
            for (int i = 0; i <= names.length; i++) {
                postInfo.setStatus(Global.CLOSE);
                postInfo.setPostName(names[i]);
                postInfo.setPostType(types[i]);
                postInfo.setPostDemand(demands[i]);
                postInfo.setPeopleNum(nums[i]);
                postInfo.setSalaryMin(mins[i]);
                postInfo.setSalaryMax(maxs[i]);
                postInfoService.save(postInfo);
            }
            ajaxJson.setMsg("success");
            ajaxJson.put("result", "添加成功");
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 新增/编辑岗位
     * @param jsonObejct 岗位信息json
     * @param companyId 认证企业id
     * @return
     */
    @RequestMapping("/savePost")
    public AjaxJson savePost(String jsonObejct,String companyId){
        AjaxJson ajaxJson = new AjaxJson();
        try{
            jsonObejct = jsonObejct.replaceAll("&quot;", "\"");
            if (jsonObejct == null){
                throw new Exception("岗位信息异常");
            }
            JSONArray jsonArray = JSONArray.parseArray(jsonObejct);
            if (jsonArray.size() == 0){
                throw new Exception("岗位信息异常");
            }
            for (int i = 0; i <jsonArray.size() ; i++) {
                JSONObject json = jsonArray.getJSONObject(i);
                PostInfo postInfo = postInfoService.get(json.getString("id"));
                if (postInfo == null){
                    postInfo = new PostInfo();
                }
                postInfo.setStatus(Global.CLOSE);
                postInfo.setCompanyId(companyId);
                postInfo.setPlaceId(json.getString("placeId"));
                postInfo.setPostName(json.getString("postName"));
                postInfo.setPostType(json.getString("postType"));
                postInfo.setPostDemand(json.getString("postDemand"));
                postInfo.setPeopleNum(json.getString("peopleNum"));
                postInfo.setSalaryMin(json.getString("salaryMin"));
                postInfo.setSalaryMax(json.getString("salaryMax"));
                postInfo.setCreateDate(new Date());
                postInfo.setPostNo(String.valueOf(System.currentTimeMillis()).substring(0,5));
                postInfoService.save(postInfo);
            }
            ajaxJson.setMsg("保存成功");
            ajaxJson.setSuccess(true);
        }catch (Exception e){
            logger.error("--------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("保存失败");
        }
        return ajaxJson;
    }

    /**
     * 岗位申请
     * @param jsonObject
     * @return
     */
    @RequestMapping("/applyForAPosition")
    public AjaxJson applyForAPosition(String jsonObject){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            jsonObject = jsonObject.replaceAll("&quot;", "\"");
            if (null==jsonObject){
                throw new Exception("申请失败");
            }
            JSONObject json = JSONObject.parseObject(jsonObject);
            if (json == null){
                throw new Exception("申请失败");
            }
            if (StringUtils.isBlank(json.getString("applicationId"))){
                ajaxJson.setSuccess(false);
                ajaxJson.setMsg("您未登录,请登录后重试。");
                return ajaxJson;
            }

            WechatUserinfo wechatUserinfo = weChatUserService.get(json.getString("applicationId"));
            if (wechatUserinfo == null){
                ajaxJson.setSuccess(false);
                ajaxJson.setMsg("用户不存在,请联系管理员。");
                return ajaxJson;
            }

            PostApplication postApplication = new PostApplication();
            postApplication.setStatus(Global.NO);
            postApplication.setId(IdGen.uuid());
            postApplication.setCompanyId(json.getString("companyId"));
            postApplication.setPostId(json.getString("postId"));
            postApplication.setApplicationId(json.getString("applicationId"));
            postApplication.setCreateDate(new Date());
            postApplication.setResumeId(json.getString("resumeId"));
            postInfoService.saveApplication(postApplication);
            ajaxJson.setMsg("申请成功");
            ajaxJson.setSuccess(true);
        }catch (Exception e){
            logger.error("---------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("申请失败");
        }
        return ajaxJson;
    }

    /**
     * 获取公司信息
     * @param companyType 公司类型
     * @return
     */
    @RequestMapping("getCompanyInfo")
    public AjaxJson getCompanyInfo(String companyType){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            CompanyInfo companyInfo = new CompanyInfo();
            companyInfo.setCompanyType(companyType);
            ajaxJson.put("companyLists", companyInfoService.findList(companyInfo));
            ajaxJson.setMsg("success");
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 岗位信息开关修改
     * @param id
     * @param status
     * @return
     */
    @RequestMapping("updateStatus")
    public AjaxJson updateStatus(String id,String status){
        AjaxJson ajaxJson = new AjaxJson();
        try{
            postInfoService.updateStatus(id,status);
            ajaxJson.setMsg("success");
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setMsg("error");
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    public static boolean hasMessyCode(String str){
        return !java.nio.charset.Charset.forName("GBK").newEncoder().canEncode(str);
    }

    /**
     * 获取微信资本相亲list
     * @param sub
     * @return
     */
    @RequestMapping("getCapitalList")
    public AjaxJson getCapitalList(String sub,String title,
                                   @RequestParam(value = "pageNo",required = false,defaultValue = "1") Integer pageNo,
                                   @RequestParam(value = "pageSize",required = false,defaultValue = "10") Integer pageSize){
        AjaxJson ajaxJson = new AjaxJson();
        try{
            WechatCapital wechatCapital = new WechatCapital();
            wechatCapital.setSub(sub);
            if(title!=null) {
                if (hasMessyCode(title)) {
                    title = new String(title.getBytes("iso-8859-1"), "UTF-8");
                    logger.error("发现转码,转码后的数据：" + title);
                }
                wechatCapital.setTitle(title);
            }
            Page<WechatCapital> page = new Page<WechatCapital>(pageNo,pageSize);
            Page<WechatCapital> wechatCapitalPage = wechatCapitalService.findPage(page,wechatCapital);
            ajaxJson.put("list",wechatCapitalPage);
            ajaxJson.setMsg("success");
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 获取微信资本相亲详细信息
     * @param id
     * @return
     */
    @RequestMapping("getCapital")
    public AjaxJson getCapital(String id){
        AjaxJson ajaxJson = new AjaxJson();
        try{
            ajaxJson.put("capital",wechatCapitalService.getCapital(id));
            ajaxJson.setMsg("success");
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 获取最新资讯
     * @return
     */
    @RequestMapping("/getLatestNew")
    public AjaxJson getLatestNew(String type){
        AjaxJson ajaxJson = new AjaxJson();
        try {
        	NewsInfo newsInfo = new NewsInfo();
        	if(StringUtils.isNotEmpty(type)) {
            	newsInfo.setType(type);
            }
            List<NewsInfo> newsInfos = newsService.findlatestNew(newsInfo);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("success");
            ajaxJson.put("list",newsInfos);
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 获取新闻列表
     * @return
     */
    @RequestMapping("/getNewList/{newsType}")
    public AjaxJson getNewList(String title,@PathVariable String newsType,String type,
                               @RequestParam(value = "pageNo",required = false,defaultValue = "1") Integer pageNo,
                               @RequestParam(value = "pageSize",required = false,defaultValue = "10") Integer pageSize){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            NewsInfo newsInfo = new NewsInfo();
            if(title!=null) {
                if (hasMessyCode(title)) {
                    title = new String(title.getBytes("iso-8859-1"), "UTF-8");
                    logger.error("发现转码,转码后的数据：" + title);
                }
                newsInfo.setTitle(title);
            }
            if(StringUtils.isNotEmpty(type)) {
                newsInfo.setType(type);
            }
            newsInfo.setNewsType(newsType);
            Page<NewsInfo> page = new Page<NewsInfo>(pageNo,pageSize);
            Page<NewsInfo> newsInfoPage = newsService.findPage(page,newsInfo);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("success");
            ajaxJson.put("list",newsInfoPage);
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 获取新闻详情
     * @param id
     * @return
     */
    @RequestMapping("/getNewInfo")
    public AjaxJson getNewInfo(String id){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            NewsInfo newsInfo = newsService.get(id);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("success");
            ajaxJson.put("info",newsInfo);
        } catch (Exception e) {
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 活动列表
     * @return
     */
    @RequestMapping("/getActiveList")
    public AjaxJson getActiveList(String type){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Active active = new Active();
            active.setType(type);
            active.setStatus("1");
            List<Active> actives = activityService.findActiveList(active);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("success");
            ajaxJson.put("list",actives);
        } catch (Exception e) {
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 获取活动信息
     * @param id
     * @return
     */
    @RequestMapping("/getActiveInfo")
    public AjaxJson getActiveInfo(String id,String userId){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Active active = new Active();
            active.setUserId(userId);
            active.setId(id);
            Active actives = activityService.getByUserId(id,userId);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("success");
            ajaxJson.put("info",actives);
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 获取导师列表
     * @return
     */
    @RequestMapping("/getMentorList")
    public AjaxJson getMentorList(@RequestParam(value = "pageNo",required = false,defaultValue = "1") Integer pageNo,
                                  @RequestParam(value = "pageSize",required = false,defaultValue = "10") Integer pageSize,String types){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<Teacher> pg = new Page<Teacher>(pageNo, pageSize);
            Teacher t = new Teacher();
            t.setTypes(types);
            Page<Teacher> teacherPage = teacherService.findPage(pg,t);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("success");
            ajaxJson.put("list",teacherPage);
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 获取导师详情
     * @param id
     * @return
     */
    @RequestMapping("/getMentorInfo")
    public AjaxJson getMentorInfo(String id){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("success");
            ajaxJson.put("info",teacherService.get(id));
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 获取问题列表
     * @return
     */
    @RequestMapping("/getListOfIssues")
    public AjaxJson getListOfIssues(String id,String status,
                                    @RequestParam(value = "pageNo",required = false,defaultValue = "1") Integer pageNo,
                                    @RequestParam(value = "pageSize",required = false,defaultValue = "10") Integer pageSize){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Issues issues = new Issues();
            issues.setTeacherId(id);
            issues.setStatus(status);
            Page<Issues> pg = new Page<Issues>(pageNo,pageSize);
            Page<Issues> issuesPage = issuesService.findPage(pg,issues);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("success");
            ajaxJson.put("list",issuesPage);
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 获取问题类型
     */
    @RequestMapping("/getIssuesType")
    public AjaxJson getIssuesType(){
        AjaxJson ajaxJson = new AjaxJson();
        ajaxJson.setSuccess(true);
        ajaxJson.setMsg("success");
        Map<String,String> map = new HashMap<String,String>();
        map.put("1","问题测试类型1");
        map.put("2","问题测试类型2");
        ajaxJson.put("list",map);
        return ajaxJson;
    }

    /**
     * 获取问题详情
     * @return
     */
    @RequestMapping("/getIssuesInfo")
    public AjaxJson getIssuesInfo(String id){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Issues issues = issuesService.get(id);
            List<Answer> answers = issuesService.findListByIssuesId(issues);
            issues.setAnswerNum(answers.size());
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("success");
            ajaxJson.put("info",issues);
            ajaxJson.put("answerList",answers);
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 发起提问
     * @return
     */
    @RequestMapping("/saveIssues")
    public AjaxJson saveIssues(String json,MultipartFile file,HttpServletRequest request,
                               HttpServletResponse response) throws IllegalStateException {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            if(null == json){
                throw new Exception("json信息为空");
            }
            json = json.replaceAll("&quot;", "\"");
            JSONObject jsonObject = JSON.parseObject(json);
            Issues issues = new Issues();
            issues.setTitle(jsonObject.getString("title"));
            issues.setDescription(jsonObject.getString("description"));
            issues.setQuestioner(jsonObject.getString("questioner"));

            issues.setType(jsonObject.getString("type"));
            issues.setTeacherId(jsonObject.getString("teacherId"));
            issues.setStatus(Global.NO);

            String path = "";
            if(file != null) {
                // 判断文件是否为空
                if (!file.isEmpty()) {
                    // 文件保存路径
                    String realPath = Global.ISSUES_BASE_URL + "images/";
                    // 转存文件
                    FileUtils.createDirectory(Global.getUserfilesBaseDir() + realPath);
                    path = realPath + new Date().getTime() + "_" + new Random().nextInt(1000) + file.getOriginalFilename();
                    file.transferTo(new File(Global.getUserfilesBaseDir() + path));
                    issues.setUrl(request.getContextPath() + path);
                }
            }
            issuesService.save(issues);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("success");
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 空间入住列表
     * @return
     */
    @RequestMapping("/roomCheckInList")
    public AjaxJson roomCheckInList(String jsonObject,
                                    @RequestParam(value = "pageNo",required = false,defaultValue = "1") Integer pageNo,
                                    @RequestParam(value = "pageSize",required = false,defaultValue = "10") Integer pageSize){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            RoomCheckIn roomCheckIn = new RoomCheckIn();
            if (jsonObject != null) {
                jsonObject = jsonObject.replaceAll("&quot;", "\"");
                JSONObject json = JSONObject.parseObject(jsonObject);
                if (json != null) {
                    roomCheckIn.setArea(new Area(json.getString("address")));
                    roomCheckIn.setRoomName(json.getString("roomName"));
                }
            }
            Page<RoomCheckIn> page = new Page<RoomCheckIn>(pageNo,pageSize);
            Page<RoomCheckIn> roomCheckInPage = roomCheckInService.findPage(page, roomCheckIn);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("success");
            ajaxJson.put("list",roomCheckInPage);
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     * 空间入住详细
     * @param id
     * @return
     */
    @RequestMapping("/roomCheckInInfo")
    public AjaxJson roomCheckInInfo(String id,String loginId){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            RoomCheckIn roomCheckIn = roomCheckInService.getByUserId(id,loginId);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("success");
            ajaxJson.put("info",roomCheckIn);
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("error");
        }
        return ajaxJson;
    }

    /**
     *
     * @param jsonObject
     * @return
     */
    @RequestMapping("/applicationCheckIn")
    public AjaxJson applicationCheckIn(String jsonObject,MultipartFile file){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            if(jsonObject == null){
                throw new Exception("申请失败");
            }
            jsonObject = jsonObject.replaceAll("&quot;", "\"");
            JSONObject json = JSONObject.parseObject(jsonObject);
            if (json == null){
                throw new Exception("申请失败");
            }
            ApplicationCheckIn applicationCheckIn = new ApplicationCheckIn();
            applicationCheckIn.setStatus(Global.NO);
            applicationCheckIn.setId(IdGen.uuid());
            applicationCheckIn.setRoomId(json.getString("roomId"));
            applicationCheckIn
                    .setApplicationId(json.getString("applicationId"));
            applicationCheckIn.setCreateDate(new Date());
            roomCheckInService.applicationRoom(applicationCheckIn);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("申请成功");
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("申请失败");
        }
        return ajaxJson;
    }

    /**
     * 我要学习
     * @param title
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping("/findYcCollegeList")
    public AjaxJson findYcCollegeList(String title,
                                      @RequestParam(value = "pageNo",required = false,defaultValue = "1") Integer pageNo,
                                      @RequestParam(value = "pageSize",required = false,defaultValue = "10") Integer pageSize){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            YcCollege ycCollege = new YcCollege();
            ycCollege.setTitle(title);
            Page<YcCollege> pg = new Page<YcCollege>(pageNo,pageSize);
            Page<YcCollege> list = ycCollegeService.findPage(pg,ycCollege);
            ajaxJson.put("list",list);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("查询成功");
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("查询失败");
        }
        return ajaxJson;
    }
    
    /**
     * 我要学习详细
     * @param id
     * @return
     */
    @RequestMapping("/findYcCollegeDetails")
    public AjaxJson findYcCollegeDetails(String id){
        AjaxJson ajaxJson = new AjaxJson();
        try {
            YcCollege bean = ycCollegeService.get(id);
            ajaxJson.put("data",bean);
            ajaxJson.setSuccess(true);
            ajaxJson.setMsg("查询成功");
        }catch (Exception e){
            logger.error("-------------------------异常",e);
            ajaxJson.setSuccess(false);
            ajaxJson.setMsg("查询失败");
        }
        return ajaxJson;
    }

    /**
     * 获取组织列表
     * @param organization
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping("getOrganizationList")
    public AjaxJson getOrganizationList(Organization organization,
    		@RequestParam(value = "pageNo",required = false,defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize",required = false,defaultValue = "10") Integer pageSize){
        AjaxJson ajaxJson = new AjaxJson();
        try {
        	if(StringUtils.isNotEmpty(organization.getName())) {
        		String title = organization.getName();
                if (hasMessyCode(title)) {
                     title = new String((organization.getName()).getBytes("iso-8859-1"), "UTF-8");
                    logger.error("发现转码,转码后的数据：" + title);
                }
                organization.setName(title);
            }
        	 Page<Organization> page = new Page<Organization>(pageNo,pageSize);
             Page<Organization> organizationPage = organizationService.findPage(page,organization);
             ajaxJson.setSuccess(true);
             ajaxJson.setMsg("success");
             ajaxJson.put("list",organizationPage);
        }catch (Exception e){
           logger.error("getOrganizationList异常",e);
            ajaxJson.setMsg("error");
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }
    
    /**
     * 获取组织详细
     */
    @RequestMapping("getOrganizationDetail")
    public AjaxJson getOrganizationDetail(String organizationId){
        AjaxJson ajaxJson = new AjaxJson();
        try {
        	Organization bean = organizationService.get(organizationId);
            ajaxJson.put("date", bean);
            ajaxJson.setMsg("success");
        }catch (Exception e){
           logger.error("getOrganizationDetail异常",e);
            ajaxJson.setMsg("error");
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }
    
    
    /**
     * 获取用户加入的组织列表
     * @param organizationUser
     * @param userId 用户id
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping("getOrganizationUserList")
    public AjaxJson getOrganizationList(OrganizationUser organizationUser,String userId,
    		@RequestParam(value = "pageNo",required = false,defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize",required = false,defaultValue = "10") Integer pageSize){
        AjaxJson ajaxJson = new AjaxJson();
        try {
        	if(StringUtils.isEmpty(userId)) {
        		ajaxJson.setMsg("userId不得为空！");
                ajaxJson.setSuccess(false);
        	}else {
        		 organizationUser.setUserId(userId);
        		 Page<OrganizationUser> page = new Page<OrganizationUser>(pageNo,pageSize);
                 Page<OrganizationUser> organizationUserPage = organizationUserService.findPage(page,organizationUser);
                 ajaxJson.setSuccess(true);
                 ajaxJson.setMsg("success");
                 ajaxJson.put("list",organizationUserPage);
        	}
        	
        }catch (Exception e){
           logger.error("getOrganizationList异常",e);
            ajaxJson.setMsg("error");
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }
    
   
    /**
     * 加入组织
     * @param organizationUser 
     * @param userId 加入人id
     * @param organizationId 组织id
     * @return
     */
   @RequestMapping("/organizationCheckIn")
   public AjaxJson organizationCheckIn(OrganizationUser organizationUser,String userId,String organizationId){
       AjaxJson ajaxJson = new AjaxJson();
       try {
           if(StringUtils.isEmpty(userId)){
        	   ajaxJson.setSuccess(false);
               ajaxJson.setMsg("加入人不得为空");
               return ajaxJson;
           }
           if(StringUtils.isEmpty(organizationId)){
        	   ajaxJson.setSuccess(false);
               ajaxJson.setMsg("组织不得为空");
               return ajaxJson;
           }
           Integer count  = organizationUserService.getCount(userId, organizationId);
           if(count >= 1) {
        	   ajaxJson.setSuccess(false);
               ajaxJson.setMsg("您已加入该组织");
           }else {
        	   organizationUser.setUserId(userId);
        	   organizationUser.setOrganizationId(organizationId);
        	   organizationUserService.save(organizationUser);
        	   ajaxJson.setSuccess(true);
               ajaxJson.setMsg("加入成功");
           }
       }catch (Exception e){
           logger.error("-------------------------异常",e);
           ajaxJson.setSuccess(false);
           ajaxJson.setMsg("加入失败");
       }
       return ajaxJson;
   }

   @RequestMapping(value = "/applicantOrganization",method = RequestMethod.POST)
   @ResponseBody
   public AjaxJson applicantOrganization(String jsonObject){
       AjaxJson ajaxJson = new AjaxJson();
       try {
           if(jsonObject == null){
               ajaxJson.setSuccess(false);
               ajaxJson.setMsg("申请失败");
               return ajaxJson;
           }
           jsonObject = jsonObject.replaceAll("&quot;", "\"");
           JSONObject json = JSONObject.parseObject(jsonObject);
           if (json == null){
               ajaxJson.setSuccess(false);
               ajaxJson.setMsg("申请失败");
               return ajaxJson;
           }
           OrganizationApplicant applicant = JSON.toJavaObject(json,OrganizationApplicant.class);
           applicant.setName(json.getString("name"));
           applicant.setSex(json.getString("sex"));
           applicant.setNation(json.getString("nation"));
           applicant.setBirthday(json.getString("birthday"));
           applicant.setEducation(json.getString("education"));
           applicant.setNative_place(json.getString("native_place"));
           applicant.setPolitical_outlook(json.getString("political_outlook"));
           applicant.setIdentity(json.getString("identity"));
           applicant.setEnterprise_name(json.getString("enterprise_name"));
           applicant.setBusiness_no(json.getString("business_no"));
           applicant.setPost(json.getString("post"));
           applicant.setTenure(json.getString("tenure"));
           applicant.setPost_type(json.getString("post_type"));
           applicant.setAddress(json.getString("address"));
           applicant.setPostal_code(json.getString("postal_code"));
           applicant.setPersonal_phone(json.getString("personal_phone"));
           applicant.setPersonal_telephone(json.getString("personal_telephone"));
           applicant.setFax(json.getString("fax"));
           applicant.setEmail(json.getString("email"));
           applicant.setWechat(json.getString("wechat"));
           applicant.setEnterprise_daily_contact(json.getString("enterprise_daily_contact"));
           applicant.setEnterprise_phone(json.getString("enterprise_phone"));
           applicant.setEnterprise_telephone(json.getString("enterprise_telephone"));
           applicant.setEnterprise_type(json.getString("enterprise_type"));
           applicant.setEnterprise_industry(json.getString("enterprise_industry"));
           applicant.setEmployees_number(json.getString("employees_number"));
           applicant.setMain_business(json.getString("main_business"));
           applicant.setIs_build_organization(json.getString("is_build_organization"));
           applicant.setMain_business_income(json.getString("main_business_income"));
           applicant.setEnterprise_output(json.getString("enterprise_output"));
           applicant.setIs_list(json.getString("is_list"));
           applicant.setProfit_margin(json.getString("profit_margin"));
           applicant.setInvestment_proportion(json.getString("investment_proportion"));
           applicant.setIs_mentor(json.getString("is_mentor"));
           applicant.setTax_amount(json.getString("tax_amount"));
           applicant.setCharity_amount_invested(json.getString("charity_amount_invested"));
           applicant.setCurriculum_vitae(json.getString("curriculum_vitae"));
           applicant.setEnterprise_profile(json.getString("enterprise_profile"));
           applicant.setEnterprise_honor(json.getString("enterprise_honor"));
           applicant.setStatus("0");
           applicant.setId(IdGen.uuid());
           if(!classObjectCheck(applicant)){
               ajaxJson.setSuccess(false);
               ajaxJson.setMsg("申请失败,参数不能为空");
               return ajaxJson;
           }
           organizationApplicantService.save(applicant);
           ajaxJson.setSuccess(false);
           ajaxJson.setMsg("申请成功");
       }catch (Exception e){
           logger.error("-------------异常",e);
           ajaxJson.setSuccess(false);
           ajaxJson.setMsg("申请失败");
       }
       return ajaxJson;
   }

   public boolean classObjectCheck(Object o){
       try {
           for(Field field :o.getClass().getDeclaredFields()){
               //私有属性公有化
               field.setAccessible(true);
               Object object = field.get(o);
               if (object == null || "".equals(object)){
                   return false;
               }
           }
       }catch (Exception e){
            e.printStackTrace();
            return false;
       }
       return true;
   }

   @RequestMapping("/getEntrepreneurship")
   @ResponseBody
   public AjaxJson getEntrepreneurship(String userId){
       AjaxJson ajaxJson = new AjaxJson();
       try {
           Entrepreneurship entrepreneurship = entrepreneurshipService.getByWxUserId(userId);
           ajaxJson.put("data",entrepreneurship);
           ajaxJson.setSuccess(true);
           ajaxJson.setMsg("查询成功");
       }catch (Exception e){
           logger.error("-------------异常",e);
           ajaxJson.setSuccess(false);
           ajaxJson.setMsg("查询失败");
       }
       return ajaxJson;
   }

    /**
     *
     * @param jsonObject 基本信息
     * @param declaration 申报表
     * @param candidate 候选人材料
     * @param picture 图片
     * @param video 视频
     * @param passportPhoto 2寸证件照
     * @param workPhotoPositive 工作照正面
     * @param workPhotoReverse 工作照反面
     * @param identityPhotoPositive 身份证正面
     * @param identityPhotoReverse 身份证反面
     * @param businessLicense 营业执照复印件
     * @return
     */
   @RequestMapping(value = "/entrepreneurshipSave",method = RequestMethod.POST)
   @ResponseBody
   public AjaxJson entrepreneurshipSave(String jsonObject,
                                        MultipartFile declaration,
                                        MultipartFile candidate,
                                        MultipartFile picture,
                                        MultipartFile video,
                                        MultipartFile passportPhoto,
                                        MultipartFile workPhotoPositive,
                                        MultipartFile workPhotoReverse,
                                        MultipartFile identityPhotoPositive,
                                        MultipartFile identityPhotoReverse,
                                        MultipartFile businessLicense){
       AjaxJson ajaxJson = new AjaxJson();
       OSS oss = null;
       try {
           if(jsonObject == null){
               ajaxJson.setSuccess(false);
               ajaxJson.setMsg("申请失败");
               return ajaxJson;
           }
           jsonObject = jsonObject.replaceAll("&quot;", "\"");
           JSONObject json = JSONObject.parseObject(jsonObject);
           if (json == null){
               ajaxJson.setSuccess(false);
               ajaxJson.setMsg("申请失败");
               return ajaxJson;
           }
           Entrepreneurship entrepreneurship = JSON.toJavaObject(json,Entrepreneurship.class);
           //校验文件的格式
           if (declaration != null && declaration.getSize() > 0){
               oss = FileUploadController.init();
               entrepreneurship.setDeclaration(entrepreneurshipFileUp(declaration,oss));
           }
           if (candidate != null && candidate.getSize() > 0){
               entrepreneurship.setCandidate(entrepreneurshipFileUp(candidate,oss));
           }
           if (picture != null && picture.getSize() > 0) {
               if (checkEntrepreneurshipFile(picture, "file")) {
                   entrepreneurship.setPicture(entrepreneurshipFileUp(picture, oss));
               } else {
                   ajaxJson.setMsg("照片列图片格式不正确,请上传.gif、.png、.jpg、 .jpeg格式图片");
                   ajaxJson.setSuccess(false);
                   return ajaxJson;
               }
           }

           if (video != null && video.getSize() > 0) {
               if (checkEntrepreneurshipFile(video, "media")) {
                   entrepreneurship.setVideo(entrepreneurshipFileUp(video, oss));
               } else {
                   ajaxJson.setMsg("视频列视频上传的格式不正确,请选择.avi、.mp4格式");
                   ajaxJson.setSuccess(false);
                   return ajaxJson;
               }
           }


           if (passportPhoto != null && passportPhoto.getSize() > 0) {
               if (checkEntrepreneurshipFile(passportPhoto, "file")) {
                   entrepreneurship.setPassportPhoto(entrepreneurshipFileUp(passportPhoto, oss));
               } else {
                   ajaxJson.setMsg("2寸证件照列图片格式不正确,请上传.gif、.png、.jpg、 .jpeg格式图片");
                   ajaxJson.setSuccess(false);
                   return ajaxJson;
               }
           }

           if (workPhotoPositive != null && workPhotoPositive.getSize() > 0) {
               if (checkEntrepreneurshipFile(workPhotoPositive, "file")) {
                   entrepreneurship.setWorkPhotoPositive(entrepreneurshipFileUp(workPhotoPositive, oss));
               } else {
                   ajaxJson.setMsg("工作照正面列图片格式不正确,请上传.gif、.png、.jpg、 .jpeg格式图片");
                   ajaxJson.setSuccess(false);
                   return ajaxJson;
               }
           }

           if (workPhotoReverse != null && workPhotoReverse.getSize() > 0) {
               if (checkEntrepreneurshipFile(workPhotoReverse, "file")) {
                   entrepreneurship.setWorkPhotoReverse(entrepreneurshipFileUp(workPhotoReverse, oss));
               } else {
                   ajaxJson.setMsg("工作照反面列图片格式不正确,请上传.gif、.png、.jpg、 .jpeg格式图片");
                   ajaxJson.setSuccess(false);
                   return ajaxJson;
               }
           }

           if (identityPhotoPositive != null && identityPhotoPositive.getSize() > 0) {
               if (checkEntrepreneurshipFile(identityPhotoPositive, "file")) {
                   entrepreneurship.setIdentityPhotoPositive(entrepreneurshipFileUp(identityPhotoPositive, oss));
               } else {
                   ajaxJson.setMsg("身份证正面列图片格式不正确,请上传.gif、.png、.jpg、 .jpeg格式图片");
                   ajaxJson.setSuccess(false);
                   return ajaxJson;
               }
           }

           if (identityPhotoReverse != null && identityPhotoReverse.getSize() > 0) {
               if (checkEntrepreneurshipFile(identityPhotoReverse, "file")) {
                   entrepreneurship.setIdentityPhotoReverse(entrepreneurshipFileUp(identityPhotoReverse, oss));
               } else {
                   ajaxJson.setMsg("身份证反面列图片格式不正确,请上传.gif、.png、.jpg、 .jpeg格式图片");
                   ajaxJson.setSuccess(false);
                   return ajaxJson;
               }
           }

           if (businessLicense != null && businessLicense.getSize() > 0) {
               if (checkEntrepreneurshipFile(businessLicense, "file")) {
                   entrepreneurship.setBusinessLicense(entrepreneurshipFileUp(businessLicense, oss));
               } else {
                   ajaxJson.setMsg("营业执照复印件列图片格式不正确,请上传.gif、.png、.jpg、 .jpeg格式图片");
                   ajaxJson.setSuccess(false);
                   return ajaxJson;
               }
           }
           entrepreneurshipService.save(entrepreneurship);
       }catch (Exception e){
           logger.error("-------------异常",e);
           ajaxJson.setSuccess(false);
           ajaxJson.setMsg("申请失败");
       }finally {
           if (oss != null){
               oss.shutdown();
           }
       }
       return ajaxJson;
   }

   @ResponseBody
   @RequestMapping("/entrepreneurshipFileUp/{type}")
   public AjaxJson entrepreneurshipFileUp(MultipartFile file,@PathVariable String type) throws IOException {
       AjaxJson ajaxJson = new AjaxJson();
       OSS oss = null;
       if(checkEntrepreneurshipFile(file,type)) {
           try {
               oss = FileUploadController.init();
               //上传图片
               String fileRealPath = "chuang/entrepreneurship/" + System.currentTimeMillis() + FileUploadTool.getFileExtension(file);
               InputStream fileInputStream = file.getInputStream();
               // 上传文件
               oss.putObject("gqt-qql", fileRealPath, fileInputStream);
               ajaxJson.put("url", "http://gqt-qql.oss-cn-hangzhou.aliyuncs.com/" + fileRealPath);
               ajaxJson.setSuccess(true);
           }catch (Exception e){
                e.printStackTrace();
           }finally {
               if (oss != null){
                   oss.shutdown();
               }
           }
       }else{
           ajaxJson.setMsg("上传文件或视频格式不正确");
           ajaxJson.setSuccess(false);
       }
       return ajaxJson;
   }

   public boolean checkEntrepreneurshipFile(MultipartFile file,String type){
       if ("file".equals(type)) {
           return FileUploadTool.checkFileType(FileUploadTool.getFileExtension(file));
       } else if ("media".equals(type)) {
           return FileUploadTool.checkMediaType(FileUploadTool.getFileExtension(file));
       }
       return false;
   }

   public String entrepreneurshipFileUp(MultipartFile file,OSS oss) throws IOException {
       //上传图片
       String fileRealPath = "chuang/entrepreneurship/" + System.currentTimeMillis() + FileUploadTool.getFileExtension(file);
       InputStream fileInputStream = file.getInputStream();
       // 上传文件
       oss.putObject("gqt-qql", fileRealPath,fileInputStream);
       return "http://gqt-qql.oss-cn-hangzhou.aliyuncs.com/" + fileRealPath;
   }


    /**
     * 获取岗位详细信息（扬帆计划）
     * @param postId 岗位id
     * @return AjaxJson
     */
    @RequestMapping("getRecruitInfoListV2")
    public AjaxJson getRecruitInfoListV2(@RequestParam String postId,@RequestParam String userId){
        AjaxJson ajaxJson = new AjaxJson();
        try {
             Map<String,Object> recruitInfo = apiService.getRecruitInfoV2(postId);
             //判断当前用户是否申请过这个岗位
            Integer c = postApplicationDao.getCountByUserIdAndPostId(userId, postId);
            if(c>0){
                ajaxJson.put("applyFlag", true);
            }else{
                ajaxJson.put("applyFlag", false);
            }
            ajaxJson.put("recruitInfo", recruitInfo);
            ajaxJson.setMsg("success");
        }catch (Exception e){
            logger.error("-------------------------异常",ExceptionUtil.printStackTrace(e));
            ajaxJson.setMsg("error");
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @RequestMapping(value = "findRecruitInfoListV2")
    public AjaxJson findRecruitInfoListV2(@RequestParam(required = false,defaultValue = "1") Integer pageNo,@RequestParam(required = false,defaultValue = "5") Integer pageSize,String postType,String postName,String workSpace){
        AjaxJson j = new AjaxJson();
        try {
            Page pageInfo =new Page(pageNo,pageSize);
            List<Map<String, Object>> list = apiDao.getPostInfoListV2(postName,postType,workSpace,pageInfo);
            pageInfo.setList(list);
            j.put("data",pageInfo);
            j.setMsg("SUCCESS");
            j.setErrorCode("200");
        }catch (Exception e){
            j.setErrorCode("500");
            j.setSuccess(false);
            j.setMsg("SYS ERROR{}"+ ExceptionUtil.printStackTrace(e));
        }
        return j;
    }
    
    
    /**
     * 获取政务岗位类别
     * @return AjaxJson
     */
    @RequestMapping("/getPostGroup")
    public AjaxJson getPostGroup(@RequestParam String postType){
        AjaxJson ajaxJson = new AjaxJson();
        try {
        	List<Map<Object, Object>> list = postInfoService.getPostGroup(postType);
            ajaxJson.put("list", list);
            ajaxJson.setMsg("success");
        }catch (Exception e){
           logger.error("getPostGroup异常",e);
            ajaxJson.setMsg("error");
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }
    
    /**
     * 根据政务岗位类别获取列表
     * @param pageNo
     * @param pageSize
     * @param postType
     * @param postName
     * @param postGroup
     * @return
     */
    @RequestMapping(value = "/findRecruitInfoListV3")
    public AjaxJson findRecruitInfoListV3(@RequestParam(required = false,defaultValue = "1") Integer pageNo,@RequestParam(required = false,defaultValue = "5") Integer pageSize,String postType,String postName,String postGroup){
        AjaxJson j = new AjaxJson();
        try {
            Page pageInfo =new Page(pageNo,pageSize);
            postGroup = StringEscapeUtils.unescapeHtml4(postGroup);
            List<Map<String, Object>> list = apiDao.getPostInfoListV3(postName,postType,postGroup,pageInfo);
            pageInfo.setList(list);
            j.put("data",pageInfo);
            j.setMsg("SUCCESS");
            j.setErrorCode("200");
        }catch (Exception e){
        	e.printStackTrace();
            j.setErrorCode("500");
            j.setSuccess(false);
            j.setMsg("SYS ERROR{}"+ ExceptionUtil.printStackTrace(e));
            logger.error("findRecruitInfoListV3异常",e);
        }
        return j;
    }

    @RequestMapping("/mainPage")
    public AjaxJson mainPage(){
        AjaxJson j = new AjaxJson();
        try {
            Map data = apiService.bidata("1");
            List<Map> data2  =apiService.bitable("1");
            j.put("mainData",data);
            j.put("mainTable",data2);
            j.setSuccess(true);
            j.setMsg("SUCCESS");
            j.setErrorCode(HttpStatus.SC_OK+"");
        }catch (Exception e){
            j.setMsg("mainPage ERROR====="+ ExceptionUtil.printStackTrace(e));
            j.setErrorCode(HttpStatus.SC_INTERNAL_SERVER_ERROR+"");
            j.setSuccess(false);
        }
        return j;
    }
}

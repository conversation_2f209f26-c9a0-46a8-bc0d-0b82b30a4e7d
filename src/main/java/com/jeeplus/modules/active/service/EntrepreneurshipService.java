package com.jeeplus.modules.active.service;

import com.jeeplus.common.service.CrudService;
import com.jeeplus.modules.active.dao.EntrepreneurshipDao;
import com.jeeplus.modules.active.entity.Entrepreneurship;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = false)
public class EntrepreneurshipService extends CrudService<EntrepreneurshipDao, Entrepreneurship> {

    public Entrepreneurship getByWxUserId(String wxUserId){
        return dao.getByWxUserId(wxUserId);
    }
}

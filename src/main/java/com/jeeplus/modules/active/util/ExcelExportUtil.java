package com.jeeplus.modules.active.util;

import com.google.common.base.Strings;
import com.jeeplus.modules.customer.entity.CustomerFormElement;
import com.jeeplus.modules.customer.util.CustomerFormElementTypeValue;
import org.apache.poi.POIXMLDocumentPart;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.PictureData;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;

public class ExcelExportUtil {
    //表头
    private String title;
    //各个列的表头
    private String[] heardList;
    //各个列的元素key值
    private String[] heardKey;
    //各个列的元素类型
    private String[] heardType;
    //各个列的元素是否必填
    private String[] reqType;
    //组件为 多/单选择器时,必填属性
    private String[] itemsType;
    //需要填充的数据信息
    private List<Map> data;
    //字体大小
    private int fontSize = 14;
    //行高
    private int rowHeight = 30;
    //列宽
    private int columWidth = 200;
    //工作表
    private String sheetName = "sheet1";

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String[] getHeardList() {
        return heardList;
    }

    public void setHeardList(String[] heardList) {
        this.heardList = heardList;
    }

    public String[] getHeardKey() {
        return heardKey;
    }

    public void setHeardKey(String[] heardKey) {
        this.heardKey = heardKey;
    }

    public List<Map> getData() {
        return data;
    }

    public void setData(List<Map> data) {
        this.data = data;
    }

    public int getFontSize() {
        return fontSize;
    }

    public void setFontSize(int fontSize) {
        this.fontSize = fontSize;
    }

    public int getRowHeight() {
        return rowHeight;
    }

    public void setRowHeight(int rowHeight) {
        this.rowHeight = rowHeight;
    }

    public int getColumWidth() {
        return columWidth;
    }

    public void setColumWidth(int columWidth) {
        this.columWidth = columWidth;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public String[] getHeardType() {
        return heardType;
    }

    public void setHeardType(String[] heardType) {
        this.heardType = heardType;
    }

    public String[] getReqType() {
        return reqType;
    }

    public void setReqType(String[] reqType) {
        this.reqType = reqType;
    }

    /**
     * 开始导出数据信息
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    public byte[] exportExport(HttpServletRequest request, HttpServletResponse response) throws IOException {
        //检查参数配置信息
        checkExportConfig();
        //创建工作簿
        HSSFWorkbook wb = new HSSFWorkbook();
        //创建工作表
        HSSFSheet wbSheet = wb.createSheet(this.sheetName);
        //设置默认行宽
        wbSheet.setDefaultColumnWidth(20);
        // 标题样式（加粗，垂直居中）
        HSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中
        HSSFFont fontStyle = wb.createFont();
        fontStyle.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        fontStyle.setFontHeightInPoints((short)16);  //设置标题字体大小
        cellStyle.setFont(fontStyle);
        //在第0行创建rows  (表标题)
        HSSFRow title = wbSheet.createRow((int) 0);
        title.setHeightInPoints(30);//行高
        HSSFCell cellValue = title.createCell(0);
        cellValue.setCellValue(this.title);
        cellValue.setCellStyle(cellStyle);
        //单元格合并
        wbSheet.addMergedRegion(new CellRangeAddress(0,0,0,(this.heardList.length-1)));
        //设置表头样式，表头居中
        HSSFCellStyle style = wb.createCellStyle();
        //设置单元格样式
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        //设置字体
        HSSFFont font = wb.createFont();
        font.setFontHeightInPoints((short) this.fontSize);
        style.setFont(font);
        /**
         * 提前创建style
         */
        HSSFCellStyle linkEHSSFCellStyle = getLinkEHSSFCellStyle(wb);


        //在第1行创建rows
        HSSFRow row = wbSheet.createRow((int) 1);
        //设置列头元素
        HSSFCell cellHead = null;
        for (int i = 0; i < heardList.length; i++) {
            cellHead = row.createCell(i);
            cellHead.setCellValue(heardList[i]);
            cellHead.setCellStyle(style);
        }

        //开始写入实体数据信息
        int a = 2;
        for (int i = 0; i < data.size(); i++) {
            //用于比较第一次的是否相同
            boolean mergedRegion = true;
            HSSFRow roww = wbSheet.createRow((int) a);
            wbSheet.autoSizeColumn(a);
            Map map = data.get(i);
            HSSFCell cell = null;
            for (int j = 0; j < heardKey.length; j++) {
                Object valueObject = map.get(heardKey[j]);
                String value = null;
                if (valueObject == null) {
                    valueObject = "";
                }
                if (valueObject instanceof String) {
                    //取出的数据是字符串直接赋值
                    value = (String) map.get(heardKey[j]);
                } else if (valueObject instanceof Integer) {
                    //取出的数据是Integer
                    value = String.valueOf(((Integer) (valueObject)).floatValue());
                } else if(valueObject instanceof BigDecimal){
                    //取出的数据是BigDecimal
                    value = String.valueOf(((BigDecimal) (valueObject)).floatValue());
                } else {
                    value = valueObject.toString();
                }
                value = Strings.isNullOrEmpty(value) ? "" : value;

                cell = roww.createCell(j);
                cell.setCellStyle(style);
                //设置单个单元格的字体颜色
                if ("uploaderImg".equals(heardType[j]) || "uploaderFile".equals(heardType[j])) {
                    cell.setCellStyle(linkEHSSFCellStyle);
                    wbSheet.setColumnWidth(a, heardKey[j].getBytes().length * 2 * 256);
                    cell.setCellValue(value);
                } else {
                    cell.setCellValue(value);
                }

                if (a > 2){
                    if (mergedRegion && !"uploaderImg".equals(heardKey[j]) ) {
                        //判断和上一个单元格值是否相同,相同合并
                        //1.获取上一个row
                        HSSFRow PreviousRow = wbSheet.getRow(a - 1);
                        //2.获取上一个cell
                        HSSFCell PreviousCel = PreviousRow.getCell(j);
                        //3.比较值是否相同
                        if (value.equals(PreviousCel.getStringCellValue())) {
                            wbSheet.addMergedRegion(new CellRangeAddress(a - 1, a, j, j));
                        }else{
                            if (j == 0) {
                                mergedRegion = false;
                            }
                        }
                    }
                }
            }
            a++;
        }
        //导出数据
        try {
            //设置Http响应头告诉浏览器下载这个附件
            response.setHeader("Content-Disposition", "attachment;Filename=" + System.currentTimeMillis() + ".xls");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.close();
            return wb.getBytes();
        }catch (Exception ex){
            ex.printStackTrace();
            throw new IOException("导出Excel出现严重异常，异常信息：" + ex.getMessage());
        }
    }

    /**
     * 下载导出模板文件
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    public byte[] downloadFileTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        //检查参数配置信息
        checkImportConfig();
        //创建工作簿
        HSSFWorkbook wb = new HSSFWorkbook();
        //创建工作表
        HSSFSheet wbSheet = wb.createSheet(this.sheetName);
        //设置默认行宽
        wbSheet.setDefaultColumnWidth(20);
        // 标题样式（加粗，垂直居中）
        HSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中
        HSSFFont fontStyle = wb.createFont();
        fontStyle.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        fontStyle.setFontHeightInPoints((short)16);  //设置标题字体大小
        cellStyle.setFont(fontStyle);

        //设置表头样式，表头居中
        HSSFCellStyle style = wb.createCellStyle();
        //设置单元格样式
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        //设置字体
        HSSFFont font = wb.createFont();
        font.setFontHeightInPoints((short) this.fontSize);
        style.setFont(font);
        //设置列头元素
        HSSFCell cellHead = null;
        //在第1行创建rows
        HSSFRow row = wbSheet.createRow((int) 0);
        for (int i = 0; i < heardList.length; i++) {
            cellHead = row.createCell(i);
            cellHead.setCellValue(heardList[i]+"|"+ getValueByKey(reqType[i]));
            if ("singleSelect".equals(heardType[i])){
                List<String> list = new ArrayList<>(Arrays.asList(itemsType[i].split(",")));
                list.remove("请选择");
                String[] str = new String[list.size()];
                for (int m = 0; m < list.size(); m++) {
                        str[m] = list.get(m);
                }
                /// 设置下拉列表
                CellRangeAddressList regions = new CellRangeAddressList(4, 9999, i, i);
                // 创建下拉列表数据
                DVConstraint constraint = DVConstraint.createExplicitListConstraint(str);
                // 绑定
                HSSFDataValidation dataValidation = new HSSFDataValidation(regions, constraint);
                wbSheet.addValidationData(dataValidation);
            }
            if ("multipleSelect".equals(heardType[i])){
                String[] strings = itemsType[i].split(",");
                List<String> list = new ArrayList<>(Arrays.asList(strings));
                for (int j = 0; j < strings.length; j++) {
                    for (int k = 0; k < j; k++) {
                        list.add(strings[k]+"、"+strings[j]);
                    }
                }
                String[] str = new String[list.size()];
                for (int m = 0; m < list.size(); m++) {
                    str[m] = list.get(m);
                }
                // 设置下拉列表
                CellRangeAddressList regions = new CellRangeAddressList(4, 9999, i, i);
                // 创建下拉列表数据
                DVConstraint constraint = DVConstraint.createExplicitListConstraint(str);
                //   绑定
                HSSFDataValidation dataValidation = new HSSFDataValidation(regions, constraint);
                wbSheet.addValidationData(dataValidation);
            }
            if ("1".equals(reqType[i]))
                cellHead.setCellStyle(getRedHSSFCellStyle(wb));
            else
                cellHead.setCellStyle(style);
        }
        //在第二行创建rows
        HSSFRow row1 = wbSheet.createRow((int) 1);
        //设置列头元素
        for (int i = 0; i < heardType.length; i++) {
            cellHead = row1.createCell(i);
            cellHead.setCellValue(heardType[i]);
            cellHead.setCellStyle(getBLUEHSSFCellStyle(wb));
        }
        //创建第三行
        HSSFRow row2 = wbSheet.createRow((int) 2);
        //设置列头元素
        for (int i = 0; i < reqType.length; i++) {
            cellHead = row2.createCell(i);
            cellHead.setCellValue(reqType[i]);

        }
        //创建第四行
        HSSFRow row3 = wbSheet.createRow((int) 3);
        for (int i = 0; i < itemsType.length; i++) {
            cellHead = row3.createCell(i);
            cellHead.setCellValue(itemsType[i]);
        }
        //设置隐藏行
        row1.setZeroHeight(true);
        row2.setZeroHeight(true);
        row3.setZeroHeight(true);
        try {
            //设置Http响应头告诉浏览器下载这个附件
            response.setHeader("Content-Disposition", "attachment;Filename=" + System.currentTimeMillis() + ".xls");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.close();
            return wb.getBytes();
        }catch (Exception ex){
            ex.printStackTrace();
            throw new IOException("导出Excel出现严重异常，异常信息：" + ex.getMessage());
        }
    }

    /**
     * 检查数据配置问题
     * @throws IOException 抛出数据异常类
     */
    protected void checkConfig() throws IOException {
        if (heardKey == null || heardList.length == 0) {
            throw new IOException("列名数组不能为空或者为NULL");
        }
        if (fontSize < 0 || rowHeight < 0 || columWidth < 0) {
            throw new IOException("字体、宽度或者高度不能为负值");
        }
        if (Strings.isNullOrEmpty(sheetName)) {
            throw new IOException("工作表表名不能为NULL");
        }
    }

    /**
     * 检查数据配置问题
     * @throws IOException 抛出数据异常类
     */
    protected void checkExportConfig() throws IOException {
        if (heardKey == null || heardList.length == 0) {
            throw new IOException("列名数组不能为空或者为NULL");
        }
        if (fontSize < 0 || rowHeight < 0 || columWidth < 0) {
            throw new IOException("字体、宽度或者高度不能为负值");
        }
        if (Strings.isNullOrEmpty(sheetName)) {
            throw new IOException("工作表表名不能为NULL");
        }
    }

    /**
     * 检查数据配置问题
     * @throws IOException 抛出数据异常类
     */
    protected void checkImportConfig() throws IOException {
        if (heardKey == null || heardList.length == 0) {
            throw new IOException("列名数组不能为空或者为NULL");
        }
        if (fontSize < 0 || rowHeight < 0 || columWidth < 0) {
            throw new IOException("字体、宽度或者高度不能为负值");
        }
        if (Strings.isNullOrEmpty(sheetName)) {
            throw new IOException("工作表表名不能为NULL");
        }
        if (reqType == null || reqType.length == 0){
            throw new IOException("列名数组不能为空或者为NULL");
        }
    }

    //设置每格数据的样式 （字体红色）
    public HSSFCellStyle getRedHSSFCellStyle(HSSFWorkbook wb){
        HSSFCellStyle cellParamStyle = wb.createCellStyle();
        HSSFFont ParamFontStyle = wb.createFont();
        cellParamStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellParamStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        ParamFontStyle.setColor(HSSFColor.DARK_RED.index);   //设置字体颜色 (红色)
        ParamFontStyle.setFontHeightInPoints((short) this.fontSize);
        cellParamStyle.setFont(ParamFontStyle);
        return cellParamStyle;
    }

    //设置每格数据的样式2（字体蓝色）
    public HSSFCellStyle getBLUEHSSFCellStyle(HSSFWorkbook wb){
        HSSFCellStyle cellParamStyle = wb.createCellStyle();
        cellParamStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellParamStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        HSSFFont ParamFontStyle = wb.createFont();
        ParamFontStyle.setColor(HSSFColor.BLUE.index);   //设置字体颜色 (蓝色)
        ParamFontStyle.setFontHeightInPoints((short) this.fontSize);
        cellParamStyle.setFont(ParamFontStyle);
        return cellParamStyle;
    }

    //设置超链接数据的样式2（字体蓝色）
    public HSSFCellStyle getLinkEHSSFCellStyle(HSSFWorkbook wb){
        HSSFCellStyle linkStyle = wb.createCellStyle();
        linkStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        linkStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        HSSFFont linkFontStyle= wb.createFont();
        linkFontStyle.setColor(HSSFColor.BLUE.index);
        linkFontStyle.setUnderline((byte) 1);
        linkFontStyle.setFontHeightInPoints((short) this.fontSize);
        linkStyle.setFont(linkFontStyle);
        return linkStyle;
    }

    /**
     * 将单元格设置为下拉选项
     * @param col 第几列
     * @param strings 下拉值
     * @return
     */
    public HSSFDataValidation settingsDropDown(int col,String[] strings){
        List<String> list = new ArrayList<>(Arrays.asList(strings));
        for (int j = 0; j < strings.length; j++) {
            for (int k = 0; k < j; k++) {
                list.add(strings[k]+"、"+strings[j]);
            }
        }
        String[] str = new String[list.size()];
        for (int m = 0; m < list.size(); m++) {
            str[m] = list.get(m);
        }
        // 设置下拉列表
        CellRangeAddressList regions = new CellRangeAddressList(4, 9999, col, col);
        // 创建下拉列表数据
        DVConstraint constraint = DVConstraint.createExplicitListConstraint(str);
        // 绑定
        HSSFDataValidation dataValidation = new HSSFDataValidation(regions, constraint);
        return dataValidation;
    }

    // 获取文本
    public String getValueByKey(String key){
        switch (key){
            case "inputText":
                return CustomerFormElementTypeValue.INPUTTEXT.getValue();
            case "textareaText":
                return CustomerFormElementTypeValue.TEXTAREATEXT.getValue();
            case "numberText":
                return CustomerFormElementTypeValue.NUMBERTEXT.getValue();
            case "singleSelect":
                return CustomerFormElementTypeValue.SINGLESELECT.getValue();
            case "multipleSelect":
                return CustomerFormElementTypeValue.MULTIPLESELECT.getValue();
            case "selectDate":
                return CustomerFormElementTypeValue.SELECTDATE.getValue();
            case "dateRange":
                return CustomerFormElementTypeValue.DATERANGE.getValue();
            case "uploaderImg":
                return CustomerFormElementTypeValue.UPLOADERIMG.getValue();
            case "tips":
                return CustomerFormElementTypeValue.TIPS.getValue();
            case "moneyInput":
                return CustomerFormElementTypeValue.MONEYINPUT.getValue();
            case "uploaderFile":
                return CustomerFormElementTypeValue.UPLOADERFILE.getValue();
            case "1":
                return CustomerFormElementTypeValue.YES.getValue();
            case "2":
                return CustomerFormElementTypeValue.NO.getValue();
            default:
        }       return "";
    }

    public String[] getItemsType() {
        return itemsType;
    }

    public void setItemsType(String[] itemsType) {
        this.itemsType = itemsType;
    }

    /**
     * 获取图片和位置 (xls)
     * @param sheet
     * @return
     * @throws IOException
     */
    public static Map<String, PictureData> getPictures (HSSFSheet sheet) throws IOException {
        Map<String, PictureData> map = new HashMap<String, PictureData>();
        List<HSSFShape> list = sheet.getDrawingPatriarch().getChildren();
        for (HSSFShape shape : list) {
            if (shape instanceof HSSFPicture) {
                HSSFPicture picture = (HSSFPicture) shape;
                HSSFClientAnchor cAnchor = (HSSFClientAnchor) picture.getAnchor();
                PictureData pdata = picture.getPictureData();
                String key = cAnchor.getRow1() + "-" + cAnchor.getCol1(); // 行号-列号
                map.put(key, pdata);
            }
        }
        return map;
    }

    /**
     * 获取图片和位置 (xlsx)
     * @param sheet 当前sheet对象
     * @return Map key:图片单元格索引（0_1_1）String，value:图片流PictureData
     */
    public static Map<String, PictureData> getPictures(XSSFSheet sheet) {
        Map<String, PictureData> map = new HashMap<String, PictureData>();
        for (POIXMLDocumentPart dr : sheet.getRelations()) {
            if (dr instanceof XSSFDrawing) {
                XSSFDrawing drawing = (XSSFDrawing) dr;
                List<XSSFShape> shapes = drawing.getShapes();
                for (XSSFShape shape : shapes) {
                    XSSFPicture pic = (XSSFPicture) shape;
                    XSSFClientAnchor anchor = pic.getPreferredSize();
                    String key = anchor.getRow1() + "-" + anchor.getCol1(); // 行号-列号
                    map.put(key, pic.getPictureData());
                }
            }
        }
        return map;
    }
}
